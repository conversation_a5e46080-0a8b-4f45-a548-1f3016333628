<?php

namespace common\models;

use Yii;
use yii\base\Model;

/**
 * Login form
 */
class LoginForm extends Model
{
    public $email;
    public $password;
    public $rememberMe = true;

    private $_user;


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            // username and password are both required
            [['email', 'password'], 'required'],
            // rememberMe must be a boolean value
            ['rememberMe', 'boolean'],
            // password is validated by validatePassword()
            ['password', 'validatePassword'],
            ['email', 'validateBusiness'],
        ];
    }

    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array $params the additional name-value pairs given in the rule
     */
    public function validatePassword($attribute, $params)
    {
        if (!$this->hasErrors()) {
            $user = $this->getUser();
            if (!$user || !$user->validatePassword($this->password)) {
                $this->addError($attribute, 'Incorrect username or password.');
            }
        }
    }

    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array $params the additional name-value pairs given in the rule
     */
    public function validateBusiness($attribute, $params)
    {
        if (!$this->hasErrors()) {
            $user = $this->getUser();
            $domain = Yii::$app->request->getHostName();
            if ($user && in_array($domain, DEV_SERVER_DOMAINS)) {
                $business = $user->business;
                // Set the business ID in the session variable
                Yii::$app->session->set('businessId', $business->id);
                return true;
            }
            $businessId = Yii::$app->session->get("businessId");
            $superAdmins = env('superAdmins');
            if ($user->business->id !== $businessId && !in_array($this->email, $superAdmins)) {
                $this->addError($attribute, 'You are not authorized to access this portal!');
            }
        }
    }

    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array $params the additional name-value pairs given in the rule
     */
    public function validateRole($attribute, $params)
    {
        if (!$this->hasErrors()) {
            $superAdmins = env('superAdmins');
            if (!in_array($this->email, $superAdmins)) {
                $this->addError($attribute, "Sorry you don't have access rights!");
            }
        }
    }

    /**
     * Finds user by [[username]]
     *
     * @return User|null
     */
    protected function getUser()
    {
        if ($this->_user === null) {
            $this->_user = User::findByEmail($this->email);
        }
        return $this->_user;
    }

    /**
     * Logs in a user using the provided username and password.
     *
     * @return bool whether the user is logged in successfully
     */
    public function login()
    {
        if ($this->validate()) {
            return Yii::$app->user->login($this->getUser(), $this->rememberMe ? 3600 * 24 * 30 : 0);
        }
        Yii::$app->session->setFlash('error', $this->getErrorSummary(true)[0]);
        return false;
    }
}
