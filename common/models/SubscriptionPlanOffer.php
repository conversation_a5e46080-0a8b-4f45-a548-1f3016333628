<?php

namespace common\models;

use Carbon\Carbon;
use common\helpers\StripeHelper;
use common\models\base\SubscriptionPlanOfferBase;
use common\models\enum\PlanDuration;
use common\models\enum\PlanType;
use yii\base\Exception;

class SubscriptionPlanOffer extends SubscriptionPlanOfferBase
{
    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * Finds Model by offerCode
     *
     * @param string $code
     * @return static|null
     */
    public static function findByCode($offerCode)
    {
        return static::findOne(['offerCode' => $offerCode, 'isActive' => 1]);
    }

    /**
     * Finds Model by offerCode
     *
     * @param string $code
     * @return static|null
     */
    public static function findByCodeAndPlan($offerCode, $planId)
    {
        return static::findOne(['offerCode' => $offerCode, 'planId' => $planId, 'isActive' => 1]);
    }


    public static function validateOffer(string $offerCode, int $planId, User $user)
    {
        $offer = self::findByCodeAndPlan($offerCode, $planId);
        if ($offer === null) {
            throw new Exception("Sorry, you have entered invalid offer code.");
        }
        if (!$offer->isActive) {
            throw new Exception("We apologize, but this offer has expired.");
        }
//        $redeemedOffer = OfferRedemption::findByOfferAndUser($offer->id, $user->id);
//        if ($redeemedOffer) {
//            throw new Exception("We’re sorry, but you have already redeemed this offer previously and it is no longer available to you.");
//        }
        return $offer;
    }

    public static function validateAndRedeem(string $offerCode, int $planId, User $user)
    {
        try {
            $offer = self::validateOffer($offerCode, $planId, $user);
        } catch (Exception | \Exception $e) {
            throw $e ?? new Exception("We apologize, but an unexpected error has occurred. Please try again later.");
        }
        $plan = $offer->plan;
        $business = $user->business;
        $subscription = $business->getActiveSubscription(); // check whether any active subscription!
        if ($plan->type === PlanType::BASE && $subscription && $plan->isRecurring) {
            throw new Exception("Sorry, you cannot redeem this offer at the moment as you already have an active subscription.");
        }

        $isSubscribed = $business->isSubscribedToPlan($planId);
        if ($isSubscribed) {
            throw new Exception("You have already subscribed to this plan!");
        }

        $currentTimestamp = Carbon::now()->timestamp;

        $offerRedeem = new OfferRedemption();
        $offerRedeem->offerId = $offer->id;
        $offerRedeem->userId = $user->id;
        $offerRedeem->save();
        $offerRedeem->refresh();
        StripeHelper::removePendingIntents($user->id); // remove previous payment intents
        $subscription = new Subscription();
        $subscription->startedFromDate = $currentTimestamp; // current timestamp
        $subscription->userId = $user->id;
        $subscription->businessId = $business->id;
        $subscription->provider = 'direct';
        $subscription->providerPlanId = $plan->slug;
        $subscription->providerSubscriptionId = "offer-" . $offerRedeem->id;

        if ($offerCode === "manual@subscription") {
            $subscription->providerSubscriptionId = "direct-" . $offerRedeem->id;
            $subscription->provider = 'direct';
        }

        $subscription->startDate = $currentTimestamp; // ms to seconds
        $subscription->lastTransactionId = "offer-" . $offerRedeem->id;
        $subscription->planId = $plan->id;

        $subscription->endDate = $currentTimestamp + $plan->interval * PlanDuration::getInSeconds($plan->period); // 1 x weekly duration in seconds
        $subscription->isActive = 1;
        $subscription->status = 'active';

        if (!$subscription->save()) {
            $offerRedeem->delete();
            throw new Exception($subscription->getErrorSummary(true)[0]);
        } // Save subscription
        $subscription->refresh();
        return $subscription;
    }

}