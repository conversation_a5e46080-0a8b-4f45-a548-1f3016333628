<?php

namespace common\models;

use common\models\base\InvoiceItemsBase;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

class InvoiceItems extends InvoiceItemsBase
{
    /**
     * @return ActiveQuery
     */
    public static function findActive()
    {
        return parent::find()->andWhere(['isDeleted' => 0]);
    }

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    public static function findByInvoice($invoiceId)
    {
        return static::findAll(['invoiceId' => $invoiceId]);
    }

    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    public function rules()
    {
        return ArrayHelper::merge(parent::rules(),
            [
                [['discountAmount', 'taxAmount'], 'default', 'value' => 0],
                ['discountPercentage', 'number', 'min' => 0, 'max' => 100],
            ]);
    }

    public function beforeSoftDelete()
    {
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        return true;
    }

}