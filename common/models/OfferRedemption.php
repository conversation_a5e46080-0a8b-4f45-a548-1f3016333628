<?php

namespace common\models;

use common\models\base\OfferRedemptionBase;

class OfferRedemption extends OfferRedemptionBase
{
    /**
     * Finds Model by id
     *
     * @param integer $offerId
     * @param integer $userId
     * @return static|null
     */
    public static function findByOfferAndUser($offerId, $userId)
    {
        return static::find()->byOfferIdAndUserId($offerId, $userId)->one();
    }


}