<?php

namespace common\models\base;

use Yii;
use common\models\Business;

/**
 * This is the model class for table "business_stats".
*
    * @property integer $businessId
    * @property integer $totalUsageCount
    * @property integer $quotationCount
    * @property integer $invoiceCount
    * @property integer $poCount
    * @property integer $piCount
    * @property integer $dnCount
    * @property integer $receiptCount
    * @property integer $budgetCount
    * @property integer $userCount
    * @property integer $productCategoryCount
    * @property integer $productCount
    * @property integer $productImageCount
    * @property integer $totalFreeUsageCount
    * @property integer $quotationFreeUsageLimit
    * @property integer $invoiceFreeUsageLimit
    * @property integer $poFreeUsageLimit
    * @property integer $piFreeUsageLimit
    * @property integer $dnFreeUsageLimit
    * @property integer $receiptFreeUsageLimit
    * @property integer $budgetFreeUsageLimit
    * @property string $createdAt
    * @property string $updatedAt
    *
            * @property Business $business
    */
class BusinessStatsBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'business_stats';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['totalUsageCount', 'quotationCount', 'invoiceCount', 'poCount', 'piCount', 'dnCount', 'receiptCount', 'budgetCount', 'userCount', 'productCategoryCount', 'productCount', 'productImageCount', 'totalFreeUsageCount', 'quotationFreeUsageLimit', 'invoiceFreeUsageLimit', 'poFreeUsageLimit', 'piFreeUsageLimit', 'dnFreeUsageLimit', 'receiptFreeUsageLimit', 'budgetFreeUsageLimit'], 'integer'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['businessId'], 'exist', 'skipOnError' => true, 'targetClass' => Business::className(), 'targetAttribute' => ['businessId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'businessId' => 'Business ID',
    'totalUsageCount' => 'Total Usage Count',
    'quotationCount' => 'Quotation Count',
    'invoiceCount' => 'Invoice Count',
    'poCount' => 'Po Count',
    'piCount' => 'Pi Count',
    'dnCount' => 'Dn Count',
    'receiptCount' => 'Receipt Count',
    'budgetCount' => 'Budget Count',
    'userCount' => 'User Count',
    'productCategoryCount' => 'Product Category Count',
    'productCount' => 'Product Count',
    'productImageCount' => 'Product Image Count',
    'totalFreeUsageCount' => 'Total Free Usage Count',
    'quotationFreeUsageLimit' => 'Quotation Free Usage Limit',
    'invoiceFreeUsageLimit' => 'Invoice Free Usage Limit',
    'poFreeUsageLimit' => 'Po Free Usage Limit',
    'piFreeUsageLimit' => 'Pi Free Usage Limit',
    'dnFreeUsageLimit' => 'Dn Free Usage Limit',
    'receiptFreeUsageLimit' => 'Receipt Free Usage Limit',
    'budgetFreeUsageLimit' => 'Budget Free Usage Limit',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusiness()
    {
    return $this->hasOne(Business::className(), ['id' => 'businessId']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\BusinessStatsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\BusinessStatsQuery(get_called_class());
}
}