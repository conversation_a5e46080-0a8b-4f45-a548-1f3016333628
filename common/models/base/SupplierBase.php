<?php

namespace common\models\base;

use Yii;
use common\models\InventoryTransaction;
use common\models\User;
use common\models\Business;

/**
 * This is the model class for table "supplier".
*
    * @property integer $id
    * @property integer $businessId
    * @property string $email
    * @property string $companyName
    * @property string $name
    * @property string $phoneNumber
    * @property string $addressLine1
    * @property string $addressLine2
    * @property string $addressLine3
    * @property string $stateCode
    * @property string $profilePic
    * @property string $taxNumber
    * @property string $panNumber
    * @property integer $addedById
    * @property string $otherInfo
    * @property integer $isDeleted
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $deletedAt
    *
            * @property InventoryTransaction[] $inventoryTransactions
            * @property User $addedBy
            * @property Business $business
    */
class SupplierBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'supplier';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['businessId', 'addedById'], 'required'],
            [['businessId', 'addedById', 'isDeleted'], 'integer'],
            [['createdAt', 'updatedAt', 'deletedAt'], 'safe'],
            [['email', 'companyName'], 'string', 'max' => 191],
            [['name', 'addressLine1', 'addressLine2', 'addressLine3', 'profilePic'], 'string', 'max' => 255],
            [['phoneNumber', 'stateCode'], 'string', 'max' => 50],
            [['taxNumber', 'panNumber'], 'string', 'max' => 20],
            [['otherInfo'], 'string', 'max' => 350],
            [['addedById'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['addedById' => 'id']],
            [['businessId'], 'exist', 'skipOnError' => true, 'targetClass' => Business::className(), 'targetAttribute' => ['businessId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'businessId' => 'Business ID',
    'email' => 'Email',
    'companyName' => 'Company Name',
    'name' => 'Name',
    'phoneNumber' => 'Phone Number',
    'addressLine1' => 'Address Line1',
    'addressLine2' => 'Address Line2',
    'addressLine3' => 'Address Line3',
    'stateCode' => 'State Code',
    'profilePic' => 'Profile Pic',
    'taxNumber' => 'Tax Number',
    'panNumber' => 'Pan Number',
    'addedById' => 'Added By ID',
    'otherInfo' => 'Other Info',
    'isDeleted' => 'Is Deleted',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'deletedAt' => 'Deleted At',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInventoryTransactions()
    {
    return $this->hasMany(InventoryTransaction::className(), ['supplierId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getAddedBy()
    {
    return $this->hasOne(User::className(), ['id' => 'addedById']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusiness()
    {
    return $this->hasOne(Business::className(), ['id' => 'businessId']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\SupplierQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SupplierQuery(get_called_class());
}
}