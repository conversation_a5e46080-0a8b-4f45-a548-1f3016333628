<?php

namespace common\models\base;

use Yii;
use common\models\Business;
use common\models\Customer;
use common\models\Inquiry;
use common\models\Invoice;
use common\models\NotificationMessage;
use common\models\OfferRedemption;
use common\models\Quotation;
use common\models\RazorpayUser;
use common\models\StripeUser;
use common\models\Subscription;
use common\models\User;
use common\models\SubscriptionPlan;
use common\models\Region;
use common\models\UserBusiness;
use common\models\UserDevice;
use common\models\UserSocialProvider;
use common\models\UserStripe;

/**
 * This is the model class for table "user".
*
    * @property integer $id
    * @property string $email
    * @property string $fromEmail
    * @property string $name
    * @property string $phoneNumber
    * @property string $addressLine1
    * @property string $addressLine2
    * @property string $addressLine3
    * @property double $latitude
    * @property double $longitude
    * @property string $profilePic
    * @property string $signatureImg
    * @property string $designation
    * @property integer $freeUsageCount
    * @property integer $quotationCount
    * @property integer $invFreeUsageCount
    * @property integer $invoiceCount
    * @property integer $planId
    * @property string $country
    * @property string $regionCode
    * @property string $state
    * @property string $city
    * @property string $pincode
    * @property integer $parentId
    * @property string $accessToken
    * @property integer $otpCode
    * @property string $otpExpiry
    * @property string $auth_key
    * @property string $password_hash
    * @property string $password_reset_token
    * @property string $razorpayCustomerId
    * @property string $stripeCustomerId
    * @property string $status
    * @property string $migratedAt
    * @property integer $isMigrated
    * @property integer $isAdmin
    * @property integer $isPhoneVerified
    * @property integer $isContactsAllowed
    * @property string $fcmTopic
    * @property integer $isDeleted
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $deletedAt
    *
            * @property Business[] $businesses
            * @property Customer[] $customers
            * @property Inquiry[] $inquiries
            * @property Inquiry[] $inquiries0
            * @property Invoice[] $invoices
            * @property Invoice[] $invoices0
            * @property NotificationMessage[] $notificationMessages
            * @property NotificationMessage[] $notificationMessages0
            * @property OfferRedemption[] $offerRedemptions
            * @property Quotation[] $quotations
            * @property Quotation[] $quotations0
            * @property RazorpayUser[] $razorpayUsers
            * @property StripeUser[] $stripeUsers
            * @property Subscription[] $subscriptions
            * @property User $parent
            * @property User[] $users
            * @property SubscriptionPlan $plan
            * @property Region $regionCode0
            * @property UserBusiness[] $userBusinesses
            * @property UserDevice[] $userDevices
            * @property UserSocialProvider[] $userSocialProviders
            * @property UserStripe $userStripe
    */
class UserBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'user';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['email', 'name', 'password_hash'], 'required'],
            [['latitude', 'longitude'], 'number'],
            [['designation', 'status'], 'string'],
            [['freeUsageCount', 'quotationCount', 'invFreeUsageCount', 'invoiceCount', 'planId', 'parentId', 'otpCode', 'isMigrated', 'isAdmin', 'isPhoneVerified', 'isContactsAllowed', 'isDeleted'], 'integer'],
            [['otpExpiry', 'migratedAt', 'createdAt', 'updatedAt', 'deletedAt'], 'safe'],
            [['email', 'fromEmail', 'stripeCustomerId', 'fcmTopic'], 'string', 'max' => 191],
            [['name', 'addressLine1', 'addressLine2', 'addressLine3', 'profilePic', 'signatureImg', 'password_hash', 'razorpayCustomerId'], 'string', 'max' => 255],
            [['phoneNumber'], 'string', 'max' => 50],
            [['country', 'state', 'city'], 'string', 'max' => 30],
            [['regionCode'], 'string', 'max' => 5],
            [['pincode'], 'string', 'max' => 10],
            [['accessToken', 'password_reset_token'], 'string', 'max' => 64],
            [['auth_key'], 'string', 'max' => 32],
            [['email'], 'unique'],
            [['password_reset_token'], 'unique'],
            [['parentId'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['parentId' => 'id']],
            [['planId'], 'exist', 'skipOnError' => true, 'targetClass' => SubscriptionPlan::className(), 'targetAttribute' => ['planId' => 'id']],
            [['regionCode'], 'exist', 'skipOnError' => true, 'targetClass' => Region::className(), 'targetAttribute' => ['regionCode' => 'code']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'email' => 'Email',
    'fromEmail' => 'From Email',
    'name' => 'Name',
    'phoneNumber' => 'Phone Number',
    'addressLine1' => 'Address Line1',
    'addressLine2' => 'Address Line2',
    'addressLine3' => 'Address Line3',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'profilePic' => 'Profile Pic',
    'signatureImg' => 'Signature',
    'designation' => 'Designation',
    'freeUsageCount' => 'Free Usage Count',
    'quotationCount' => 'Quotation Count',
    'invFreeUsageCount' => 'Inv Free Usage Count',
    'invoiceCount' => 'Invoice Count',
    'planId' => 'Plan ID',
    'country' => 'Country',
    'regionCode' => 'Region Code',
    'state' => 'State',
    'city' => 'City',
    'pincode' => 'Pincode',
    'parentId' => 'Parent ID',
    'accessToken' => 'Access Token',
    'otpCode' => 'Otp Code',
    'otpExpiry' => 'Otp Expiry',
    'auth_key' => 'Auth Key',
    'password_hash' => 'Password Hash',
    'password_reset_token' => 'Password Reset Token',
    'razorpayCustomerId' => 'Razorpay Customer ID',
    'stripeCustomerId' => 'Stripe Customer ID',
    'status' => 'Status',
    'migratedAt' => 'Migrated At',
    'isMigrated' => 'Is Migrated',
    'isAdmin' => 'Is Admin',
    'isPhoneVerified' => 'Is Phone Verified',
    'isContactsAllowed' => 'Is Contacts Allowed',
    'fcmTopic' => 'Fcm Topic',
    'isDeleted' => 'Is Deleted',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'deletedAt' => 'Deleted At',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusinesses()
    {
    return $this->hasMany(Business::className(), ['ownerId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getCustomers()
    {
    return $this->hasMany(Customer::className(), ['addedBy' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInquiries()
    {
    return $this->hasMany(Inquiry::className(), ['assignedTo' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInquiries0()
    {
    return $this->hasMany(Inquiry::className(), ['statusUpdatedBy' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInvoices()
    {
    return $this->hasMany(Invoice::className(), ['assignedToId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInvoices0()
    {
    return $this->hasMany(Invoice::className(), ['statusUpdatedById' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getNotificationMessages()
    {
    return $this->hasMany(NotificationMessage::className(), ['fromUserId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getNotificationMessages0()
    {
    return $this->hasMany(NotificationMessage::className(), ['toUserId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getOfferRedemptions()
    {
    return $this->hasMany(OfferRedemption::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getQuotations()
    {
    return $this->hasMany(Quotation::className(), ['assignedToId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getQuotations0()
    {
    return $this->hasMany(Quotation::className(), ['statusUpdatedById' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getRazorpayUsers()
    {
    return $this->hasMany(RazorpayUser::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getStripeUsers()
    {
    return $this->hasMany(StripeUser::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getSubscriptions()
    {
    return $this->hasMany(Subscription::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getParent()
    {
    return $this->hasOne(User::className(), ['id' => 'parentId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUsers()
    {
    return $this->hasMany(User::className(), ['parentId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getPlan()
    {
    return $this->hasOne(SubscriptionPlan::className(), ['id' => 'planId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getRegionCode0()
    {
    return $this->hasOne(Region::className(), ['code' => 'regionCode']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUserBusinesses()
    {
    return $this->hasMany(UserBusiness::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUserDevices()
    {
    return $this->hasMany(UserDevice::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUserSocialProviders()
    {
    return $this->hasMany(UserSocialProvider::className(), ['userId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUserStripe()
    {
    return $this->hasOne(UserStripe::className(), ['userId' => 'id']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\UserQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\UserQuery(get_called_class());
}
}