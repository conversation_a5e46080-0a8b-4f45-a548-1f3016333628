<?php

namespace common\models\base;

use Yii;
use common\models\User;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "region".
*
    * @property string $code
    * @property string $name
    * @property string $locale
    * @property string $dialPrefix
    * @property string $currencyCode
    * @property string $fmtAmount
    * @property string $createdAt
    * @property string $updatedAt
    *
            * @property User[] $users
    */
class RegionBase extends ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'region';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['code', 'currencyCode'], 'required'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['code', 'dialPrefix', 'currencyCode'], 'string', 'max' => 5],
            [['name', 'fmtAmount'], 'string', 'max' => 50],
            [['locale'], 'string', 'max' => 10],
            [['code'], 'unique'],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'code' => 'Code',
    'name' => 'Name',
    'locale' => 'Locale',
    'dialPrefix' => 'Dial Prefix',
    'currencyCode' => 'Currency Code',
    'fmtAmount' => 'Fmt Amount',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
];
}

    /**
    * @return ActiveQuery
    */
    public function getUsers()
    {
    return $this->hasMany(User::className(), ['regionCode' => 'code']);
    }
}