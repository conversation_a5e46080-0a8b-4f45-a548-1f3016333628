<?php

namespace common\models\base;

/**
 * This is the model class for table "subscription_log".
*
    * @property integer $id
    * @property string $provider
    * @property string $providerAccount
    * @property string $providerSubscriptionId
    * @property string $providerPlanId
    * @property string $lastTransactionId
    * @property integer $userId
    * @property integer $planId
    * @property integer $startDate
    * @property integer $endDate
    * @property string $status
    * @property string $event
    * @property string $payloadData
    * @property string $linkedPurchaseToken
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $ipAddress
    * @property string $paymentReceipt
    * @property string $transactionDetails
    * @property double $price
    * @property string $currency
*/
class SubscriptionLogBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'subscription_log';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['provider', 'userId', 'planId', 'startDate', 'endDate'], 'required'],
            [['provider', 'payloadData', 'paymentReceipt', 'transactionDetails'], 'string'],
            [['userId', 'planId', 'startDate', 'endDate'], 'integer'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['price'], 'number'],
            [['providerAccount', 'lastTransactionId', 'status', 'event'], 'string', 'max' => 255],
            [['providerSubscriptionId', 'providerPlanId', 'linkedPurchaseToken'], 'string', 'max' => 191],
            [['ipAddress'], 'string', 'max' => 45],
            [['currency'], 'string', 'max' => 5],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'provider' => 'Provider',
    'providerAccount' => 'Provider Account',
    'providerSubscriptionId' => 'Provider Subscription ID',
    'providerPlanId' => 'Provider Plan ID',
    'lastTransactionId' => 'Last Transaction ID',
    'userId' => 'User ID',
    'planId' => 'Plan ID',
    'startDate' => 'Start Date',
    'endDate' => 'End Date',
    'status' => 'Status',
    'event' => 'Event',
    'payloadData' => 'Payload Data',
    'linkedPurchaseToken' => 'Linked Purchase Token',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'ipAddress' => 'Ip Address',
    'paymentReceipt' => 'Payment Receipt',
    'transactionDetails' => 'Transaction Details',
    'price' => 'Price',
    'currency' => 'Currency',
];
}

    /**
     * @inheritdoc
     * @return \common\models\query\SubscriptionLogQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SubscriptionLogQuery(get_called_class());
}
}