<?php

namespace common\models\base;

use Yii;
use common\models\Attachment;
use common\models\BusinessCategory;
use common\models\Template;
use common\models\SubscriptionPlan;
use common\models\States;
use common\models\User;
use common\models\BusinessSettings;
use common\models\BusinessStats;
use common\models\Customer;
use common\models\InventoryLocation;
use common\models\InventoryTransaction;
use common\models\Invoice;
use common\models\Product;
use common\models\ProductCategory;
use common\models\Quotation;
use common\models\Receipt;
use common\models\Subscription;
use common\models\Supplier;
use common\models\TermsCondition;
use common\models\UserBusiness;

/**
 * This is the model class for table "business".
*
    * @property integer $id
    * @property integer $ownerId
    * @property string $slug
    * @property string $email
    * @property string $name
    * @property integer $planId
    * @property integer $lastPlanId
    * @property integer $isPremium
    * @property string $features
    * @property integer $isMultiuser
    * @property integer $categoryId
    * @property string $otherCategory
    * @property string $tagLine
    * @property string $phoneNumber
    * @property string $addressLine1
    * @property string $addressLine2
    * @property string $addressLine3
    * @property string $stateCode
    * @property string $otherInfo
    * @property string $regionCode
    * @property string $website
    * @property string $image
    * @property string $signatureImg
    * @property string $headerImg
    * @property string $footerImg
    * @property string $taxLabel
    * @property string $taxNumber
    * @property string $panNumber
    * @property string $bankInfo
    * @property string $quotationPrefix
    * @property integer $quotationNumber
    * @property string $piPrefix
    * @property integer $piNumber
    * @property string $poPrefix
    * @property integer $poNumber
    * @property string $contactName
    * @property string $invoicePrefix
    * @property integer $invoiceNumber
    * @property string $dnPrefix
    * @property integer $dnNumber
    * @property string $receiptPrefix
    * @property integer $receiptNumber
    * @property string $budgetPrefix
    * @property integer $budgetNumber
    * @property integer $budgetTemplateId
    * @property integer $dnTemplateId
    * @property integer $quotationTemplateId
    * @property integer $piTemplateId
    * @property integer $poTemplateId
    * @property integer $invoiceTemplateId
    * @property integer $receiptTemplateId
    * @property integer $isDeleted
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $deletedAt
    *
            * @property Attachment[] $attachments
            * @property BusinessCategory $category
            * @property Template $invoiceTemplate
            * @property SubscriptionPlan $lastPlan
            * @property Template $piTemplate
            * @property SubscriptionPlan $plan
            * @property Template $poTemplate
            * @property States $stateCode0
            * @property Template $quotationTemplate
            * @property User $owner
            * @property BusinessSettings[] $businessSettings
            * @property BusinessStats $businessStats
            * @property Customer[] $customers
            * @property InventoryLocation[] $inventoryLocations
            * @property InventoryTransaction[] $inventoryTransactions
            * @property Invoice[] $invoices
            * @property Product[] $products
            * @property ProductCategory[] $productCategories
            * @property Quotation[] $quotations
            * @property Receipt[] $receipts
            * @property Subscription[] $subscriptions
            * @property Supplier[] $suppliers
            * @property TermsCondition[] $termsConditions
            * @property UserBusiness[] $userBusinesses
    */
class BusinessBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'business';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['ownerId', 'name'], 'required'],
            [['ownerId', 'planId', 'lastPlanId', 'isPremium', 'isMultiuser', 'categoryId', 'quotationNumber', 'piNumber', 'poNumber', 'invoiceNumber', 'dnNumber', 'receiptNumber', 'budgetNumber', 'budgetTemplateId', 'dnTemplateId', 'quotationTemplateId', 'piTemplateId', 'poTemplateId', 'invoiceTemplateId', 'receiptTemplateId', 'isDeleted'], 'integer'],
            [['createdAt', 'updatedAt', 'deletedAt'], 'safe'],
            [['slug', 'email'], 'string', 'max' => 191],
            [['name', 'addressLine1', 'addressLine2', 'addressLine3', 'website', 'image', 'signatureImg', 'headerImg', 'footerImg'], 'string', 'max' => 255],
            [['features'], 'string', 'max' => 5000],
            [['otherCategory'], 'string', 'max' => 100],
            [['tagLine'], 'string', 'max' => 160],
            [['phoneNumber', 'taxLabel', 'taxNumber'], 'string', 'max' => 50],
            [['stateCode', 'regionCode'], 'string', 'max' => 5],
            [['otherInfo'], 'string', 'max' => 350],
            [['panNumber'], 'string', 'max' => 20],
            [['bankInfo'], 'string', 'max' => 2000],
            [['quotationPrefix', 'piPrefix', 'poPrefix', 'invoicePrefix', 'dnPrefix', 'receiptPrefix'], 'string', 'max' => 25],
            [['contactName'], 'string', 'max' => 60],
            [['budgetPrefix'], 'string', 'max' => 15],
            [['slug'], 'unique'],
            [['categoryId'], 'exist', 'skipOnError' => true, 'targetClass' => BusinessCategory::className(), 'targetAttribute' => ['categoryId' => 'id']],
            [['invoiceTemplateId'], 'exist', 'skipOnError' => true, 'targetClass' => Template::className(), 'targetAttribute' => ['invoiceTemplateId' => 'id']],
            [['lastPlanId'], 'exist', 'skipOnError' => true, 'targetClass' => SubscriptionPlan::className(), 'targetAttribute' => ['lastPlanId' => 'id']],
            [['piTemplateId'], 'exist', 'skipOnError' => true, 'targetClass' => Template::className(), 'targetAttribute' => ['piTemplateId' => 'id']],
            [['planId'], 'exist', 'skipOnError' => true, 'targetClass' => SubscriptionPlan::className(), 'targetAttribute' => ['planId' => 'id']],
            [['poTemplateId'], 'exist', 'skipOnError' => true, 'targetClass' => Template::className(), 'targetAttribute' => ['poTemplateId' => 'id']],
            [['stateCode'], 'exist', 'skipOnError' => true, 'targetClass' => States::className(), 'targetAttribute' => ['stateCode' => 'code']],
            [['quotationTemplateId'], 'exist', 'skipOnError' => true, 'targetClass' => Template::className(), 'targetAttribute' => ['quotationTemplateId' => 'id']],
            [['ownerId'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['ownerId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'ownerId' => 'Owner ID',
    'slug' => 'Slug',
    'email' => 'Email',
    'name' => 'Name',
    'planId' => 'Plan ID',
    'lastPlanId' => 'Last Plan ID',
    'isPremium' => 'Is Premium',
    'features' => 'Features',
    'isMultiuser' => 'Is Multiuser',
    'categoryId' => 'Category ID',
    'otherCategory' => 'Other Category',
    'tagLine' => 'Tag Line',
    'phoneNumber' => 'Phone Number',
    'addressLine1' => 'Address Line1',
    'addressLine2' => 'Address Line2',
    'addressLine3' => 'Address Line3',
    'stateCode' => 'State Code',
    'otherInfo' => 'Other Info',
    'regionCode' => 'Region Code',
    'website' => 'Website',
    'image' => 'Image',
    'signatureImg' => 'Signature Img',
    'headerImg' => 'Header Img',
    'footerImg' => 'Footer Img',
    'taxLabel' => 'Tax Label',
    'taxNumber' => 'Tax Number',
    'panNumber' => 'Pan Number',
    'bankInfo' => 'Bank Info',
    'quotationPrefix' => 'Quotation Prefix',
    'quotationNumber' => 'Quotation Number',
    'piPrefix' => 'Pi Prefix',
    'piNumber' => 'Pi Number',
    'poPrefix' => 'Po Prefix',
    'poNumber' => 'Po Number',
    'contactName' => 'Contact Name',
    'invoicePrefix' => 'Invoice Prefix',
    'invoiceNumber' => 'Invoice Number',
    'dnPrefix' => 'Dn Prefix',
    'dnNumber' => 'Dn Number',
    'receiptPrefix' => 'Receipt Prefix',
    'receiptNumber' => 'Receipt Number',
    'budgetPrefix' => 'Budget Prefix',
    'budgetNumber' => 'Budget Number',
    'budgetTemplateId' => 'Budget Template ID',
    'dnTemplateId' => 'Dn Template ID',
    'quotationTemplateId' => 'Quotation Template ID',
    'piTemplateId' => 'Pi Template ID',
    'poTemplateId' => 'Po Template ID',
    'invoiceTemplateId' => 'Invoice Template ID',
    'receiptTemplateId' => 'Receipt Template ID',
    'isDeleted' => 'Is Deleted',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'deletedAt' => 'Deleted At',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getAttachments()
    {
    return $this->hasMany(Attachment::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getCategory()
    {
    return $this->hasOne(BusinessCategory::className(), ['id' => 'categoryId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInvoiceTemplate()
    {
    return $this->hasOne(Template::className(), ['id' => 'invoiceTemplateId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getLastPlan()
    {
    return $this->hasOne(SubscriptionPlan::className(), ['id' => 'lastPlanId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getPiTemplate()
    {
    return $this->hasOne(Template::className(), ['id' => 'piTemplateId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getPlan()
    {
    return $this->hasOne(SubscriptionPlan::className(), ['id' => 'planId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getPoTemplate()
    {
    return $this->hasOne(Template::className(), ['id' => 'poTemplateId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getStateCode0()
    {
    return $this->hasOne(States::className(), ['code' => 'stateCode']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getQuotationTemplate()
    {
    return $this->hasOne(Template::className(), ['id' => 'quotationTemplateId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getOwner()
    {
    return $this->hasOne(User::className(), ['id' => 'ownerId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusinessSettings()
    {
    return $this->hasMany(BusinessSettings::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusinessStats()
    {
    return $this->hasOne(BusinessStats::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getCustomers()
    {
    return $this->hasMany(Customer::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInventoryLocations()
    {
    return $this->hasMany(InventoryLocation::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInventoryTransactions()
    {
    return $this->hasMany(InventoryTransaction::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getInvoices()
    {
    return $this->hasMany(Invoice::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getProducts()
    {
    return $this->hasMany(Product::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getProductCategories()
    {
    return $this->hasMany(ProductCategory::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getQuotations()
    {
    return $this->hasMany(Quotation::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getReceipts()
    {
    return $this->hasMany(Receipt::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getSubscriptions()
    {
    return $this->hasMany(Subscription::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getSuppliers()
    {
    return $this->hasMany(Supplier::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getTermsConditions()
    {
    return $this->hasMany(TermsCondition::className(), ['businessId' => 'id']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getUserBusinesses()
    {
    return $this->hasMany(UserBusiness::className(), ['businessId' => 'id']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\BusinessQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\BusinessQuery(get_called_class());
}
}