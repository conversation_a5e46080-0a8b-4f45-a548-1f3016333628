<?php

namespace common\models\base;

use common\models\query\SubscriptionPlanOfferQuery;
use Yii;
use common\models\OfferRedemption;
use common\models\SubscriptionPlan;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "subscription_plan_offer".
*
    * @property integer $id
    * @property integer $planId
    * @property string $offerCode
    * @property string $description
    * @property integer $isActive
    * @property string $createdAt
    * @property string $updatedAt
    *
            * @property OfferRedemption[] $offerRedemptions
            * @property SubscriptionPlan $plan
    */
class SubscriptionPlanOfferBase extends ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'subscription_plan_offer';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['planId', 'offerCode'], 'required'],
            [['planId', 'isActive'], 'integer'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['offerCode'], 'string', 'max' => 50],
            [['description'], 'string', 'max' => 255],
            [['planId'], 'exist', 'skipOnError' => true, 'targetClass' => SubscriptionPlan::class, 'targetAttribute' => ['planId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'planId' => 'Plan ID',
    'offerCode' => 'Offer Code',
    'description' => 'Description',
    'isActive' => 'Is Active',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
];
}

    /**
    * @return ActiveQuery
    */
    public function getOfferRedemptions()
    {
    return $this->hasMany(OfferRedemption::class, ['offerId' => 'id']);
    }

    /**
    * @return ActiveQuery
    */
    public function getPlan()
    {
    return $this->hasOne(SubscriptionPlan::class, ['id' => 'planId']);
    }

    /**
     * @inheritdoc
     * @return SubscriptionPlanOfferQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new SubscriptionPlanOfferQuery(get_called_class());
}
}