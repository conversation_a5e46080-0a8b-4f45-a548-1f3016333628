<?php

namespace common\models\base;

use common\models\Business;
use common\models\query\UserBusinessQuery;
use common\models\User;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "user_business".
 *
 * @property integer $id
 * @property integer $userId
 * @property integer $businessId
 * @property integer $isOwner
 * @property string $createdAt
 * @property string $updatedAt
 *
 * @property Business $business
 * @property User $user
 */
class UserBusinessBase extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_business';
    }

    /**
     * @inheritdoc
     * @return UserBusinessQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new UserBusinessQuery(get_called_class());
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['userId', 'businessId'], 'required'],
            [['userId', 'businessId', 'isOwner'], 'integer'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['businessId'], 'exist', 'skipOnError' => true, 'targetClass' => Business::className(), 'targetAttribute' => ['businessId' => 'id']],
            [['userId'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['userId' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'userId' => 'User ID',
            'businessId' => 'Business ID',
            'isOwner' => 'Is Owner',
            'createdAt' => 'Created At',
            'updatedAt' => 'Updated At',
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getBusiness()
    {
        return $this->hasOne(Business::className(), ['id' => 'businessId']);
    }

    /**
     * @return ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'userId']);
    }
}