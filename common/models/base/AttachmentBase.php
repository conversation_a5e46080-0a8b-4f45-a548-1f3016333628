<?php

namespace common\models\base;

use Yii;
use common\models\Business;

/**
 * This is the model class for table "attachment".
*
    * @property integer $id
    * @property integer $businessId
    * @property string $title
    * @property string $type
    * @property string $linkUrl
    * @property string $fileUrl
    * @property integer $isDeleted
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $deletedAt
    *
            * @property Business $business
    */
class AttachmentBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'attachment';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['businessId'], 'required'],
            [['businessId', 'isDeleted'], 'integer'],
            [['type'], 'string'],
            [['createdAt', 'updatedAt', 'deletedAt'], 'safe'],
            [['title'], 'string', 'max' => 250],
            [['linkUrl', 'fileUrl'], 'string', 'max' => 350],
            [['businessId'], 'exist', 'skipOnError' => true, 'targetClass' => Business::className(), 'targetAttribute' => ['businessId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'businessId' => 'Business ID',
    'title' => 'Title',
    'type' => 'Type',
    'linkUrl' => 'Link Url',
    'fileUrl' => 'File Url',
    'isDeleted' => 'Is Deleted',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'deletedAt' => 'Deleted At',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getBusiness()
    {
    return $this->hasOne(Business::className(), ['id' => 'businessId']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\AttachmentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\AttachmentQuery(get_called_class());
}
}