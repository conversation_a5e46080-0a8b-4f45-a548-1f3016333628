<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class PlatformType
{
    //("")
    public const IOS = 'ios';
    public const ANDROID = 'android';
    public const WEB = 'web';


    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::IOS, self::ANDROID, self::WEB];
    }
}