<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

use common\models\Business;

class QuotationStatus
{
    // "new","revised","paid","partially-paid","unpaid"
    public const NEW = 'new';
    public const REVISED = 'revised';
    public const DISPATCHED = 'dispatched';
    public const IN_PROGRESS = 'in-progress';
    public const APPROVED = 'approved';
    public const TENDER_INQUIRY = "tender-inquiry";
    public const NEGOTIATION = "negotiation";
    public const REJECTED = 'rejected';
    public const HOT = 'hot';
    public const COLD = 'cold';
    public const WARM = 'warm';
    public const WBC = 'wbc';
    public const CLOSED = 'closed';

    public static $defaultStatusArray = [
        self::NEW, self::REVISED, self::DISPATCHED, self::IN_PROGRESS,
        self::APPROVED, self::NEGOTIATION, self::REJECTED,
        self::HOT, self::COLD, self::WARM, self::COLD,
        self::WBC, self::CLOSED
    ];
    public static $allowedStatusArray;
    public static $group;

    public static function getSelectionArray($group = Key::GROUP_QUOTATION): array
    {
        $allowedStatusData = Business::getConfig($group, Key::allowedFollowupStatus);
        if (!empty($allowedStatusData)) {
            $allowedStatusArray = [];
            foreach ($allowedStatusData as $statusData) {
                $allowedStatusArray[$statusData['key']] = $statusData['label'];
            }
            return $allowedStatusArray;
        }
        $statusArray = self::getArray($group);
        $arrayItems = [];
        foreach ($statusArray as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray($group = null): array
    {
        if ($group){
            $allowedStatusData = Business::getConfig($group, Key::allowedFollowupStatus);
            $allowedStatusArray = [];
            if (!empty($allowedStatusData)) {
                //  $allowedStatusArray = [self::NEW, self::REVISED];
                foreach ($allowedStatusData as $key => $statusData) {
                    $allowedStatusArray[] = $statusData['key'];
                }
                return $allowedStatusArray;
            }
        }
        return self::$defaultStatusArray;
    }

    public static function getFollowupArray(): array
    {

        return [self::IN_PROGRESS, self::APPROVED, self::REJECTED];
    }


    public static function getColor($status, $group = Key::GROUP_QUOTATION)
    {
        if ($group == self::$group && !empty(self::$allowedStatusArray) && !empty(self::$allowedStatusArray[$status]['color'])) {
            return self::$allowedStatusArray[$status]['color'];
        }
        $allowedStatusData = Business::getConfig($group, Key::allowedFollowupStatus);
        if (!empty($allowedStatusData)) {
            self::$group = $group;
            foreach ($allowedStatusData as $statusData) {
                self::$allowedStatusArray[$statusData['key']] = $statusData;
            }
            if (!empty($allowedStatusData[$status]['color'])) {
                return $allowedStatusData[$status]['color'];
            }
        }
        switch ($status) {
            case self::NEW:
                return "green";
            case self::REVISED:
                return "orange";
            case self::REJECTED:
                return "red";
        }
        return "light-blue";
    }
    public static function getLabel($status, $group = Key::GROUP_QUOTATION)
    {
        if ($group == self::$group && !empty(self::$allowedStatusArray) && !empty(self::$allowedStatusArray[$status]['label'])) {
            return self::$allowedStatusArray[$status]['label'];
        }
        $allowedStatusData = Business::getConfig($group, Key::allowedFollowupStatus);
        if (!empty($allowedStatusData)) {
            self::$group = $group;
            foreach ($allowedStatusData as $statusData) {
                self::$allowedStatusArray[$statusData['key']] = $statusData;
            }
            if (!empty($allowedStatusData[$status]['label'])) {
                return $allowedStatusData[$status]['label'];
            }
        }
        return ucfirst($status);
    }

    public static function getClass($status, $group = Key::GROUP_QUOTATION)
    {
        switch ($status) {
            case self::NEW:
                return "success";
            case self::REVISED:
                return "secondary";
            case self::REJECTED:
                return "danger";
        }
        return "info";
    }

    public static function allowedForDisplay(){
        return [self::IN_PROGRESS, self::APPROVED, self::REJECTED];
    }


}