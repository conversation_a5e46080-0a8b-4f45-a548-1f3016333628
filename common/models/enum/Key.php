<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class Key extends Fields
{
    public const GROUP_APP = 'app';
    public const GROUP_QUOTATION = 'quotation';
    public const GROUP_BUDGET = 'budget';
    public const GROUP_INVOICE = 'invoice';
    public const GROUP_LOCALE = "locale";
    public const GROUP_INVENTORY = "inventory";
    public const GROUP_SALES_USER = "sales_user";
    public const GROUP_MULTI_USER = "multi_user";
    public const GROUP_RECEIPT = "receipt";
    public const GROUP_BUSINESS = "business";

    public const DISCOUNT_SETTINGS = 'discount_settings';  //  "no_discount", "no_discount,per_item,on_total"
    public const PER_ITEM = 'per_item';
    public const ON_TOTAL = 'on_total';
    public const NO_DISCOUNT = 'no_discount';

    public const TAX_LABEL = 'tax_label';
    public const TAX_RATE_ON_TOTAL = 'tax_rate_on_total';
    public const TAX_SETTINGS = 'tax_settings'; // "no_tax,per_item,on_total"
    public const NO_TAX = 'no_tax';

    public const TOP_MESSAGE = 'top_message';
    public const BOTTOM_MESSAGE = 'bottom_message';
    public const IS_DISPLAY_BANK_DETAILS = 'is_display_bank_details';
    public const IS_OTHER_CHARGES = 'is_other_charges';
    public const OTHER_CHARGES_LABEL = 'other_charges_label';
    public const IS_NOTIFICATION_ENABLED = 'is_notification_enabled';

    public const ALLOW_CREATE_CUSTOMER_TO_SALES_USER = 'allow_create_customer_to_sales_user';
    public const IS_DISPLAY_APP_NAME = 'is_display_app_name';
    public const IS_PRODUCT_UNIT_DISPLAY_ENABLED = 'is_product_unit_display_enabled';
    public const IS_HEADER_FOOTER_ENABLED = 'is_header_footer_enabled';
    public const IS_FOLLOW_UP_ENABLED = 'is_follow_up_enabled';
    public const IS_PRODUCT_IMAGE_ENABLED = 'is_product_image_enabled';
    public const IS_PRODUCT_GALLERY_ENABLED = 'is_product_gallery_enabled';


    public const LOCALE_CODE = 'locale_code';
    public const LANGUAGE_CODE = 'language_code';
    public const REGION_CODE = 'region_code';
    public const CURRENCY_CODE = 'currency_code';
    public const DATE_FORMAT = 'date_format';

    public const INVOICE_ACCESS = 'invoice_access';

    public const TEMPLATE = "template";
    public const HSN_CODE_LABEL = "hsn_code_label";
    public const IS_DISPLAY_HSN_CODE = "is_display_hsn_code";

    // for global app settings
    public const CONTACT_NUMBER = "contact_number";
    public const SECONDARY_CONTACT_NUMBER = "secondary_contact_number";
    public const CONTACT_EMAIL = "contact_email";
    public const WEBSITE = "website";
    public const APP_REVIEW_DISPLAY_COUNT = "app_review_display_count"; // after how many quotations it should display app review pop-up on the app
    public const FREE_USAGE_LIMIT = "free_usage_limit";
    public const DEFAULT_TEMPLATE_ID = "default_template_id";
    public const IOS_YOUTUBE_INTRO_VIDEO_ID = "ios_youtube_intro_video_id";
    public const ANDROID_YOUTUBE_INTRO_VIDEO_ID = "android_youtube_intro_video_id";
    public const FREE_USAGE_COUNT_FOR_DISPLAY_SUBSCRIPTION = "free_usage_count_for_display_subscription";
    public const STRIPE_PUBLISHABLE_KEY = "stripe_publishable_key";
    public const STRIPE_SECRET_KEY = "stripe_secret_key";
    public const isSkipSubscriptionVerification = "isSkipSubscriptionVerification";
    public const SHOULD_DISPLAY_IOS_ADS = "should_display_ios_ads";
    public const SHOULD_DISPLAY_ANDROID_ADS = "should_display_android_ads";
    public const IOS_REWARD_ADS_UNIT_ID = "ios_reward_ads_unit_id";
    public const ANDROID_REWARD_ADS_UNIT_ID = "android_reward_ads_unit_id";
    public const FORCE_UPDATE_MINIMUM_IOS_VERSION = "force_update_minimum_ios_version";
    public const FORCE_UPDATE_MINIMUM_ANDROID_VERSION = "force_update_minimum_android_version";
    public const IOS_BANNER_ADS_SET_1 = "ios_banner_ads_set_1";
    public const IOS_BANNER_ADS_SET_2 = "ios_banner_ads_set_2";
    public const IOS_BANNER_ADS_SET_3 = "ios_banner_ads_set_3";
    public const ANDROID_BANNER_ADS_SET_1 = "android_banner_ads_set_1";
    public const ANDROID_BANNER_ADS_SET_2 = "android_banner_ads_set_2";
    public const ACTIVE_ALL_REWARD_ADS_COUNT = "active_all_reward_ads_count";
    public const FORCE_SUBSCRIPTION_COUNT = "force_subscription_count";
    public const DEFAULT_QUOTATION_TEMPLATE_SLUG = "default_quotation_template_slug";
    public const DEFAULT_PO_TEMPLATE_SLUG = "default_po_template_slug";
    public const DEFAULT_RECEIPT_TEMPLATE_SLUG = "default_receipt_template_slug";
    public const DEFAULT_INVOICE_TEMPLATE_SLUG = "default_invoice_template_slug";

    public const DEFAULT_PROFORMA_INVOICE_TEMPLATE_SLUG = "default_proforma_invoice_template_slug";
    public const FREE_INVOICE_USAGE_LIMIT = "free_invoice_usage_limit";
    public const DISPLAY_SERIAL_COLUMN = "display_serial_column";
    public const PACKAGE_NAME = "package_name";
    public const APP_NAME = "app_name";
    public const MINIMUM_LIMIT_FOR_MULTIPLE_ADS = "minimum_limit_for_multiple_ads";
    /*  Custom Params */
    public const IS_DISPLAY_TOTAL = "is_display_total";
    public const IS_DISPLAY_NET_RATE = "is_display_net_rate";

    public const GROUP_CUSTOM = "custom";
    public const INVOICE_LABEL = "invoice_label";

    // Custom Settings KEYS
    public const TOTAL_USER_LIMIT = "total_user_limit";
    public const TOTAL_PRODUCT_LIMIT = "total_product_limit";
    public const TOTAL_LINK_ATTACHMENT_LIMIT = "total_link_attachment_limit";
    public const TOTAL_IMAGE_ATTACHMENT_LIMIT = "total_image_attachment_limit";
    public const TOTAL_PDF_ATTACHMENT_LIMIT = "total_pdf_attachment_limit";
    public const ADDITIONAL_PRODUCT_FIELDS = "additional_product_fields";
    public const IS_SUBJECT_ENABLED = "is_subject_enabled";
    public const IS_INVOICE_ENABLED = "is_invoice_enabled";
    public const IS_PURCHASE_ORDER_ENABLED = "is_purchase_order_enabled";
    public const IS_PROFORMA_ENABLED = "is_proforma_enabled";
    public const ALLOW_CREATE_PRODUCT_TO_SALES_USER = "allow_create_product_to_sales_user";
    public const GROUP_PURCHASE_ORDER = "purchase_order";
    public const BOOL = "bool";
    public const BOOLEAN = "boolean";
    public const STRING = "string";
    public const INT = "int";
    public const FLOAT = "float";
    public const JSON = "json";
    public const IS_DISPlAY_AMOUNT_WORDS = "is_display_amount_words";
    public const PDF_NAME_TEMPLATE = "pdf_name_template";

    // stripe account names
    public const STRIPE_ACCOUNTS = "stripe_accounts";

    public const SUPPORTED_LANGUAGES = "supported_languages";
    public const SUPPORTED_CURRENCY = "supported_currency";
    const APP_ENV = "app_env";

    public const PUBLISHABLE_KEY = "PUBLISHABLE_KEY";
    public const SECRET_KEY = "SECRET_KEY";
    public const WEBHOOK_SECRET = "WEBHOOK_SECRET";
    public const DEFAULT_ACCOUNT = "DEFAULT_ACCOUNT";
    public const resetBusinessFields = "resetBusinessFields";
    public const businessFields = "businessFields";
    public const BusinessSettings = "businessSettings";
    public const QUOTATION_FREE_USAGE_LIMIT = "quotation_free_usage_limit";

    public const PURCHASE_ORDER_FREE_USAGE_LIMIT = "purchase_order_free_usage_limit";
    public const INVOICE_FREE_USAGE_LIMIT = "invoice_free_usage_limit";

    public const MAINTENANCE_MODE = "maintenance_mode";
    public const IS_MARGIN_ON_PRODUCT_ENABLED = "is_margin_on_product_enabled";
    public const ENV = "env";
    public const INDIA_ACCOUNT = "india_account";
    public const FOREIGN_ACCOUNT = "foreign_account";
    public const GROUP_PROFORMA_INVOICE = "proforma_invoice";
    public const PROFORMA_FREE_USAGE_LIMIT = "proforma_free_usage_limit";
    public const DELIVERY_FREE_USAGE_LIMIT = "delivery_free_usage_limit";
    public const BUDGET_FREE_USAGE_LIMIT = "budget_free_usage_limit";
    public const RECEIPT_FREE_USAGE_LIMIT = "receipt_free_usage_limit";
    public const IS_QUOTATION_TEMPLATE_SECTION_ENABLED = "is_quoation_template_section_enabled";
    public const IS_LUMPSUM_AMOUNT_ENABLED = "is_lumpsum_amount_enabled";
    public const isPageBreakForTerms = "isPageBreakForTerms";

    public const MAX_DECIMAL_PLACES = "max_decimal_places";
    public const currencyWith3DecimalSupport = "currencyWith3DecimalSupport";
    public const RAZORPAY_ACCOUNTS = "razorpay_accounts";
    public const razorpayKeyId = "razorpayKeyId";
    public const razorpayKeySecret = "razorpayKeySecret";
    public const razorpayWebhookSecret = "razorpayWebhookSecret";
    public const LOG_VALIDATION_ERRORS = "log_validation_errors";
    public const SKIP_ERROR_CODES = "skip_error_codes";
    public const isHeaderEnabled = "isHeaderEnabled";
    public const DEFAULT_DELIVERY_NOTE_TEMPLATE_SLUG = "default_delivery_note_template_slug";
    public const DEFAULT_BUDGET_TEMPLATE_SLUG = "default_budget_template_slug";
    public const GROUP_DELIVERY_NOTE = "delivery_note";
    public const maxOtherInfoFields = "maxOtherInfoFields";
    public const shouldRepeatHeaderFooter = "shouldRepeatHeaderFooter";
    public const isBudgetTermsEnable = "isBudgetTermsEnable";
    public const isCompanyScopeEnable = "isCompanyScopeEnable";
    public const isCustomerScopeEnable = "isCustomerScopeEnable";
    public const allowedFollowupStatus = "allowedFollowupStatus";
    public const shouldDisplayPrice = "shouldDisplayPrice";
    public const IS_PAGE_BREAK_BEFORE_TERMS = "is_page_break_before_terms";
    public const businessNameStyle = "businessNameStyle";
    public const isOnlyPriceDisplay = "isOnlyPriceDisplay";
    public const isDisplaySalesPerson = "isDisplaySalesPerson";
    public const isDisplaySignatureBlock = "isDisplaySignatureBlock";
    public const usageLimitThreshold = "usageLimitThreshold";
    public const discountLabel = "discountLabel";
    public const devUserIds = "devUserIds";
    public const TOTAL_PRODUCT_IMAGE_LIMIT = "total_product_image_limit";
    public const shouldDisplayProductAdditionalFields = "shouldDisplayProductAdditionalFields";
    public const isProductCodeEnable = "isProductCodeEnable";
    public const isDisplayReferenceName = "isDisplayReferenceName";
    public const isDisplaySalesPersonName = "isDisplaySalesPersonName";
    public const IS_INVENTORY_ENABLED = "is_inventory_enabled";
    public const useSeparateInventoryItems = "useSeparateInventoryItems";
    public const txnAdditionalFields = "txnAdditionalFields";
    public const txnItemsAdditionalFields = "txnItemsAdditionalFields";
    public const disabledProductFields = "disabledProductFields";
    public const usageLimitForWatermark = "usageLimitForWatermark";
    public const isAttachmentEnable = "isAttachmentEnable";
    public const allowedAttachmentTypes = "allowedAttachmentTypes";
    public const isCategoryWiseDiscountEnable = "isCategoryWiseDiscountEnable";
    public const canAccessInvoice = "canAccessInvoice";
    public const canAccessQuotation = "canAccessQuotation";
    public const canAccessPo = "canAccessPo";
    public const canAccessPi = "canAccessPi";
    public const canAccessDeliveryNote = "canAccessDeliveryNote";
    public const canAccessReceipt = "canAccessReceipt";
    public const shouldAllowCreateProduct = "shouldAllowCreateProduct";
    public const shouldAllowCreateTerms = "shouldAllowCreateTerms";
    public const shouldDisplayOtherCustomers = "shouldDisplayOtherCustomers";
    public const isBarcodeScanEnable = "isBarcodeScanEnable";
    public const IS_DISPLAY_PRODUCT_CODE = "is_display_product_code";
    public const isDisplayUnitColumn = "isDisplayUnitColumn";
    public const solarUnitsPerYearPerKw = "solarUnitsPerYearPerKw";
    public const GROUP_ITEMIZED_RECEIPT = "itemized_receipt";
    public const isItemizedReceipt = "isItemizedReceipt";
    public const receiptType = "receiptType";
    public const SIMPLE = "simple";
    public const ITEMIZED = "itemized";
    public const IS_PDF_STORAGE_ENABLED = "is_pdf_storage_enabled";
    public const IS_QTY2_ENABLED = "is_qty2_enabled";
    public const QTY2_LABEL = "qty2_label";
    public const QUANTITY_LABEL = "quantity_label";
    public const shouldAllowProductCategory = "shouldAllowProductCategory";
    public const allowUserSignature = "allowUserSignature";
    public const isItemHeadingsEnabled = "isItemHeadingsEnabled";
    public const itemHeadingsData = "itemHeadingsData";
    public const isDisplayUpiDetails = "isDisplayUpiDetails";
    public const usageLimitNotificationSlots = "usageLimitNotificationSlots";

}