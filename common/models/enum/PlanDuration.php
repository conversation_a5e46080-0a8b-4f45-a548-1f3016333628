<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class PlanDuration
{
    //('active', 'inactive','blocked'
    public const WEEKLY = 'weekly';
    public const MONTHLY = 'monthly';
    public const YEARLY = 'yearly';
    public const LIFETIME = 'lifetime';


    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::WEEKLY, self::MONTHLY, self::YEARLY];
    }

    public static function getInSeconds($value): int
    {
        return match ($value) {
            self::WEEKLY => 604800,
            self::MONTHLY => 2628000,
            self::YEARLY => 31536000, // 1 year
            self::LIFETIME => 315360000, // 10 years
            default => 0,
        };

    }

    public static function getClass($value)
    {
        switch ($value) {
            case self::WEEKLY:
                $response = "success";
                break;
            case self::MONTHLY:
                $response = "secondary";
                break;
            case self::YEARLY:
                $response = "danger";
                break;
            case self::LIFETIME:
                $response = "info";
                break;
            default:
                $response = "info";
        }
        return $response;
    }

}