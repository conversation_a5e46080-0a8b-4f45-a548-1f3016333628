<?php

namespace common\models\enum;

use common\models\AppSettings;
use common\models\Business;
use common\models\Region;
use common\models\traits\AppSettingsTrait;
use common\models\traits\BudgetSettings;
use common\models\traits\CustomSettings;
use common\models\traits\DeliveryNoteSettings;
use common\models\traits\InventorySettings;
use common\models\traits\InvoiceSettings;
use common\models\traits\MultiUserSettings;
use common\models\traits\ProformaInvoiceSettings;
use common\models\traits\PurchaseOrderSettings;
use common\models\traits\QuotationSettings;
use common\models\traits\ReceiptSettings;
use common\models\traits\SalesUserSettings;
use common\models\traits\SettingsContext;

class DefaultBusinessSettings
{
    /**
     * @var Business
     */
    public static $business = null;
    public const BUSINESS_NAME = "{business_name}";
    public const BUSINESS_REGION_CODE = "IN";
    /**
     * @var string[]
     */
    public static $supportedCountries = ["IN", "ZA"];

    use CustomSettings;
    use AppSettingsTrait;
    use PurchaseOrderSettings;
    use QuotationSettings;
    use BudgetSettings;
    use InvoiceSettings;
    use ProformaInvoiceSettings;
    use DeliveryNoteSettings;
    use InventorySettings;
    use SalesUserSettings;
    use ReceiptSettings;
    use MultiUserSettings;

    /**
     * @param Business|null $business
     * @return array[]
     */


    public static function overwriteSettings($group): array
    {
        $defaultSettings = [];

        $group = str_replace("-", "_", $group);
        $methodName = lcfirst($group) . '_overwrite';
        if (method_exists(self::class, $methodName)) {
            $defaultSettings = self::$methodName();
        }

        return $defaultSettings;
    }

    public static function setBusiness($business)
    {
        self::$business = $business;
    }

    public static function getBusinessTableFields()
    {
        return ['quotationNumber', 'quotationPrefix', 'poNumber',
            'poPrefix', 'piNumber', 'piPrefix', 'dnNumber',
            'dnPrefix', 'budgetNumber', 'budgetPrefix',
            'invoiceNumber', 'invoicePrefix', 'receiptNumber',
            'receiptPrefix'];
    }

    /**
     * Get the default settings for a specific type.
     *
     * @param string $group The type of settings (app, quotation, invoice, etc.)
     * @return array
     */
    public static function defaultSettings($group, $key = null): array
    {
        $defaultSettings = [];
        $freeUsageCount = 10;
        $invFreeUsageCount = 5;
        $freeUsageSetting = AppSettings::findByKey(Key::FREE_USAGE_LIMIT);
        $invFreeUsageSetting = AppSettings::findByKey(Key::FREE_INVOICE_USAGE_LIMIT);
        if ($freeUsageSetting !== null) {
            $freeUsageCount = $freeUsageSetting->getValue();
        }
        if ($invFreeUsageSetting != null) {
            $invFreeUsageCount = $invFreeUsageSetting->getValue();
        }

        $localeCode = "en-IN";
        $business = self::$business;
        if ($business) {
            $businessName = $business->name ?? self::BUSINESS_NAME;
            $regionCode = $business->regionCode ?? self::BUSINESS_REGION_CODE;
            $region = Region::findByCode($regionCode);
            $localeCode = $region->locale ?? $localeCode;
        }
        $settingContext = new SettingsContext();
        $settingContext->freeUsageCount = $freeUsageCount;
        $settingContext->invFreeUsageCount = $invFreeUsageCount;
        $settingContext->localeCode = $localeCode;
        $group = str_replace("-", "_", $group);
        $methodName = lcfirst($group) . '_settings';
        if (method_exists(self::class, $methodName)) {
            $defaultSettings = self::$methodName($settingContext);
        }

        if ($business) {
            $regionCode = $business->regionCode;
            if (in_array($regionCode, self::$supportedCountries)) {
                $defaultSettings = self::{"getDefaults_" . strtoupper($regionCode)}($defaultSettings, $group);
            }
        }
        return empty($key) ? $defaultSettings : $defaultSettings[$key];
    }

    /**
     * Get the default settings for a specific type.
     *
     * @param string $group The type of settings (app, quotation, invoice, etc.)
     * @return array
     */
    public static function getDefaultSettings($group): array
    {
        $settings = [];
        $defaultSettings = self::defaultSettings($group);
        foreach ($defaultSettings as $key => $defaultSetting) {
            $settings[$key] = getValue($defaultSetting['type'], $defaultSetting['value']);
        }
        return $settings;
    }

    public static function generate(Business $business): array
    {
        self::setBusiness($business);
        $businessSettings = [];
        $businessId = $business->id;
        $dbSettings = [
            Key::GROUP_APP => [Key::TAX_LABEL],
            Key::GROUP_QUOTATION => [Key::TAX_SETTINGS],
            Key::GROUP_INVOICE => [Key::TAX_SETTINGS],
            Key::GROUP_PROFORMA_INVOICE => [Key::TAX_SETTINGS],
            Key::GROUP_PURCHASE_ORDER => [Key::TAX_SETTINGS],
            Key::GROUP_DELIVERY_NOTE => [],
        ];

        foreach ($dbSettings as $groupKey => $groupSettings) {
            $defaultSettings = self::defaultSettings($groupKey);
            foreach ($groupSettings as $settingKey) {
                $setting = $defaultSettings[$settingKey] ?? null;
                if (!empty($setting)) {
                    $businessSettings[] = [$businessId, $setting['group'], $setting['type'], $setting['key'], $setting['value'], $setting['description']];
                }
            }
        }
        return $businessSettings;
    }

    public static function combine($defaultSettings, $localSettings)
    {
        foreach ($localSettings as $localSetting) {
            $isMatch = false;
            foreach ($defaultSettings as $key => $defaultSetting) {
                if ($defaultSetting[0] === $localSetting[0] && $defaultSetting[2] === $localSetting[2]) {
                    $defaultSettings[$key][3] = $localSetting[3];
                    $isMatch = true;
                }
            }
            if (!$isMatch) {
                $defaultSettings[] = $localSetting;
            }
        }
        return $defaultSettings;
    }

    public static function getDefaults_US(&$defaultSettings, $group)
    {
        $business = self::$business;
        switch ($group) {
            case Key::GROUP_APP:
                $setting = &$defaultSettings[Key::TAX_LABEL];
                $setting['value'] = "VAT";
                break;
            case Key::GROUP_PURCHASE_ORDER:
                break;
            case Key::GROUP_BUDGET:
                break;
            case Key::GROUP_QUOTATION:
                break;
            case Key::GROUP_INVOICE:
                break;
            case Key::GROUP_PROFORMA_INVOICE:
                break;
            // Add more cases for other types if needed
            default:
                // Handle unknown type if needed
                break;
        }

        return $defaultSettings;
    }

    /**
     * @param Business $business
     * @return array
     */
    public static function getDefaults_IN(&$defaultSettings, $group)
    {
        $business = self::$business;

        switch ($group) {
            case Key::GROUP_APP:
                $setting = &$defaultSettings[Key::TAX_LABEL];
                $setting['value'] = "GST";
                break;
            case Key::GROUP_PURCHASE_ORDER:
                if (empty($business->taxNumber)) {
                    $setting = &$defaultSettings[Key::TAX_SETTINGS];
                    $setting['value'] = Key::NO_TAX;
                }
                break;
            case Key::GROUP_BUDGET:
                if (empty($business->taxNumber)) {
                    $setting = &$defaultSettings[Key::TAX_SETTINGS];
                    $setting['value'] = Key::NO_TAX;
                }
                break;
            case Key::GROUP_QUOTATION:
                if (empty($business->taxNumber)) {
                    $setting = &$defaultSettings[Key::TAX_SETTINGS];
                    $setting['value'] = Key::NO_TAX;
                }
                break;
            case Key::GROUP_INVOICE:
                if (empty($business->taxNumber)) {
                    $setting = &$defaultSettings[Key::TAX_SETTINGS];
                    $setting['value'] = Key::NO_TAX;
                }
                break;
            case Key::GROUP_PROFORMA_INVOICE:
                if (empty($business->taxNumber)) {
                    $setting = &$defaultSettings[Key::TAX_SETTINGS];
                    $setting['value'] = Key::NO_TAX;
                }
                break;
            // Add more cases for other types if needed
            default:
                // Handle unknown type if needed
                break;
        }
        return $defaultSettings;
    }

    public static function getDefaults_ZA(&$defaultSettings, $group)
    {
        $business = self::$business;
        switch ($group) {
            case Key::GROUP_APP:
                $setting = &$defaultSettings[Key::TAX_LABEL];
                $setting['value'] = "VAT";
                break;
            case Key::GROUP_PURCHASE_ORDER:
                break;
            case Key::GROUP_BUDGET:
                break;
            case Key::GROUP_QUOTATION:
                break;
            case Key::GROUP_INVOICE:
                break;
            case Key::GROUP_PROFORMA_INVOICE:
                break;
            // Add more cases for other types if needed
            default:
                // Handle unknown type if needed
                break;
        }
        return $defaultSettings;
    }

    public static function preferenceSettings($group): array
    {
        $settings = [];

        $group = str_replace("-", "_", $group);
        $methodName = lcfirst($group) . '_preferences';
        if (method_exists(self::class, $methodName)) {
            $settings = self::$methodName();
        }

        return $settings;
    }
}