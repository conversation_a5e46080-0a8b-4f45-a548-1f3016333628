<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class ModuleType
{
    //("")
    const QUOTATION = 'quotation';
    const INVOICE = 'invoice';
    const PURCHASE_ORDER = 'purchase_order';
    const BUDGET = 'budget';
    const COMPANY_SCOPE = 'company_scope';
    const CUSTOMER_SCOPE = 'customer_scope';

    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::QUOTATION, self::INVOICE, self::PURCHASE_ORDER, self::BUDGET, self::COMPANY_SCOPE, self::CUSTOMER_SCOPE];
    }
}