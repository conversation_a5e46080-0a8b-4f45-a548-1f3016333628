<?php

namespace common\models;

class Inventory extends \common\models\base\InventoryBase
{

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getBusiness()
    {
        return $this
            ->hasOne(Business::className(), ['id' => 'businessId'])
            ->viaTable('product', ['id' => 'productId']);
    }

    public function beforeSave($insert)
    {
        return parent::beforeSave($insert); // TODO: Change the autogenerated stub
    }

}