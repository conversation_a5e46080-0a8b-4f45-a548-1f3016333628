<?php

namespace common\models;

use common\models\base\QuotationItemsBase;
use common\models\enum\Key;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

class QuotationItems extends QuotationItemsBase
{
    /**
     * @return ActiveQuery
     */
    public static function findActive()
    {
        return parent::find()->andWhere(['isDeleted' => 0]);
    }

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    public static function findByQuotation($quotationId)
    {
        return static::findAll(['quotationId' => $quotationId]);
    }

    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    public function rules()
    {
        return ArrayHelper::merge(parent::rules(),
            [
                [['discountAmount', 'taxAmount'], 'default', 'value' => 0],
                ['discountPercentage', 'number', 'min' => 0, 'max' => 100],
                ['productId', 'required', 'when' => function($model) {
                    return !$model->isSection;
                }],
                ['description', 'required', 'when' => function($model) {
                    return $model->isSection;
                }],

                ['description', 'string', 'max' => 2000, 'when' => function($model) {
                    return !IS_PREMIUM_APP;
                }, 'message' => 'Section title cannot be longer than 250 characters.'],

                ['description', 'string', 'max' => 4000, 'when' => function($model) {
                    return IS_PREMIUM_APP;
                }, 'message' => 'Section title cannot be longer than 250 characters.'],

                // when isSection is true, description max length should be 200 characters and I want to provide custom error message
                ['description', 'string', 'max' => 250, 'when' => function($model) {
                    return $model->isSection;
                }, 'message' => 'Section title cannot be longer than 250 characters.'],
            ]);
    }

    public function beforeSoftDelete()
    {
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        return true;
    }


    public function combineAdditionalFields($config, $userInput = null)
    {
        $combinedFields = [];
        foreach ($config as $field) {
            $key = $field['key'];
            if ($userInput) {
                $value = isset($userInput[$key]) ? $userInput[$key] : '';
            } else {
                $value = $field['value'];
            }
            // If the field is an enum and the value is not in the options, set it to the default value
            if ($field['type'] === 'enum') {
                $options = $field['options'];
                $options = str_replace(" || ", "||", $options);
                $options = explode('||', $options);
                $field['options'] = $options;
            }
            $field['value'] = $value;
            $combinedFields[$key] = $field;
        }
        return $combinedFields;
    }


    public function getAdditionalFieldsData()
    {
        $business = $this->quotation->business;
        $additionalFieldsConfig = $business->config(Key::GROUP_CUSTOM, Key::ADDITIONAL_PRODUCT_FIELDS);
        if (empty($additionalFieldsConfig)) {
            return null;
        }
        if (empty($this->additionalFields)) {
            return $additionalFieldsConfig;
        }

        $additionalFieldsData = json_decode($this->additionalFields, true);
        return $this->combineAdditionalFields($additionalFieldsConfig, $additionalFieldsData);
    }

}