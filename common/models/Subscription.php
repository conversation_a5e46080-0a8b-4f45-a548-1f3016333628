<?php

namespace common\models;

use common\helpers\StripeHelper;
use common\models\base\SubscriptionBase;
use common\models\enum\PlanType;
use common\models\enum\ProviderType;
use common\models\enum\SubscriptionStatus;
use common\services\EmailService;
use Yii;
use yii\base\Exception;
use yii\helpers\Json;

class Subscription extends SubscriptionBase
{
    /**
     * @var array Additional transaction details that will be stored as JSON
     */
    private $_transactionDetailsData = [];

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public
    static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * Activate Subscription
     * @return bool
     */
    public function activate()
    {
        $this->status = SubscriptionStatus::ACTIVE;
        $this->isActive = 1;
        return $this->save();
    }

    /**
     * Deactivate Subscription
     * @return bool
     */
    public function deactivate($status = SubscriptionStatus::EXPIRED)
    {
        $this->status = $status;
        $this->isActive = 0;
        return $this->save();
    }

    /**
     * Cancels Subscription
     * @return bool
     */
    public function cancel($status = SubscriptionStatus::CANCELLED)
    {
        return $this->deactivate($status);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($this->provider === ProviderType::RAZORPAY) {
            RzpSubscriptionCache::removeBySubscriptionId($this->providerSubscriptionId);
        }
        if ($insert || isset($changedAttributes['isActive']) || isset($changedAttributes['planId'])) {
            if ($this->isActive) {
                $this->business->subscribeToPlan($this->plan);
                if ($this->plan->type === PlanType::BASE) {
                    $this->user->subscribeToPlan($this->planId);
                    StripeHelper::checkPastDueSubscriptions($this->business, $this->plan, $this->providerSubscriptionId);
                }
                $this->sendPaymentNotificationEmail();
            } else {
                $this->business->unsubscribeToPlan($this->plan);
                if ($this->plan->type === PlanType::BASE) {
                    $this->user->unSubscribeToPlan($this->planId);
                }
                if ($this->plan->type === PlanType::MULTI_USER) {
                    $this->business->handleMultiUserPlanRemoval($this);
                }

                if ($this->status !== "incomplete") {
                    $this->sendUnsubscribeNotificationEmail();
                }
            }
        }
        parent::afterSave($insert, $changedAttributes);
    }

    /**
     * Sends payment notification email to admin
     */
    public function sendPaymentNotificationEmail()
    {
        $emailService = new EmailService(true);
        $emailService->sendSubscriptionReceiptEmail($this->user, $this);

        $this->user->sendNotification("You have successfully subscribed to {$this->plan->name}! For any issues/queries please contact to our support from contact us screen in the app!");
    }

    public function sendUnsubscribeNotificationEmail()
    {
        $emailService = new EmailService(true);
        $emailService->sendSubscriptionRemovedEmail($this->user, $this);
    }

    public function sendPastdueCancelNotificationEmail()
    {
        $emailService = new EmailService(true);
        $emailService->sendAdminMail(
            'notifications/stripe-pastdue-cancel-notification',
            [
                'subscription' => $this,
                'user' => $this->user,
                'business' => $this->business,
                'plan' => $this->plan
            ],
            'Stripe Past-due Subscription Removed'
        );
    }

    public function sendPaymentIntentEmail()
    {
        $usageLevel = "L-" . $this->business->getUsageLevel();

        $emailService = new EmailService(true);
        $emailService->sendAdminMail(
            'notifications/payment-intent-create',
            [
                'subscription' => $this,
                'user' => $this->user,
                'business' => $this->business,
                'plan' => $this->plan
            ],
            "Payment-Intent $usageLevel"
        );
    }

    public static function SendPaymentIntentEmailNotification($user, $plan, $provider)
    {
        $usageLevel = "L-" . $user->business->getUsageLevel();

        $emailService = new EmailService(true);
        return $emailService->sendAdminMail(
            'notifications/payment-intent-create',
            [
                'providerData' => $provider,
                'user' => $user,
                'business' => $user->business,
                'plan' => $plan
            ],
            "Payment-Intent $usageLevel"
        );
    }

    /**
     * Sends payment failed notification email to admin and user
     *
     * @param string $paymentIntentId The payment intent ID
     * @param string $failureCode The error code from payment provider
     * @param string $failureMessage The error message from payment provider
     * @return bool Whether the email was sent successfully
     */
    public static function sendPaymentFailedNotificationEmail($paymentIntentId, $failureCode, $failureMessage)
    {
        $subscription = self::find()
            ->where(['paymentIntentClientSecret' => $paymentIntentId])
            ->one();

        if (!$subscription) {
            Yii::info("No subscription found for paymentIntent: $paymentIntentId");
            return false;
        }

        $user = $subscription->user ?? null;
        $business = $user->business ?? null;

        if (!$user || !$business) {
            Yii::error("Missing user/business for subscription {$subscription->id}");
            return false;
        }

        $emailService = new EmailService(true);
        return $emailService->sendPaymentFailedMails($user, $subscription, $failureCode, $failureMessage);
    }


    /**
     * @param $providerSubscriptionId
     * @param User $user | null
     * @return array|Subscription|null
     * @throws Exception
     */
    public static function getExistingSubscription($providerSubscriptionId, User $user = null): array|null|Subscription
    {
        $subscription = self::find()->byProviderSubscriptionId($providerSubscriptionId)->one(); // in case receipt is sent again
        if ($subscription === null && $user === null) {
            $message = "Subscription transaction not available in DB!";
            $message = "\n" . $message . "\n" . "providerSubscriptionId :: " . $providerSubscriptionId;
            Yii::error($message);
            throw new Exception($message);
        }
        if ($subscription && $user && $user->business && $subscription->businessId !== $user->business->id) {
            $otherUser = $subscription->user;
            $otherBusiness = $otherUser->business;
            $message = "You have already subscribed with another account! Please check following details \n User : {$otherUser->name} and Business : {$otherBusiness->name} \n PIN : {$otherBusiness->ownerId} \n Please contact support for further assistance!";
            Yii::error("Subscription from other user \n\n" . $message);
            throw new Exception($message);
        }
        return $subscription;
    }

    /**
     * Sets the IP address of the user during payment
     *
     * @param string $ipAddress The IP address to store
     * @return $this
     */
    public function setIpAddress($ipAddress)
    {
        $this->ipAddress = $ipAddress;
        return $this;
    }

    /**
     * Sets the price and currency information for the subscription
     *
     * @param float|null $amount The actual amount paid (if different from plan price)
     * @param string|null $currencyCode The currency of the actual amount paid
     * @return $this
     */
    public function setPrice($amount = null, $currencyCode = null)
    {
        if ($amount !== null && $currencyCode !== null) {
            // Store the actual price and currency
            $this->price = $amount;
            $this->currency = $currencyCode;
        }
        return $this;
    }

    /**
     * Gets the formatted amount with currency symbol
     *
     * @return string The formatted amount with currency symbol
     */
    public function getFormattedAmount()
    {
        if (!empty($this->price) && !empty($this->currency)) {
            return formatAsCurrency($this->price, $this->currency);
        }
        return '';
    }

    /**
     * Sets the payment receipt URL or data
     *
     * @param string $paymentReceipt The payment receipt URL or data
     * @return $this
     */
    public function setPaymentReceipt($paymentReceipt)
    {
        $this->paymentReceipt = $paymentReceipt;
        return $this;
    }

    /**
     * Adds a transaction detail to be stored in the transactionDetails field
     *
     * @param string $key The key for the transaction detail
     * @param mixed $value The value for the transaction detail
     * @return $this
     */
    public function addTransactionDetail($key, $value)
    {
        // Load existing transaction details if available
        if (!empty($this->transactionDetails) && empty($this->_transactionDetailsData)) {
            try {
                $this->_transactionDetailsData = Json::decode($this->transactionDetails);
            } catch (\Exception $e) {
                Yii::error("Error decoding transaction details: " . $e->getMessage());
                $this->_transactionDetailsData = [];
            }
        }

        // Add the new detail
        $this->_transactionDetailsData[$key] = $value;

        // Save back to the model
        $this->transactionDetails = Json::encode($this->_transactionDetailsData);

        return $this;
    }

    /**
     * Gets a transaction detail by key
     *
     * @param string $key The key for the transaction detail
     * @param mixed $default The default value if the key doesn't exist
     * @return mixed The transaction detail value
     */
    public function getTransactionDetail($key, $default = null)
    {
        if (empty($this->_transactionDetailsData) && !empty($this->transactionDetails)) {
            try {
                $this->_transactionDetailsData = Json::decode($this->transactionDetails);
            } catch (\Exception $e) {
                Yii::error("Error decoding transaction details: " . $e->getMessage());
                return $default;
            }
        }

        return $this->_transactionDetailsData[$key] ?? $default;
    }

    /**
     * Gets all transaction details
     *
     * @return array The transaction details
     */
    public function getTransactionDetails()
    {
        if (empty($this->_transactionDetailsData) && !empty($this->transactionDetails)) {
            try {
                $this->_transactionDetailsData = Json::decode($this->transactionDetails);
            } catch (\Exception $e) {
                Yii::error("Error decoding transaction details: " . $e->getMessage());
                return [];
            }
        }

        return $this->_transactionDetailsData;
    }

    /**
     * Captures the current user's IP address and stores it in the subscription
     *
     * @return $this
     */
    public function captureUserIp()
    {
        $ipAddress = Yii::$app->request->userIP;
        return $this->setIpAddress($ipAddress);
    }
}