<?php

namespace common\models;

use common\models\enum\ProviderType;

class Ra<PERSON>payUser extends \common\models\base\RazorpayUserBase
{

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByUserIdAndAccount($userId, $razorpayAccount)
    {
        $subscription = Subscription::find()->byUserId($userId)->andWhere(['provider' => ProviderType::RAZORPAY, 'providerAccount' => $razorpayAccount, 'isActive' => 1])->one();

        if ($subscription && !empty($subscription->providerCustomerId)) {
            return static::find()->byCustomerId($subscription->providerCustomerId)->orderBy("updatedAt DESC")->one();
        }

        return static::find()->where(['userId' => $userId, 'razorpayAccount' => $razorpayAccount])->orderBy('id DESC')->one();
    }

}