<?php

namespace common\models\query;

use common\models\QuotationLog;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\QuotationLog]].
 *
 * @see \common\models\QuotationLog
 */
class QuotationLogQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return QuotationLog[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return QuotationLog|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
