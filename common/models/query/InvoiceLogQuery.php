<?php

namespace common\models\query;

use common\models\InvoiceLog;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\InvoiceLog]].
 *
 * @see \common\models\InvoiceLog
 */
class InvoiceLogQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return InvoiceLog[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return InvoiceLog|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
