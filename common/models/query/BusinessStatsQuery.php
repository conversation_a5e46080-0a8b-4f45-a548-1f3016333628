<?php

namespace common\models\query;

use common\models\BusinessStats;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\BusinessStats]].
 *
 * @see \common\models\BusinessStats
 */
class BusinessStatsQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return BusinessStats[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return BusinessStats|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
