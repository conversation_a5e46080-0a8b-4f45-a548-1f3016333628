<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\Receipt]].
 *
 * @see \common\models\Receipt
 */
class ReceiptQuery extends \yii\db\ActiveQuery
{
    public function active()
    {
        return $this->andWhere(['isDeleted' => 0]);
    }

    public function byBusiness($businessId)
    {
        return $this->andWhere(['businessId' => $businessId]);
    }

    public function byCustomer($customerId)
    {
        return $this->andWhere(['customerId' => $customerId]);
    }

    public function latest()
    {
        return $this->orderBy(['createdAt' => SORT_DESC]);
    }
}