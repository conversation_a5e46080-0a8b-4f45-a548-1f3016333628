<?php

namespace common\models\query;

use common\models\Business;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\Business]].
 *
 * @see \common\models\Business
 */
class BusinessQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return Business[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return Business|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }

    public function byId($businessId, $alias = "")
    {
        if ($alias != "") {
            $alias .= ".";
        }
        $this->andWhere([$alias . 'id' => $businessId]);
        return $this;
    }
}
