<?php

namespace common\models\query;

use common\models\OfferRedemption;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\OfferRedemption]].
 *
 * @see \common\models\OfferRedemption
 */
class OfferRedemptionQuery extends ActiveQuery
{
    public function byOfferIdAndUserId($offerId, $userId)
    {
        $this->andWhere(['offerId'=>$offerId, 'userId'=>$userId]);
        return $this;
    }

    /**
     * @inheritdoc
     * @return OfferRedemption[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return OfferRedemption|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
