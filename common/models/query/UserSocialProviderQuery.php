<?php

namespace common\models\query;

use common\models\UserSocialProvider;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\UserSocialProvider]].
 *
 * @see \common\models\UserSocialProvider
 */
class UserSocialProviderQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return UserSocialProvider[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return UserSocialProvider|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
