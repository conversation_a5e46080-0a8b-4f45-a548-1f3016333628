<?php

namespace common\models\query;

use common\models\UserBusiness;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\UserBusiness]].
 *
 * @see \common\models\UserBusiness
 */
class UserBusinessQuery extends ActiveQuery
{
    /*public function active()
    {
        $this->andWhere('[[status]]=1');
        return $this;
    }*/

    /**
     * @inheritdoc
     * @return UserBusiness[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return UserBusiness|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }

    public function byBusinessId($businessId)
    {
        $this->andWhere(['businessId'=>$businessId]);
        return $this;
    }
}
