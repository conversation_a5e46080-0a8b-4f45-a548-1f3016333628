<?php

namespace common\models;

use common\helpers\FileManager;
use common\models\base\BusinessBase;
use common\models\enum\AttachmentType;
use common\models\enum\DefaultBusinessSettings;
use common\models\enum\Key;
use common\models\enum\PlanFeature;
use common\models\enum\PlanType;
use common\validators\SafeFilterValidator;
use JsonException;
use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

/**
 * This is the model class for table "business".
 *
 *
 *
 * @property integer activeUsersCount
 * @property User[] $users
 * @property Subscription[] $activeSubscriptions
 * @property SubscriptionPlan[] $activePlans
 */
class Business extends BusinessBase
{
    /**
     * @var UploadedFile
     */
    public $imageFile, $signatureImgFile;
    public $headerFile, $footerFile;
    public $upiQrImageFile;
    /**
     * @var mixed|null
     */
    public static $settings;

    private static $currentBusiness;

    private $activePlanIds;

    private $activeBasePlanIds;

    private $_settings;
    private $_planFeatures;

    const EVENT_BEFORE_UPDATE = 'beforeUpdate';

    public function init()
    {
        parent::init();
        $this->on(self::EVENT_BEFORE_UPDATE, [$this, 'beforeUpdate']);
    }

    public function __construct($config = [])
    {
        parent::__construct($config);
        if ($this->id) {
            $this->_settings = $this->settings();
            $this->_planFeatures = $this->getPlanFeatures();
        }
    }

    /**
     * @return ActiveQuery
     */
    public static function findActive()
    {
        return parent::find()->andWhere(['isDeleted' => 1]);
    }

    /**
     * Finds Model by id
     *
     * @param string|int $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    public static function getBusiness($reset = false)
    {
        if (self::$currentBusiness && !$reset) {
            return self::$currentBusiness;
        }
        self::$currentBusiness = null;
        /** @var User $currentUser */
        $currentUser = Yii::$app->user->identity;
        if ($currentUser) {
            self::$currentBusiness = $currentUser->business;
        }
        return self::$currentBusiness;
    }

    public static function setBusiness($businessId): ?Business
    {
        self::$currentBusiness = Business::findByPk($businessId);
        self::$settings = null;
        DefaultBusinessSettings::setBusiness(self::$currentBusiness);
        return self::$currentBusiness;
    }


    /**
     * @inheritdoc
     */

    public function rules()
    {
        return ArrayHelper::merge(parent::rules(), [
//            ['email', 'email'],
            [['imageFile', 'signatureImgFile', 'headerFile', 'footerFile', 'upiQrImageFile'], 'file', 'checkExtensionByMimeType' => false, 'skipOnEmpty' => true, 'extensions' => 'png, jpg, jpeg'],
            ['email', SafeFilterValidator::class, 'filter' => 'trim'],
            [['otherCategory'], 'default', 'value' => null, 'isEmpty' => function ($value) {
                return $value === '';
            }],
            [['panNumber'], 'match', 'pattern' => '/^[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}$/', 'message' => 'PAN Number is invalid'],
            [['upiCode'], 'string', 'max' => 255],
        ]);
    }

    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    public function beforeSoftDelete()
    {
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        return true;
    }

    /**
     * @return ActiveQuery
     */
    public function getUsers()
    {
        return $this->hasMany(User::className(), ['id' => 'userId'])
            ->viaTable('user_business', ['businessId' => 'id']);
    }

    public function getActiveUsersCount(): int
    {
        return $this->getUsers()
            ->andWhere(['status' => User::STATUS_ACTIVE])
            ->count();
    }

    /**
     * @return ActiveQuery
     */
    public function getActivePlans()
    {
        return $this
            ->hasMany(SubscriptionPlan::className(), ['id' => 'planId'])
            ->viaTable('subscription', ['businessId' => 'id'])->where(['subscription.isActive' => 1]);
    }


    public function getTotalQuotations(): int
    {
        return $this->getQuotations()->count();
    }

    public function getActiveSubscription()
    {
        return Subscription::find()->alias('s')
            ->innerJoin('subscription_plan sp', 'sp.id = s.planId')
            ->where(['s.businessId' => $this->id])
            ->andWhere(['sp.type' => PlanType::BASE])
            ->andWhere('s.isActive = 1')
            ->orderBy('s.id DESC')
            ->limit(1)
            ->one();
    }

    public function getLastSubscription()
    {
        return Subscription::find()->alias('s')
            ->innerJoin('subscription_plan sp', 'sp.id = s.planId')
            ->where(['s.businessId' => $this->id])
            ->andWhere(['s.planId' => $this->lastPlanId])
            ->andWhere(['sp.type' => PlanType::BASE])
            ->andWhere('s.isActive = 0')
            ->orderBy('s.id DESC')
            ->limit(1)
            ->one();
    }

    public function getActiveSubscriptionByGroup($group)
    {
        if ($group === null) {
            return null;
        }

        return Subscription::find()->alias('s')
            ->innerJoin('subscription_plan sp', 'sp.id = s.planId')
            ->where(['s.businessId' => $this->id])
            ->andWhere(['sp.group' => $group])
            ->andWhere('s.isActive = 1')
            ->orderBy('s.id DESC')
            ->limit(1)
            ->one();
    }


    /**
     * @return ActiveQuery
     */
    public function getActiveSubscriptions()
    {
        return $this->hasMany(Subscription::className(), ['businessId' => 'id'])->andWhere(['isActive' => 1]);
    }

    /**
     * @param $planId
     * @return ActiveRecord|array
     */
    public function getActiveSubscriptionByPlanId($planId)
    {
        return $this->getActiveSubscriptions()->andWhere(['planId' => $planId])->one();
    }

    public function getActivePlanIds()
    {
        if (empty($this->activePlanIds)) {
            $activePlans = $this->getActiveSubscriptions()->select('planId')->all();
            $this->activePlanIds = ArrayHelper::getColumn($activePlans, 'planId');
        }
        return $this->activePlanIds;
    }

    /**
     * @param SubscriptionPlan $plan
     * @return void
     */
    public function subscribeToPlan($plan)
    {
        $features = $this->getPlanFeatures(false);
        if (empty($features)) {
            $this->features = null;
        } else {
            $this->features = implode(",", $features);
        }
        if ($plan->type === PlanType::BASE) {
            $this->planId = $plan->id;
            $this->isPremium = true;
        }
        if ($plan->type === PlanType::MULTI_USER) {
            $this->isMultiuser = 1;
        }
        $this->save(false);
        $this->processPlanSettings($plan); // Process Plan settings.
    }

    public function unsubscribeToPlan($plan)
    {
        $features = $this->getPlanFeatures(false);
        if (empty($features)) {
            $this->features = null;
        } else {
            $this->features = implode(",", $features);
        }
        $activeSubscription = $this->getActiveSubscription();
        if ($activeSubscription) {
            $this->isPremium = 1;
            $this->planId = $activeSubscription->planId;
        } else {
            $this->isPremium = 0;
            $this->planId = null;
        }
        if ($plan->type === PlanType::MULTI_USER) {
            $this->isMultiuser = 0;
        }
        $this->save(false);
        $this->processPlanSettings($plan, reset: true); // Process Plan settings.
    }


    /**
     * @param SubscriptionPlan $plan
     * @param bool $reset
     * @return void
     */
    private function processPlanSettings($plan, $reset = false)
    {
        if (empty($plan?->settings)) {
            return;
        }
        try {
            $settings = json_decode($plan->settings, true, 512, JSON_THROW_ON_ERROR);
        } catch (JsonException $e) {
            Yii::error($e);
        }
        if ($reset){
            $businessFields = $settings[Key::resetBusinessFields] ?? [];
        }else{
            $businessFields = $settings[Key::businessFields] ?? [];
        }
        foreach ($businessFields as $key => $val) {
            $this->$key = $val;
            $this->save();
        }
        $this->refresh();
        $businessSettings = $settings[Key::BusinessSettings] ?? [];
        foreach ($businessSettings as $key => $objSetting) {
            $group = $objSetting['group'];
            $value = $reset ? $objSetting['resetValue'] : $objSetting['value'];
            $businessSetting = BusinessSettings::findByGroupAndKey($group, $key, $this->id);
            if ($businessSetting === null) {
                $businessSetting = new BusinessSettings();
                $businessSetting->businessId = $this->id;
                $defaultSetting = $this->defaultSettings($group, $key);
                if (empty($defaultSetting)) {
                    Yii::error("Invalid Business Settings found for Key {$key}"); // when settings not found.
                    return;
                }
                $businessSetting->setAttributes($defaultSetting);
            }
            $businessSetting->value = (string)$value;
            $businessSetting->save(false);
            //   logError("Business Settings Update found ::", $businessSetting);
        }
    }

    public function isSubscribedToPlan($planId)
    {
        return in_array($planId, $this->getActivePlanIds(), true);
    }

    public function getPlanFeatures($useCache = true)
    {
        if (empty($this->_planFeatures) || !$useCache) {
            $this->_planFeatures = [];
            $activeSubscriptions = $this->activeSubscriptions;
            foreach ($activeSubscriptions as $subscription) {
                $features = explode(",", $subscription->plan->features);
                $this->_planFeatures[] = $features;
            }
            $this->_planFeatures = array_merge(...$this->_planFeatures);
        }
        return $this->_planFeatures;
    }

    public function hasAccess($feature): bool
    {
        return in_array($feature, $this->getPlanFeatures(), true);
    }


    public function settings($group = null)
    {
        if (empty($this->_settings)) {
            $business = $this;
            if ($business === null) {
                return [];
            }
            DefaultBusinessSettings::setBusiness($this);
            $this->_settings[Key::GROUP_CUSTOM] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_CUSTOM);
            $this->_settings[Key::GROUP_APP] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_APP);
            $this->_settings[Key::GROUP_QUOTATION] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_QUOTATION);
            $this->_settings[Key::GROUP_PURCHASE_ORDER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_PURCHASE_ORDER);
            $this->_settings[Key::GROUP_PROFORMA_INVOICE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_PROFORMA_INVOICE);
            $this->_settings[Key::GROUP_DELIVERY_NOTE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_DELIVERY_NOTE);
            $this->_settings[Key::GROUP_BUDGET] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_BUDGET);
            $this->_settings[Key::GROUP_INVOICE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_INVOICE);
            $this->_settings[Key::GROUP_RECEIPT] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_RECEIPT);
            $this->_settings[Key::GROUP_MULTI_USER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_MULTI_USER);
            $this->_settings[Key::GROUP_SALES_USER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_SALES_USER);
            $businessSettings = $business->businessSettings;
            if ($businessSettings) {
                foreach ($businessSettings as $businessSetting) {
                    $value = $businessSetting->getValue();
                    $this->_settings[$businessSetting->group][$businessSetting->key] = $value;
                }
            }
        }
        if ($group) {
            return $this->_settings[$group];
        }
        return $this->_settings;
    }


    public function config($group, $key)
    {
        $settings = $this->settings($group);
        return $settings[$key];
    }

    public function defaultSettings($group, $key = null)
    {
        DefaultBusinessSettings::setBusiness($this);
        if ($key) {
            return DefaultBusinessSettings::defaultSettings($group, $key);
        }
        return DefaultBusinessSettings::defaultSettings($group);
    }

    public static function getSettings($group = null): array
    {
        if (empty(self::$settings)) {
            $business = self::getBusiness();
            if ($business === null) {
                return [];
            }

            DefaultBusinessSettings::setBusiness($business);
            self::$settings[Key::GROUP_CUSTOM] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_CUSTOM);
            self::$settings[Key::GROUP_APP] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_APP);
            self::$settings[Key::GROUP_QUOTATION] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_QUOTATION);
            self::$settings[Key::GROUP_PURCHASE_ORDER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_PURCHASE_ORDER);
            self::$settings[Key::GROUP_PROFORMA_INVOICE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_PROFORMA_INVOICE);
            self::$settings[Key::GROUP_DELIVERY_NOTE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_DELIVERY_NOTE);
            self::$settings[Key::GROUP_BUDGET] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_BUDGET);
            self::$settings[Key::GROUP_INVOICE] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_INVOICE);
            self::$settings[Key::GROUP_SALES_USER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_SALES_USER);
            self::$settings[Key::GROUP_RECEIPT] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_RECEIPT);
            self::$settings[Key::GROUP_MULTI_USER] = DefaultBusinessSettings::getDefaultSettings(Key::GROUP_MULTI_USER);

            $businessSettings = $business->businessSettings; // Get business settings from DB
            if ($businessSettings) {
                foreach ($businessSettings as $businessSetting) {
                    $value = $businessSetting->getValue();
                    self::$settings[$businessSetting->group][$businessSetting->key] = $value;
                }
            }
        }
        if ($group) {
            return self::$settings[$group];
        }
        return self::$settings;
    }

    public static function getConfig($group, $key)
    {
        self::$settings = self::getSettings();
        return self::$settings[$group][$key] ?? null;
    }

    public function updateRegion($regionCode)
    {
        $this->regionCode = $regionCode;
        $this->save(false);
        return BusinessSettings::UpdateRegion($this, $regionCode);
    }

    public function beforeSave($insert)
    {
        if (!empty($this->otherCategory)) {
            $this->categoryId = null;
        }

        if ($insert) {
            $this->setupTemplateIds();
        }
        return parent::beforeSave($insert);
    }

    public function beforeUpdate($event): void
    {
        if ($this->isAttributeChanged('planId') && !empty($this->oldAttributes['planId'])) {
            // PlanId has changed - Store old PlanId into lastPlanId
            $this->lastPlanId = $this->oldAttributes['planId'];
        }
    }

    /**
     * @param bool $insert
     * @param array $changedAttributes
     */
    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            $businessStats = new BusinessStats();
            $businessStats->businessId = $this->id;
            $businessStats->save();
            $userBusiness = new UserBusiness();
            $userBusiness->userId = $this->ownerId;
            $userBusiness->businessId = $this->id;
            $userBusiness->isOwner = 1;
            $userBusiness->save();
            BusinessSettings::generateNewSettings($this);
        }
        FileManager::saveBusinessFile($this);
        FileManager::saveBusinessSignFile($this);
        FileManager::saveBusinessHeaderFile($this);
        FileManager::saveBusinessFooterFile($this);
        FileManager::saveBusinessUpiQrFile($this);
        parent::afterSave($insert, $changedAttributes);
    }

    public function getFullAddress()
    {
        $addressArray = [$this->addressLine1, $this->addressLine2, $this->addressLine3];
        $addressArray = array_filter($addressArray);
        return implode(', ', $addressArray);
    }

    public function hasPermssion($permission)
    {
        // TODO implement business permission flow
    }

    public static function findByOwnerId(int $userId)
    {
        return static::findOne(['ownerId' => $userId]);
    }

    public function isGstApplicable($groupKey)
    {
        $taxSetting = $this->config($groupKey, Key::TAX_SETTINGS) ?? Key::NO_TAX;
        return $this->regionCode === "IN" && !empty($this->taxNumber) && $taxSetting !== Key::NO_TAX;
    }

    public function canGenerateQuotation()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::QUOTATION)) {
            return $this->businessStats->quotationFreeUsageLimit > 0;
        }
        return true;
    }

    public function canGeneratePurchaseOrder()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::PURCHASE_ORDER)) {
            return $this->businessStats->poFreeUsageLimit > 0;
        }
        return true;
    }

    public function canGenerateInvoice()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::INVOICE)) {
            return $this->businessStats->invoiceFreeUsageLimit > 0;
        }
        return true; // always return true for Customized app!
    }

    public function canGenerateProformaInvoice()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::PROFORMA_INVOICE)) {
            return $this->businessStats->piFreeUsageLimit > 0;
        }
        return true; // always return true for Customized app!
    }


    public function canGenerateBudget()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::BUDGET)) {
            return $this->businessStats->budgetFreeUsageLimit > 0;
        }
        return true; // always return true for Customized app!
    }

    public function canGenerateDeliveryNote()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::DELIVERY_NOTE)) {
            return $this->businessStats->dnFreeUsageLimit > 0;
        }
        return true; // always return true for Customized app!
    }

    public function canAddNewUser()
    {
        if (!$this->isMultiuser) {
            return false;
        }
        $totalUserLimit = $this->config(Key::GROUP_APP, Key::TOTAL_USER_LIMIT);
        $userCount = $this->businessStats->userCount;
        return $userCount < $totalUserLimit;
    }

    public function canActivateUser()
    {
        if (!$this->isMultiuser) {
            return false;
        }
        $totalUserLimit = $this->config(Key::GROUP_APP, Key::TOTAL_USER_LIMIT);
        $userCount = $this->activeUsersCount;
        return $userCount < $totalUserLimit;
    }

    public function getActiveUserCount(): int
    {
        return User::find()
            ->viaTable('user_business', ['businessId' => 'id'])
            ->andWhere(['status' => User::STATUS_ACTIVE])
            ->count();
    }

    public function canAddNewProduct()
    {
        $totalProductLimit = $this->config(Key::GROUP_APP, Key::TOTAL_PRODUCT_LIMIT);
        $productCount = $this->businessStats->productCount;
        return $productCount < $totalProductLimit;
    }

    public function canAddNewProductImage()
    {
        $totalProductImageLimit = $this->config(Key::GROUP_APP, Key::TOTAL_PRODUCT_IMAGE_LIMIT);
        $productImageCount = $this->businessStats->productImageCount;
        return $productImageCount < $totalProductImageLimit;
    }

    public function canAddNewAttachment($type)
    {
        $limitKey = match ($type) {
            AttachmentType::LINK => Key::TOTAL_LINK_ATTACHMENT_LIMIT,
            AttachmentType::PDF => Key::TOTAL_PDF_ATTACHMENT_LIMIT,
            AttachmentType::IMAGE => Key::TOTAL_IMAGE_ATTACHMENT_LIMIT,

        };
        $totalLimit = $this->config(Key::GROUP_APP, $limitKey);
        $attachmentCount = $this->getAttachments()->where(['type' => $type])->count();
        return $attachmentCount < $totalLimit;
    }

    public function getTemplateId($group)
    {
        $this->setupTemplateIds();

        switch ($group) {
            case Key::GROUP_QUOTATION:
                return $this->quotationTemplateId;
            case Key::GROUP_INVOICE:
                return $this->invoiceTemplateId;
            case Key::GROUP_PROFORMA_INVOICE:
                return $this->piTemplateId;
            case Key::GROUP_PURCHASE_ORDER:
                return $this->poTemplateId;
            case Key::GROUP_DELIVERY_NOTE:
                return $this->dnTemplateId;
            case Key::GROUP_BUDGET:
                return $this->budgetTemplateId;
            case Key::GROUP_RECEIPT:
                return $this->receiptTemplateId;
        }

        return null;
    }

    /**
     * @return void
     */
    public function setupTemplateIds(): void
    {
        if (empty($this->quotationTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_QUOTATION_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_QUOTATION, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_QUOTATION);
            }
            if ($template) {
                $this->quotationTemplateId = $template->id;
            }
        }

        if (empty($this->poTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_PO_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_PURCHASE_ORDER, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_PURCHASE_ORDER);
            }
            if ($template) {
                $this->poTemplateId = $template->id;
            }
        }

        if (empty($this->invoiceTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_INVOICE_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_INVOICE, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_INVOICE);
            }
            if ($template) {
                $this->invoiceTemplateId = $template->id;
            }
        }

        if (empty($this->piTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_PROFORMA_INVOICE_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_PROFORMA_INVOICE, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_PROFORMA_INVOICE);
            }
            if ($template) {
                $this->piTemplateId = $template->id;
            }
        }

        if (empty($this->dnTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_DELIVERY_NOTE_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_DELIVERY_NOTE, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_DELIVERY_NOTE);
            }
            if ($template) {
                $this->dnTemplateId = $template->id;
            }
        }

        if (empty($this->receiptTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_RECEIPT_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_QUOTATION, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_RECEIPT);

            }
            if ($template) {
                $this->receiptTemplateId = $template->id;
            }
        }


        if (empty($this->budgetTemplateId)) {
            $defaultTemplateSlug = AppSettings::getValueByKey(Key::DEFAULT_BUDGET_TEMPLATE_SLUG);
            $template = Template::findByTypeAndSlug(Key::GROUP_BUDGET, $defaultTemplateSlug);
            if ($template === null) {
                $template = Template::findDefaultByType(Key::GROUP_BUDGET);
            }
            if ($template) {
                $this->budgetTemplateId = $template->id;
            }
        }
        if ($this->id) {
            $this->save(false);
        }
    }

    public function checkAndSendUsageLimitNotification()
    {
        $usageCount = $this->businessStats->totalUsageCount;
        $forceLimit = env(Key::FORCE_SUBSCRIPTION_COUNT);
        $usageLimitThreshold = env(Key::usageLimitThreshold);
        $notificationLimit = $forceLimit - $usageLimitThreshold; // 80-0 = 80
        if ($this->isPremium || $usageCount < $notificationLimit) {
            return;
        }
        // Set the timezone to IST (Indian Standard Time)
        date_default_timezone_set('Asia/Kolkata');
        $todayDate = date("d.m.Y");

        $subject = 'Usage limit Reached!';
        if ($usageCount >= $forceLimit) {
            $subject = "Final Usage limit reached!";
        }
        $appName = Yii::$app->name;
        $composeMail = Yii::$app
            ->mailer
            ->compose(
                ['html' => 'notifications/usage-limit-notification'],
                ['user' => $this->owner, 'business' => $this],
            )
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']])
            ->setTo(Yii::$app->params['adminEmail'])
            ->setSubject("$subject - $todayDate | $appName");
        $isSent = $composeMail->send();
        if ($isSent !== true) {
            Yii::error("Error occurred in sending subscription email");
        }
    }

    public function canGenerateReceipt()
    {
        if (!$this->plan || !$this->hasAccess(PlanFeature::RECEIPT)) {
            return $this->businessStats->receiptFreeUsageLimit > 0;
        }
        return true;
    }

    public function handleMultiUserPlanRemoval(Subscription $subscription): void
    {
        // Change access token of all users of this business
        $users = $this->users;
        foreach ($users as $user) {
            if ($user->id == $this->ownerId) {
                $user->removeAccessTokenAndSave();
                continue;
            }
            $user->deActivate();
        }
    }

    // create function to getUsageLevel based on totalUsageCount.
    public function getUsageLevel()
    {
        if ($this->businessStats->getUsageCount() > 100) {
            return 6;
        }
        return ceil($this->businessStats->getUsageCount() / 20);
    }

    public function isPdfStorageEnabled()
    {
        if (IS_PREMIUM_APP)
            return true;
        $isPdfStorageEnabled = $this->config(Key::GROUP_APP, Key::IS_PDF_STORAGE_ENABLED);
        return ($isPdfStorageEnabled || $this->isMultiuser);
    }
}