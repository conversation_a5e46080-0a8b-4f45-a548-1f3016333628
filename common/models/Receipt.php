<?php

namespace common\models;

use common\helpers\FileManager;
use common\models\base\ReceiptBase;
use common\models\enum\DefaultBusinessSettings;
use common\models\enum\Key;
use common\models\enum\PlanFeature;
use common\models\enum\ReceiptStatus;
use common\services\EmailClient;
use common\services\PdfService;
use JsonException;
use Yii;
use yii\base\Exception;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\symfonymailer\Mailer;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

/**
 * This is the model class for table "receipt".
 * @property-read mixed $pdfFileDataUrl
 * @property-read mixed $otherInfoData
 * @property-read mixed $pdfFilePath
 * @property string $groupKey
 *
 */
class Receipt extends ReceiptBase
{

    public $DateRange;
    public $companyName;
    public $pdfFileBase64;
    public $pdfHtmlContent;
    public $pdfOutputData;
    public $pdfFile;

    /**
     * @return ActiveQuery
     */
    public
    static function findActive()
    {
        return parent::find()->andWhere(['isDeleted' => 0]);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getReceiptItems()
    {
        return $this->hasMany(ReceiptItems::className(), ['receiptId' => 'id'])->orderBy(['order' => SORT_ASC, 'id' => SORT_ASC]);
    }

    /**
     * @param $request
     * @param User $salesUser
     * @return Receipt
     * @throws Exception
     */
    public
    static function CreateOrUpdate($request, User $salesUser): Receipt
    {
        $quoteId = $request->post('id');
        $newReceipt = Receipt::findByPk($quoteId);

        if ($quoteId && $newReceipt == null) {
            throw new Exception('Invalid receipt id', 101);
        }
        $isNewRecord = false;
        $status = ReceiptStatus::REVISED;

        if ($newReceipt == null) {
            $newReceipt = new Receipt();
            $isNewRecord = true;
            $status = ReceiptStatus::NEW;
        }

        $isItemizedReceipt = toBool($request->post(Key::isItemizedReceipt));

        $newReceipt->templateId = $salesUser->business->getTemplateId(Key::GROUP_RECEIPT);

        $newReceipt->setAttributes($_POST);  // load all attributes (in new User model)
        $newReceipt->createdById = $request->post('createdById', $salesUser->id);

        $receiptDate = $request->post('receiptDate');
        if ($receiptDate) {
            $newReceipt->receiptDate = date("Y-m-d", strtotime($receiptDate));
        }

        $newReceipt->status = $request->post('status', $status);

        if (!$newReceipt->validate()) {

            $errors = $newReceipt->getErrorSummary(true)[0];
            throw new Exception($errors, 101);
        }
        if ($newReceipt->save()) { // save new receipt
            if ($isItemizedReceipt) {
                $jsonReceiptItems = $request->post('receiptItems');
                if ($jsonReceiptItems == null && $isNewRecord) {
                    throw new Exception('Please send receipt item details', 101);
                }
                $receiptItems = Json::decode($jsonReceiptItems);
                $errorMsg = $newReceipt->saveReceiptItems($receiptItems, $isNewRecord);

                if ($errorMsg) {
                    throw new Exception($errorMsg, 101);
                }
            }
        }

        if (!$newReceipt->generatePDF() && $newReceipt->getPdfFileDataUrl() == null) {
            $errorMsg = "There are issues in generating PDF File on server! Please try again later!";
            throw new Exception($errorMsg, 101);
        }

        return $newReceipt;
    }

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public
    static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * @param $receiptItems
     * @param $isNewRecord
     * @return false|mixed
     */
    public
    function saveReceiptItems($receiptItems, $isNewRecord)
    {
        $updatedIds = [];
        foreach ($receiptItems as $index => $item) {
            $newItem = new ReceiptItems();
            if (!empty($item['id'])) {
                $newItem->id  = $item['id'];
                $newItem->setIsNewRecord(false);
            }

            $newItem->setAttributes($item);
            $newItem->receiptId = $this->id;
            $newItem->order = $index; // Set the order based on array index
            $newItem->updatedAt = new Expression('NOW()');
            if (!$newItem->save()) {
                Yii::error(print_r($newItem->getErrors(), true));
                if ($isNewRecord) {
                    Receipt::deleteAll(['id' => $this->id]);
//                    $this->delete(); // remove receipt object - if new record and receipt item has an error!
                }
                echo $newItem->getErrorSummary(true)[0];
                return $newItem->getErrorSummary(true)[0];
            }
            $updatedIds[] = $newItem->id;
        }

        if (!$isNewRecord) {
            $this->removeReceiptItems($updatedIds);
        }

        return false; // no errors
    }

    public
    function removeReceiptItems($updatedIds)
    {
        // remove receipt items by id which are not available in updatedIds
        if (!empty($updatedIds)) {
            ReceiptItems::deleteAll(['AND', ['receiptId' => $this->id], ['NOT IN', 'id', $updatedIds]]);
        }
    }

    public function getPdfFileDataUrl()
    {
        if ($this->pdfFileUrl) {
            return $this->pdfFileUrl;
        }
        return Url::to(['receipt/view-pdf', 'id' => $this->id]);
    }

    public
    function generatePDF($generateFileName = true)
    {
        // Use the PdfService to generate the PDF
        $pdfService = PdfService::getInstance();
        return $pdfService->generateReceiptPdf($this, $generateFileName);
    }

    /**
     * @param string $relPath
     * @return string|null
     */
    public
    function getFilePath($relPath = '')
    {
        $relativePath = sprintf(RECEIPT_DIR, $this->businessId, date('Y'), date('m')) . $relPath;
        return FileManager::getFilePath($relativePath);
    }

    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    public
    function rules()
    {

        return ArrayHelper::merge(parent::rules(),
            [
                ['status', 'in', 'range' => ReceiptStatus::getArray()],
                ['taxPercentage', 'number', 'min' => 0, 'max' => 100],
            ]);

    }

    public function getSettingsData()
    {
        $defaultSettings = Business::getSettings(Key::GROUP_RECEIPT);
        if (!empty($this->receiptSettings)) {
            try {
                $receiptSettings = json_decode($this->receiptSettings, true, 512, JSON_THROW_ON_ERROR);
                return array_merge($defaultSettings, $receiptSettings);
            } catch (JsonException $e) {
                Yii::error($e->getMessage() . "\n" . $e->getTraceAsString());
            }
        }
        return $defaultSettings;
    }

    public function getOtherInfoData()
    {
        if ($this->otherInfo) {
            try {
                return json_decode($this->otherInfo, false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $this->otherInfo;
    }

    /**
     * @return bool
     */
    public
    function beforeSoftDelete()
    {
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        return true;
    }

    public
    function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            $this->generateReceiptNumber();
        }
        $this->saveSettings();
        if ($this->status == ReceiptStatus::NEW) {
            $this->statusText = $this->createdBy->getName();
            $this->updateUsage();
        }

        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    public
    function generateReceiptNumber()
    {
        if ($this->id && empty($this->receiptNumber)) {
            $nextNumber = $this->business->receiptNumber;
            $receiptPrefix = $this->business->receiptPrefix;
            $this->receiptNumber = sprintf("%s%s", $receiptPrefix, $nextNumber);
            $this->updateAttributes(['receiptNumber']);
            $this->business->receiptNumber++; // increment new receipt number!
            $this->business->updateAttributes(['receiptNumber']);
            return true;
        }
        return false;
    }

    public
    function detail()
    {
        $findQuery = self::find();
        $findQuery->alias('r');
        $findQuery->select(['r.*', 'c.name as customer_name',
            'u.name as salesUser_name',
        ]);

        $findQuery->andWhere(['r.id' => $this->id]);
        $findQuery->joinWith('customer c');
        $findQuery->joinWith('createdBy u');
        $findQuery->with('receiptItems')->asArray();
        return $findQuery->one();
    }


    /**
     * Sends confirmation email to user
     * @param User $user user model to with email should be send
     * @return bool whether the email was sent
     */
    public
    function sendMailToCustomer()
    {
        if ($this->getPdfFilePath() == null) {
            return false;
        }

        /** @var Mailer $composeMail */
        $composeMail = EmailClient::getInstance()->getSmtpMailer()
            ->compose(
                ['html' => 'receipt-html'],
                ['receipt' => $this, 'customer' => $this->customer]
            )
            ->attach($this->getPdfFilePath())
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']]);

        $composeMail
            ->setBcc(Yii::$app->params['supportEmail']);

        $isSent = $composeMail
            ->setTo($this->customer->email)
            ->setSubject('You have received new receipt from ' . Yii::$app->name)
            ->send();

        if ($isSent !== true) {
            Yii::error($composeMail->getErrors());
        }

        return $isSent;
    }

    /**
     * @return $path|null
     */
    public
    function getPdfFilePath()
    {
        return FileManager::getFilePathFromURL($this->pdfFileUrl);
    }

    public function remove()
    {
        return $this->softDelete();
    }

    private function updateUsage()
    {
        $user = $this->business->owner;
        $user->businessStats->incrementReceiptCount();
        if (!$this->business->hasAccess(PlanFeature::RECEIPT)) {
            $user->businessStats->decrementReceiptFreeUsageLimit();
        }
    }

    public function getGroupKey()
    {
        return Key::GROUP_RECEIPT;
    }


    public function saveSettings()
    {
        Business::setBusiness($this->businessId);

        $groupKey = $this->getGroupKey();
        $receiptSettings = Business::getSettings($groupKey);
        if ($receiptSettings) {
            try {
                $currentSettings = $this->getSettingsData($groupKey);
                $overwriteSettings = DefaultBusinessSettings::overwriteSettings($groupKey);
                foreach ($overwriteSettings as $settingKey) {
                    if (is_string($settingKey) && isset($currentSettings[$settingKey])) {
                        $receiptSettings[$settingKey] = $currentSettings[$settingKey];
                    }
                }
                $preferences = DefaultBusinessSettings::preferenceSettings($groupKey);
                foreach ($preferences as $settingKey) {
                    unset($receiptSettings[$settingKey]);
                }
                $this->receiptSettings = json_encode($receiptSettings, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        $this->updateAttributes(['receiptSettings']);
    }
}