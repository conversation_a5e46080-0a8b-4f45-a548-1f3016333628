<?php

namespace common\models;

use common\models\base\UserDeviceBase;
use common\models\query\UserDeviceQuery;

class UserDevice extends UserDeviceBase
{
    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }


    /**
     * @param $userId
     * @param $userType
     * @return UserDevice[]
     */
    public static function findByUserId($userId, $userType = 'user')
    {
        return static::findAll(['userId' => $userId, 'userType' => $userType]);
    }

    public static function findByDeviceId($deviceId, $userType = 'user')
    {
        return static::findOne(['uuid' => $deviceId, 'userType' => $userType]);
    }

    public static function find()
    {
        return new UserDeviceQuery(get_called_class());
    }


}