<?php

namespace common\models;

use borales\extensions\phoneInput\PhoneInputBehavior;
use borales\extensions\phoneInput\PhoneInputValidator;
use common\helpers\FileManager;
use common\models\base\UserBase;
use common\models\enum\Key;
use common\models\enum\NotificationType;
use common\models\enum\PlanFeature;
use common\models\enum\UserStatus;
use common\services\EmailService;
use Exception;
use Yii;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\web\IdentityInterface;
use yii\web\UploadedFile;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

/**
 * This is the model class for table "user".
 *
 * @property Business $business
 * @property UserBusiness $userBusiness
 * @property UserSocialProvider[] $userSocialProviders
 * @property Subscription $subscription
 * @property BusinessStats $businessStats
 * @property boolean $isProUser
 */
class User extends UserBase implements IdentityInterface
{
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_BLOCKED = 'blocked';

    // Signup SCENARIO
    const SCENARIO_SIGNUP = 'signup';

    const SALES_Person = 'sales_person';
    const SALES_Manager = 'sales_manager';
    const SALES_Director = 'sales_director';
    const INVENTORY_MANAGER = 'inventory_manager';
    const SCENARIO_VALIDATE_PHONE = "validate_phone";
    public static $fields = [];
    public $confirm_password;
    public $last_method_of_payment;
    public $DateRange;
    /**
     * @var UploadedFile
     */
    public $profileImage;
    /**
     * @var UploadedFile
     */
    public $signatureImgFile;
    /**
     * @var mixed|null
     */

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findBySocialProviderId($providerId)
    {
        $socialProvider = UserSocialProvider::findOne(['providerId' => $providerId]);
        return $socialProvider != null ? $socialProvider->user : null;
    }

    /**
     * Finds user by email
     *
     * @param string $email
     * @return static|null
     */
    public static function findByEmail($email)
    {
        return static::findOne(['email' => $email]);
    }

    /**
     * Finds user by email
     *
     * @param string $email
     * @return static|null
     */
    public static function findActiveByEmail($email)
    {
        return static::findOne(['email' => $email, 'status' => self::STATUS_ACTIVE]);
    }

    public static function findByAdminEmail($email)
    {
        return static::findOne(['email' => $email, 'status' => self::STATUS_ACTIVE, 'isAdmin' => 1]);
    }

    public static function findByActiveUser($id)
    {
        return static::findOne(['id' => $id, 'status' => self::STATUS_ACTIVE]);
    }

    /**
     * @inheritdoc
     */
    public static function findIdentity($id)
    {
        return static::findOne(['id' => $id, 'status' => self::STATUS_ACTIVE]);
    }

    /**
     * @inheritdoc
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(['access_token' => $token]);
    }

    /**
     * Finds user by password reset token
     *
     * @param string $token password reset token
     * @return static|null
     */
    public static function findByPasswordResetToken($token)
    {
        if (!static::isPasswordResetTokenValid($token)) {
            return null;
        }
        return static::findOne([
            'password_reset_token' => $token,
            'status' => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Finds out if password reset token is valid
     *
     * @param string $token password reset token
     * @return bool
     */
    public static function isPasswordResetTokenValid($token)
    {
        if (empty($token)) {
            return false;
        }
        $timestamp = (int)substr($token, strrpos($token, '_') + 1);
        $expire = Yii::$app->params['user.passwordResetTokenExpire'];
        return $timestamp + $expire >= time();
    }

    public static function checkPasswordStrength($password)
    {
        $pattern = '/^.{6,}$/'; //min 6 char
        if (preg_match($pattern, $password)) {
            return true;
        }
        return false;

    }

    /**
     * @param $password
     * @return string
     * @throws Exception
     */
    public static function generatePassword($password)
    {
        return Yii::$app->security->generatePasswordHash($password);
    }

    /**
     * @return string
     * @throws Exception
     */
    public static function generateRandomAuth()
    {
        return Yii::$app->security->generateRandomString();
    }

    public function scenarios()
    {
        return parent::scenarios();
    }

    public function behaviors()
    {
        return [
            [
                'class' => PhoneInputBehavior::className(),
                'phoneAttribute' => 'phoneNumber',
            ],
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getUserBusiness()
    {
        return $this->hasOne(UserBusiness::className(), ['userId' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getUserSocialProviders()
    {
        return $this->hasMany(UserSocialProvider::className(), ['userId' => 'id']);
    }

    /**
     * @inheritdoc
     */

    public function rules()
    {
        return ArrayHelper::merge(parent::rules(),
            [
                //['confirm_password','required'],
                ['email', 'email'],
                //  ['phoneNumber', 'integer'],
                ['confirm_password', 'compare', 'compareAttribute' => 'password_hash'],
                ['password_hash', 'required', 'on' => self::SCENARIO_SIGNUP],
                ['status', 'default', 'value' => self::STATUS_INACTIVE],
                ['status', 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_BLOCKED, self::STATUS_INACTIVE]],
                [['country', 'state', 'city'], 'default', 'value' => '-'],
//                [['phoneNumber'], 'required'],
                ['designation', 'default', 'value' => self::SALES_Person],
                ['designation', 'in', 'range' => [self::SALES_Person, self::SALES_Manager, self::SALES_Director, self::INVENTORY_MANAGER]],

                [['profileImage'], 'file', 'checkExtensionByMimeType' => false, 'skipOnEmpty' => true, 'extensions' => 'png, jpg, jpeg'],
                [['signatureImgFile'], 'file', 'checkExtensionByMimeType' => false, 'skipOnEmpty' => true, 'extensions' => 'png, jpg, jpeg'],

                [['email'], 'filter', 'filter' => 'trim', 'skipOnEmpty' => true],

                ['pincode', 'match', 'pattern' => "/^[1-9]{1}[0-9]{2}\s{0,1}[0-9]{3}$/"],
                [['phoneNumber'], PhoneInputValidator::class, 'on' => self::SCENARIO_VALIDATE_PHONE],
            ]);
    }

    public function attributeLabels()
    {
        return ArrayHelper::merge(parent::attributeLabels(), [
            'email' => 'Email',
            'confirm_password' => 'Confirm Password',
            'password_hash' => 'Password',
        ]);
    }

    /**
     * @return boolean
     */
    public function isActive()
    {
        return $this->status == self::STATUS_ACTIVE;
    }

    /**
     * @return boolean
     */
    public function isRemoved()
    {
        return (bool)$this->isDeleted;
    }

    /**
     * Generates new password reset token
     */
    public function generateOTPCode()
    {
        $this->otpCode = random_int(100000, 999999); // a random 6 digit number
        $unixTime = time() + Yii::$app->params['user.OTPCodeExpire'];
        $this->otpExpiry = date('Y-m-d H:i:s', $unixTime); // To expire otp after 10 minutes
    }

    /**
     * Validates OTP Code
     *
     * @param string $otpCode password to validate
     * @return bool if OTP provided is valid for current user
     */
    public function isOTPCodeValid($otpCode)
    {
        if (empty($otpCode)) {
            return false;
        }

        return ($this->otpCode == $otpCode && strtotime($this->otpExpiry) >= time());
    }

    /**
     * Activate user
     * @return bool
     */
    public function activate()
    {
        $this->status = self::STATUS_ACTIVE;
        $this->isDeleted = 0;
        $this->removeOTPCode();
        return $this->save(false);
    }

    /**
     * Activate user
     * @return bool
     */
    public function deActivate()
    {
        $this->status = self::STATUS_BLOCKED;
        $this->isDeleted = 1;
        $this->removeOTPCode();
        $this->removeAccessToken();
        return $this->save(false);
    }

    /**
     * Removes email activate token
     */
    public function removeOTPCode()
    {
        $this->otpCode = null;
        $this->otpExpiry = null;
    }

    /**
     * @inheritdoc
     */
    public function getId()
    {
        return $this->getPrimaryKey();
    }

    public function getTotalQuotations(): int
    {
        return $this->getQuotations()->count();
    }

    /**
     * @return ActiveQuery
     */
    public function getBusiness()
    {
        return $this
            ->hasOne(Business::className(), ['id' => 'businessId'])
            ->viaTable('user_business', ['userId' => 'id']);
    }

    public function validateDevice($deviceId)
    {
        $devices = UserDevice::find()->byUserId($this->id)->exceptDeviceId($deviceId)->all();
        if ($devices) {
            UserDevice::deleteAll(['and', ['userId' => $this->id], ['<>', 'deviceId', $deviceId]]);
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    public function getAccessToken()
    {
        if (empty($this->accessToken) && $this->id) {
            $this->generateAndSaveAccessToken(); // to Prevent Multiple login with same User a/c.
        }
        return $this->accessToken;
    }

    /**
     * @inheritdoc
     */
    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }

    /**
     * @inheritdoc
     */
    public function getAuthKey()
    {
        return $this->auth_key;
    }

    /**
     * Validates password
     *
     * @param string $password password to validate
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password_hash);
    }

    /**
     * Generates random password hash
     * @return bool|string
     */
    public function setRandomPassword()
    {
        try {
            $password = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 6)), 0, 6);
            $this->password_hash = Yii::$app->security->generatePasswordHash($password);
            return $password;
        } catch (Exception $exception) {
            Yii::error('Error in generating password hash!');
        }
        Yii::error('Error in generating random password!');
        return null;
    }

    /**
     * Generates password hash from password and sets it to the model
     *
     * @param string $password
     */
    public function setPassword($password)
    {
        try {
            $this->password_hash = Yii::$app->security->generatePasswordHash($password);
        } catch (Exception $exception) {
            Yii::error('Error in generating password hash!');
        }
    }

    /**
     * Generates "remember me" authentication key
     */
    public function generateAuthKey()
    {
        $this->auth_key = Yii::$app->security->generateRandomString();
    }

    /**
     * Generates "remember me" authentication key
     */
    public function generateAccessToken()
    {
        $this->accessToken = Yii::$app->security->generateRandomString();
    }

    public function generateAndSaveAccessToken()
    {
        $this->generateAccessToken();
        $this->updateAttributes(['accessToken']);
    }

    public function getName()
    {
        return trim($this->name);
    }

    /**
     * Deletes the table row corresponding to this active record.
     * @return false|int
     */
    public function remove()
    {
        return $this->softDelete();
    }

    /**
     * Sends confirmation email to user
     * @return bool whether the email was sent
     */
    public function sendOTPEmail()
    {
        $emailService = new EmailService(true);

        try {
            $send_mail = $emailService->sendUserMail(
                $this,
                'emailVerify-html',
                ['user' => $this],
                'OTP Verification'
            );
        } catch (\Exception $e) {
            Yii::error($e);
            $send_mail = '1';
        }
        return $send_mail;
    }

    public function getSubIds()
    {

        $newSubIds = [$this->id];
        $userIds = [$this->id];
        $level = 1;
        switch ($this->designation) {
            case self::SALES_Person:
                return $userIds;
            case self::SALES_Manager:
                $level = 2;
                break;
            case self::SALES_Director:
                $level = 3;
                break;
        }
        for ($i = 0; $i < $level; $i++) {
            $userIds = $this->getSubUsers($userIds);
            $newSubIds = ArrayHelper::merge($newSubIds, $userIds);
        }
        return $newSubIds;
    }

    public function getSubUsers($userIds)
    {
        $userIds = User::find()->select(['id'])->where(['in', 'parentId', $userIds])->asArray()->all();
        return ArrayHelper::getColumn($userIds, 'id');
    }

    /**
     * @param $userId {user id to check access with current user}
     * @return bool
     */
    public function hasAccess($userId)
    {
        $parentIds = self::getParentIds($userId);
        $parentIds[] = $this->id;
        return in_array($this->id, $parentIds);
    }


    public static function getFields($alias = null)
    {
        $fields = ['id', 'email', 'fromEmail', 'name', 'phoneNumber', 'addressLine1', 'addressLine2', 'addressLine3',
            'latitude', 'longitude', 'profilePic', 'signatureImg', 'designation', 'freeUsageCount', 'quotationCount', 'isContactsAllowed',
            'invFreeUsageCount', 'invoiceCount', 'planId', 'country', 'regionCode', 'state', 'city',
            'pincode', 'parentId',
            'status', 'isMigrated',
            'migratedAt', 'isAdmin', 'isPhoneVerified', 'isDeleted', 'createdAt', 'updatedAt', 'deletedAt'];

        if ($alias) {
            $fields = array_map(function ($value) use ($alias) {
                return $alias . '.' . $value;
            }, $fields);
        }
//        print_r($fields); die();
        return $fields;
    }

    public static function getParentIds($id, $parentIds = [])
    {
        /** @var User $user */
        $user = User::find()->select(['id'])->where(['id' => $id])->one();
        if ($user->parentId == 0) {
            return $parentIds;
        }
        $parentIds[] = $user->parentId;
        return self::getParentIds($user->parentId, $parentIds);
    }

    public function beforeValidate()
    {
        return parent::beforeValidate(); // TODO: Change the autogenerated stub
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->setFreeUsageLimit();
            $this->setInvFreeUsageLimit();
        }
        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        FileManager::saveUserImage($this);
        FileManager::saveUserSignFile($this);

        // Send welcome email to new users
        if ($insert && $this->email && $this->status != self::STATUS_BLOCKED) {
            try {
                $emailService = new EmailService(true);
                $emailService->sendWelcomeEmail($this);
            } catch (\Exception $e) {
                Yii::error("Failed to send welcome email to {$this->email}: " . $e->getMessage());
            }
        }

        parent::afterSave($insert, $changedAttributes);
    }

    public function getFullAddress()
    {
        $addressArray = [$this->addressLine1, $this->addressLine2, $this->addressLine3];
        $addressArray = array_filter($addressArray);
        return implode(', ', $addressArray);
    }

    public function sendEmailTempPassword($randomPassword)
    {
        $emailService = new EmailService(true);

        try {
            $send_mail = $emailService->sendUserMail(
                $this,
                'createUser-html',
                ['data' => $this, 'randomPassword' => $randomPassword],
                'Your login details'
            );
        } catch (\Exception $e) {
            Yii::error($e);
            $send_mail = '1';
        }
        return $send_mail;
    }

    /**
     * @param integer $userId
     * @param string $txtMessage
     * @param array $payloadData
     */
    public function sendNotification($txtMessage, $type = NotificationType::GENERAL, $payloadData = [])
    {
        $notification = new NotificationMessage();
        $notification->fromUserId = null;
        $notification->fromUserType = null;

        $notification->toUserId = $this->id;
        $notification->toUserType = Identity::TYPE_USER;

        $notification->title = APP_NAME;
        $notification->message = $txtMessage;
        $notification->type = $type;
        $payloadData = array_merge($payloadData, [
            'message' => $txtMessage,
            'type' => NotificationType::GENERAL,
        ]);

        $notification->payloadData = $payloadData;
        $notification->send();
    }

    /**
     * @param integer $userId
     * @param string $txtMessage
     * @param array $payloadData
     */
    public function sendUserNotification($toUserId, $txtMessage)
    {
        $notification = new NotificationMessage();
        $notification->fromUserId = $this->id;
        $notification->fromUserType = Identity::TYPE_USER;

        $notification->toUserId = $toUserId;
        $notification->toUserType = Identity::TYPE_USER;

        $notification->title = APP_NAME;
        $notification->message = $txtMessage;
        $notification->type = NotificationType::GENERAL;
        $payloadData = [
            'message' => $txtMessage,
            'type' => NotificationType::GENERAL,
        ];
        $notification->payloadData = $payloadData;

//        $adminIds = User::getAdminUserIds();
        $notification->send();
    }

    public static function getAdminUserIds()
    {
        $userIds = User::find()->select(['id'])->where(['isAdmin' => true])->asArray()->all();
        return ArrayHelper::getColumn($userIds, 'id');
    }

    public function sendNotificationToAll($txtMessage)
    {
        $notification = new NotificationMessage();
        $notification->fromUserId = $this->id;
        $notification->fromUserType = Identity::TYPE_USER;

        $notification->title = 'My Quotation App';
        $notification->message = $txtMessage;
        $notification->payloadData = ['testData' => 'Random Value'];

        if ($this->isAdmin) {
            $notification->type = NotificationType::GENERAL;
            $notification->topic = NotificationType::TOPIC_ALL_USERS;
            $notification->broadcast();
        } else {

            if ($this->designation == User::SALES_Director) {
                $userIds = ArrayHelper::merge([$this->id], $this->getSubUsers($this->id));
            } else {
                $userIds = [$this->id];
            }
            foreach ($userIds as $userId) {
                $notification = new NotificationMessage();
                $notification->fromUserId = $this->id;
                $notification->fromUserType = Identity::TYPE_USER;

                $notification->title = 'My Quotation App';
                $notification->message = $txtMessage;
                $notification->payloadData = ['testData' => 'Random Value'];

                $notification->topic = APP_ID . $userId;
                $notification->broadcast();
            }
        }
    }

    /**
     * Generates new password reset token
     */
    public function generatePasswordResetToken()
    {
        $this->password_reset_token = Yii::$app->security->generateRandomString() . '_' . time();
    }

    /**
     * Removes password reset token
     */
    public function removePasswordResetToken()
    {
        $this->password_reset_token = null;
    }

    /**
     * get active subscription for the current user!
     * @return Subscription
     */
    public function getActiveSubscription()
    {
        return Subscription::find()->byUserId($this->id)->one();
    }

    /**
     * get active subscription for the current user!
     * @return \yii\db\ActiveQuery
     */
    public function getSubscription()
    {
        return $this->hasOne(Subscription::className(), ['userId' => 'id', 'isActive' => 1]);
    }

    public function subscribeToPlan($planId)
    {
        if ($this->planId !== $planId) {
            $this->planId = $planId;
            $this->save(false);
        }
        // already subscribed!
    }

    public function beforeSoftDelete()
    {
        $this->removeSocialProviders();
        $this->removeAccessToken();
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        $this->status = UserStatus::IN_ACTIVE; // log the deletion date
        $this->email .= "-REMOVED-" . $this->id;
        return true;
    }

    public function beforeRestore()
    {
        $this->generateAccessToken();
        $this->deletedAt = null;
        $this->status = UserStatus::ACTIVE; // log the deletion date\
        return true;
    }

    private function removeSocialProviders()
    {
        UserSocialProvider::deleteAll(['userId' => $this->id]);
    }

    public function removeAccount()
    {
        return $this->softDelete();
    }

    /**
     * Removes API access token
     */
    public function removeAccessToken()
    {
        $this->accessToken = null;
    }

    public function removeAccessTokenAndSave()
    {
        $this->accessToken = null;
        $this->updateAttributes(['accessToken']);
    }

    public function getFcmLevel()
    {
        $fcmTopic = $this->fcmTopic;
        // Check if "level-" exists in the string
        if ($fcmTopic && strpos($fcmTopic, "level-") !== false) {
            // Extract the numeric part
            return substr($fcmTopic, strlen("level-"));
        }
        return null;
    }

    private function removeAllDevices()
    {
        UserDevice::deleteAll(['userId' => $this->id]);
    }

    public function unSubscribeToPlan($planId)
    {
        if ($this->planId === $planId) {
            $this->planId = null;
            $this->save(false);
        }
    }

    public function incrementQuotationCount($count = 1)
    {
        $this->quotationCount += $count;
        $this->save(false);
    }

    public function decrementQuotationCount()
    {
        if ($this->quotationCount > 0) {
            $this->quotationCount--;
            $this->save(false);
        }
    }

    public function incrementInvoiceCount($count = 1)
    {
        $this->invoiceCount += $count;
        $this->save(false);
    }

    public function decrementInvoiceCount()
    {
        if ($this->invoiceCount > 0) {
            $this->invoiceCount--;
            $this->save(false);
        }
    }

    /**
     * @return ActiveQuery
     */
    public function getBusinessStats()
    {
        return $this
            ->hasOne(BusinessStats::className(), ['businessId' => 'businessId'])
            ->viaTable('user_business', ['userId' => 'id']);
    }

    public function decrementFreeUsageCount()
    {
        if ($this->freeUsageCount > 0) {
            $this->freeUsageCount--;
            $this->save(false);
        }
    }

    public function decrementInvFreeUsageCount()
    {
        if ($this->invFreeUsageCount > 0) {
            $this->invFreeUsageCount--;
            $this->save(false);
        }
    }


    public function getIsProUser()
    {
        return (bool)$this->planId;
    }

    public function canGenerateQuotation()
    {
        if (!$this->isProUser || !$this->plan->hasAccess(PlanFeature::QUOTATION)) {
            return $this->freeUsageCount > 0;
        }
        return true;
    }

    private function setFreeUsageLimit()
    {
        if ($this->freeUsageCount !== null) {
            return;
        }
        $freeUsageSetting = AppSettings::findByKey(Key::FREE_USAGE_LIMIT);
        if ($freeUsageSetting !== null) {
            $this->freeUsageCount = $freeUsageSetting->getValue();
        } else {
            $this->freeUsageCount = 10;
        }
    }

    private function setInvFreeUsageLimit()
    {
        if ($this->invFreeUsageCount !== null) {
            return;
        }
        $freeUsageSetting = AppSettings::findByKey(Key::FREE_INVOICE_USAGE_LIMIT);
        if ($freeUsageSetting !== null) {
            $this->invFreeUsageCount = $freeUsageSetting->getValue();
        } else {
            $this->invFreeUsageCount = 5;
        }
    }

    public function getSubscriptionStatus()
    {
        $subscriptionStatus = "Inactive";
        $subscription = Subscription::find()->byUserId($this->id)->one();
        if ($subscription) {
            $subscriptionStatus = "Active";
        } else {
            $expiredCount = Subscription::find()->where(['userId' => $this->id])->andWhere(['isActive' => 0])->count();
            if ($expiredCount > 0) {
                $subscriptionStatus = "Expired";
            }
        }
        return $subscriptionStatus;
    }

    /**
     * @throws Exception
     */
    public function bindRazorpayCustomerId($customerId)
    {
        if ($this->razorpayCustomerId !== null && $this->razorpayCustomerId !== $customerId) {
            throw new Exception("User is already created on Razorpay with other id");
        }
        $this->razorpayCustomerId = $customerId;
        $this->save(false);
    }

    /**
     * @throws Exception
     */
    public function bindStripeCustomerId($customerId)
    {
        $this->stripeCustomerId = $customerId;
        $this->save(false);
    }

    public function removeStripeCustomerId()
    {
        $this->stripeCustomerId = null;
        $this->save(false);
    }


}