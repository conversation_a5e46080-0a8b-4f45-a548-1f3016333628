<?php

namespace common\models\traits;

use common\models\enum\Key;

trait BudgetSettings
{
    public static function budget_settings(SettingsContext $context): array
    {
        $group = Key::GROUP_BUDGET;
        return [
            Key::DISCOUNT_SETTINGS => [
                'key' => Key::DISCOUNT_SETTINGS,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "no_discount", // "no_discount", "per_item,per_item,on_total"
                "description" => "no_discount,per_item,on_total",
            ], // bool
            Key::TAX_RATE_ON_TOTAL => [
                'key' => Key::TAX_RATE_ON_TOTAL,
                'group' => $group,
                'type' => Key::INT,
                'value' => 18,
                "description" => "TAX_RATE_ON_TOTAL",
            ], // bool
            Key::TAX_SETTINGS => [
                'key' => Key::TAX_SETTINGS,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "per_item",
                "description" => "no_tax,per_item,on_total",
            ], // bool
            Key::IS_DISPLAY_BANK_DETAILS => [
                'key' => Key::IS_DISPLAY_BANK_DETAILS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "IS_DISPLAY_BANK_DETAILS",
            ], // bool
            Key::isDisplayUpiDetails => [
                'key' => Key::isDisplayUpiDetails,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "IS_DISPLAY_UPI_DETAILS",
            ], // bool
            Key::TOP_MESSAGE => [
                'key' => Key::TOP_MESSAGE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "Dear Sir/Mam, \n We are pleased to submit the budget as below.",
                "description" => "TOP_MESSAGE",
            ], // bool
            Key::BOTTOM_MESSAGE => [
                'key' => Key::BOTTOM_MESSAGE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "Your prompt attention to this budget is greatly appreciated, and we look forward to a successful transaction.",
                "description" => "BOTTOM_MESSAGE",
            ], // bool
            Key::IS_DISPLAY_BANK_DETAILS => [
                'key' => Key::IS_DISPLAY_BANK_DETAILS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_DISPLAY_BANK_DETAILS",
            ], // bool
            Key::IS_DISPLAY_HSN_CODE => [
                'key' => Key::IS_DISPLAY_HSN_CODE,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_DISPLAY_HSN_CODE",
            ], // bool
            KEY::IS_DISPLAY_TOTAL => [
                'key' => Key::IS_DISPLAY_TOTAL,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "should display total?",
            ], // bool
            KEY::IS_DISPLAY_NET_RATE => [
                'key' => Key::IS_DISPLAY_NET_RATE,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "should display netRate?",
            ], // bool
            Key::IS_SUBJECT_ENABLED => [
                'key' => Key::IS_SUBJECT_ENABLED,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_SUBJECT_ENABLED",
            ], // bool
            KEY::LOCALE_CODE => [
                'key' => Key::LOCALE_CODE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => null,
                "description" => "Locale Code",
                /*
                 en-In, en-US, de-FR, etc... to identify the currency value.
                 */
            ], // str
            KEY::LANGUAGE_CODE => [
                'key' => Key::LANGUAGE_CODE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => null,
                "description" => "Language code Code",
                /*
                 hi, en, de, etc...
                 */
            ], // str
            //isLumpsumAmountEnabled
            Key::IS_LUMPSUM_AMOUNT_ENABLED => [
                'key' => Key::IS_LUMPSUM_AMOUNT_ENABLED,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_SUBJECT_ENABLED",
            ], // bool

            Key::IS_DISPlAY_AMOUNT_WORDS => [
                'key' => Key::IS_DISPlAY_AMOUNT_WORDS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "Is display amount in words?",
            ], // bool
            Key::PDF_NAME_TEMPLATE => [
                'key' => Key::PDF_NAME_TEMPLATE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "{number}.pdf", // "no_discount", "per_item,per_item,on_total"
                "description" => "{customerName}_{date}_{number}.pdf",
            ], // string
            KEY::IS_MARGIN_ON_PRODUCT_ENABLED => [
                'key' => Key::IS_MARGIN_ON_PRODUCT_ENABLED,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "Should allow profit margin on product?",
            ], // bool
            Key::IS_PAGE_BREAK_BEFORE_TERMS => [
                'key' => Key::IS_PAGE_BREAK_BEFORE_TERMS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_PAGE_BREAK_BEFORE_TERMS?",
            ], // bool
            Key::shouldDisplayPrice => [
                'key' => Key::shouldDisplayPrice,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "shouldDisplayPrice?",
            ], // bool
            Key::allowedFollowupStatus => [
                'key' => Key::allowedFollowupStatus,
                'group' => $group,
                'type' => Key::JSON,
                'value' => '[
  {
    "key": "new",
    "label": "New",
    "color": "#8FB022"
  },
  {
    "key": "revised",
    "label": "Revised",
    "color": "#F2C548"
  },
  {
    "key": "in-progress",
    "label": "In Progress",
    "color": "#FFA500"
  },
  {
    "key": "rejected",
    "label": "Rejected",
    "color": "#D04236"
  },
  {
    "key": "approved",
    "label": "Approved",
    "color": "#008000"
  }
]',
                "description" => '',
            ], // json color changes
        ];
    }
}

