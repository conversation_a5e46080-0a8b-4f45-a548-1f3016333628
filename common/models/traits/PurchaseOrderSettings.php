<?php

namespace common\models\traits;

use common\models\enum\Key;

trait PurchaseOrderSettings
{
    public static function purchase_order_settings(SettingsContext $context): array
    {
        $group = Key::GROUP_PURCHASE_ORDER;

        return [
            Key::DISCOUNT_SETTINGS => [
                'key' => Key::DISCOUNT_SETTINGS,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "no_discount", // "no_discount", "per_item,per_item,on_total"
                "description" => "no_discount,per_item,on_total",
            ], // bool
            Key::TAX_RATE_ON_TOTAL => [
                'key' => Key::TAX_RATE_ON_TOTAL,
                'group' => $group,
                'type' => Key::INT,
                'value' => 18,
                "description" => "TAX_RATE_ON_TOTAL",
            ], // bool
            Key::TAX_SETTINGS => [
                'key' => Key::TAX_SETTINGS,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "per_item",
                "description" => "no_tax,per_item,on_total",
            ], // bool
            Key::TOP_MESSAGE => [
                'key' => Key::TOP_MESSAGE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "",
                "description" => "TOP_MESSAGE",
            ], // bool
            Key::BOTTOM_MESSAGE => [
                'key' => Key::BOTTOM_MESSAGE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "",
                "description" => "BOTTOM_MESSAGE",
            ], // bool
            Key::IS_DISPLAY_BANK_DETAILS => [
                'key' => Key::IS_DISPLAY_BANK_DETAILS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_DISPLAY_BANK_DETAILS",
            ], // bool
            Key::isDisplayUpiDetails => [
                'key' => Key::isDisplayUpiDetails,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_DISPLAY_BANK_DETAILS",
            ], // bool
            Key::IS_DISPLAY_HSN_CODE => [
                'key' => Key::IS_DISPLAY_HSN_CODE,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_DISPLAY_HSN_CODE",
            ], // bool
            Key::IS_DISPlAY_AMOUNT_WORDS => [
                'key' => Key::IS_DISPlAY_AMOUNT_WORDS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "Is display amount in words?",
            ], // bool
            Key::PDF_NAME_TEMPLATE => [
                'key' => Key::PDF_NAME_TEMPLATE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "{number}.pdf", // "no_discount", "per_item,per_item,on_total"
                "description" => "{customerName}_{date}_{number}.pdf",
            ], // string
            Key::IS_PAGE_BREAK_BEFORE_TERMS => [
                'key' => Key::IS_PAGE_BREAK_BEFORE_TERMS,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "IS_PAGE_BREAK_BEFORE_TERMS?",
            ], // bool
            KEY::LOCALE_CODE => [
                'key' => Key::LOCALE_CODE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => null,
                "description" => "Locale Code",
                /*
                 en-In, en-US, de-FR, etc... to identify the currency value.
                 */
            ], // str
            KEY::LANGUAGE_CODE => [
                'key' => Key::LANGUAGE_CODE,
                'group' => $group,
                'type' => Key::STRING,
                'value' => null,
                "description" => "Language code Code",
                /*
                 hi, en, de, etc...
                 */
            ], // str
            Key::allowedFollowupStatus => [
                'key' => Key::allowedFollowupStatus,
                'group' => $group,
                'type' => Key::JSON,
                'value' => '[]',
                "description" => '',
            ], // json
            Key::isDisplaySignatureBlock => [
                'key' => Key::isDisplaySignatureBlock,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "isDisplaySignatureBlock?",
            ], // bool
        ];
    }

    public static function purchase_order_overwrite(): array
    {
        return [Key::IS_PAGE_BREAK_BEFORE_TERMS, Key::LOCALE_CODE, Key::LANGUAGE_CODE];
    }
}

