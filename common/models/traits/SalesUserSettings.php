<?php

namespace common\models\traits;

use common\models\enum\Key;

trait SalesUserSettings
{
    public static function sales_user_settings(SettingsContext $context): array
    {
        $group = Key::GROUP_SALES_USER;
        return [
            Key::canAccessInvoice => [
                'key' => Key::canAccessInvoice,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessInvoice?",
            ], // bool
            Key::canAccessQuotation => [
                'key' => Key::canAccessQuotation,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessQuotation?",
            ], // bool
            Key::canAccessPo => [
                'key' => Key::canAccessPo,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessPo?",
            ], // bool
            Key::canAccessPi => [
                'key' => Key::canAccessPi,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessPi?",
            ], // bool
            Key::canAccessReceipt => [
                'key' => Key::canAccessReceipt,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessReceipt?",
            ], // bool
            Key::canAccessDeliveryNote => [
                'key' => Key::canAccessDeliveryNote,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "canAccessDeliveryNote?",
            ], // bool
            Key::shouldAllowCreateProduct => [
                'key' => Key::shouldAllowCreateProduct,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "shouldAllowCreateProduct?",
            ], // bool
            Key::shouldAllowCreateTerms => [
                'key' => Key::shouldAllowCreateTerms,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 0,
                "description" => "shouldAllowCreateTerms?",
            ], // bool
            Key::shouldDisplayOtherCustomers => [
                'key' => Key::shouldDisplayOtherCustomers,
                'group' => $group,
                'type' => Key::BOOL,
                'value' => 1,
                "description" => "shouldAllowCreateTerms?",
            ], // bool

        ];
    }
}

