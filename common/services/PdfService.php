<?php

namespace common\services;

use common\helpers\FileManager;
use common\models\Business;
use common\models\enum\Key;
use common\models\enum\PlanFeature;
use common\models\Invoice;
use common\models\Quotation;
use common\models\Receipt;
use common\models\Template;
use Exception;
use kartik\mpdf\Pdf;
use Mpdf\MpdfException;
use setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfParser\Type\PdfTypeException;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;

/**
 * PDF Service
 *
 * This service handles PDF generation for various document types.
 */
class PdfService
{
    /**
     * @var PdfService|null Singleton instance
     */
    private static $instance = null;

    /**
     * @var array Cache of published assets within the current request
     */
    private $publishedAssets = [];

    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct()
    {
        // Initialize the service
    }

    /**
     * Get singleton instance
     *
     * @return PdfService
     */
    public static function getInstance(): PdfService
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Generate PDF for a quotation
     *
     * @param Quotation $quotation The quotation model
     * @param bool $generateFileName Whether to generate a new filename
     * @return bool Whether the PDF was generated successfully
     */
    public function generateQuotationPdf(Quotation $quotation, bool $generateFileName = true): bool
    {
        try {
            $template = $quotation->template;
            $business = $quotation->business;

            // Check if business has feature access
            $hasFeatureAccess = ($quotation->isPurchaseOrder && $business->hasAccess(PlanFeature::PURCHASE_ORDER)) ||
                (!$quotation->isPurchaseOrder && $business->hasAccess(PlanFeature::QUOTATION));

            // Get template directory and publish assets
            $templateDir = $this->getTemplateDir($template);
            $assetsDir = $this->publishAssets($templateDir);
            $assetsDirUrl = $assetsDir[1];

            // Get business settings
            $appSettings = $business->settings(Key::GROUP_APP);
            $customSettings = $business->settings(Key::GROUP_CUSTOM);
            $moduleSettings = $business->settings($quotation->groupKey);

            // Determine if watermark should be displayed
            $shouldDisplayWatermark = $this->shouldDisplayWatermark($business, $hasFeatureAccess, $quotation->groupKey);
            $watermarkLink = $this->getWatermarkLink($quotation->groupKey, $business->regionCode);

            // Render the template
            $htmlContent = $this->renderTemplate(
                $templateDir . '/pdfView.php',
                [
                    'quotation' => $quotation,
                    'template' => $template,
                    'business' => $business,
                    'appSettings' => $appSettings,
                    'customSettings' => $customSettings,
                    'quotationItems' => $quotation->quotationItems,
                    'terms' => $quotation->getTermsData(),
                    'companyScopeItems' => $quotation->getCompanyScopeData(),
                    'customerScopeItems' => $quotation->getCustomerScopeData(),
                    'shouldDisplayWatermark' => $shouldDisplayWatermark,
                    'watermarkLink' => $watermarkLink,
                    'otherInfo' => $quotation->getOtherInfoData(),
                    'assetsUrl' => $assetsDirUrl,
                    'currentUser' => $quotation->assignedTo
                ]
            );

            // Determine format and orientation first
            $format = null;
            $orientation = null;

            // Special configuration for quotations with LP orientation
            if ($template->orientation == "LP") {
                $additionalFieldsConfig = Business::getConfig(Key::GROUP_CUSTOM, Key::ADDITIONAL_PRODUCT_FIELDS);
                $quotationSettings = $quotation->getQuotationSettingsData();
                $displayProductAdditionalFields = $quotationSettings[Key::shouldDisplayProductAdditionalFields] ?? false;

                if ($displayProductAdditionalFields && $additionalFieldsConfig && count($additionalFieldsConfig) > 0) {
                    $orientation = Pdf::ORIENT_LANDSCAPE;
                } else {
                    $orientation = Pdf::ORIENT_PORTRAIT;
                }
            }

            // Configure mPDF with the determined format and orientation
            $mPdf = $this->configureMpdf($template, $business, $templateDir, $format, $orientation);

            // Generate PDF
            $output = $this->generatePdfOutput($mPdf, $htmlContent);

            // Store PDF data in the model
            $quotation->pdfOutputData = $output;
            $quotation->pdfHtmlContent = $htmlContent;
            $quotation->pdfFileBase64 = base64_encode($output);

            // Handle file naming and storage
            if ($generateFileName || empty($quotation->pdfFileName)) {
                $fileInitial = $quotation->isPurchaseOrder ? "PO" : "Q";
                $fileName = $this->generateUniquePdfFilename($fileInitial, $quotation->id);
                $quotation->pdfFileName = $fileName;
                $quotation->updateAttributes(['pdfFileName']);
            }

            $isPdfStorageEnabled = $business->isPdfStorageEnabled();
            if ($hasFeatureAccess && $isPdfStorageEnabled) {
                FileManager::saveQuotationFile($quotation);
            } elseif (!empty($quotation->pdfFileUrl)) {
                $quotation->pdfFileUrl = null;
                $quotation->updateAttributes(['pdfFileUrl']);
            }

            return true;
        } catch (Exception $e) {
            $this->logError($e);
            return false;
        }
    }

    /**
     * Generate PDF for an invoice
     *
     * @param Invoice $invoice The invoice model
     * @param bool $generateFileName Whether to generate a new filename
     * @return bool Whether the PDF was generated successfully
     */
    public function generateInvoicePdf(Invoice $invoice, bool $generateFileName = true): bool
    {
        try {
            $template = $invoice->template;
            $business = $invoice->business;

            // Check if business has feature access
            $hasFeatureAccess = ($invoice->isInvoice && $business->hasAccess(PlanFeature::INVOICE)) ||
                ($invoice->isProformaInvoice && $business->hasAccess(PlanFeature::PROFORMA_INVOICE)) ||
                ($invoice->isDeliveryNote && $business->hasAccess(PlanFeature::DELIVERY_NOTE));

            // Get template directory and publish assets
            $templateDir = $this->getTemplateDir($template);
            $assetsDir = $this->publishAssets($templateDir);
            $assetsDirUrl = $assetsDir[1];

            // Determine which view file to use
            $pdfViewFile = "pdfView.php";
            if (!$invoice->isDeliveryNote && $business->isGstApplicable($invoice->groupKey)) {
                $pdfViewFile = "pdfView-gst.php";
            }

            // Get business settings
            $appSettings = $business->settings(Key::GROUP_APP);
            $customSettings = $business->settings(Key::GROUP_CUSTOM);
            $moduleSettings = $business->settings($invoice->groupKey);

            // Determine if watermark should be displayed
            $shouldDisplayWatermark = $this->shouldDisplayWatermark($business, $hasFeatureAccess, $invoice->groupKey);
            $watermarkLink = $this->getWatermarkLink($invoice->groupKey, $business->regionCode);

            // Render the template
            $htmlContent = $this->renderTemplate(
                $templateDir . '/' . $pdfViewFile,
                [
                    'invoice' => $invoice,
                    'template' => $template,
                    'business' => $business,
                    'appSettings' => $appSettings,
                    'customSettings' => $customSettings,
                    'invoiceItems' => $invoice->invoiceItems,
                    'terms' => $invoice->getTermsData(),
                    'shouldDisplayWatermark' => $shouldDisplayWatermark,
                    'watermarkLink' => $watermarkLink,
                    'otherInfo' => $invoice->getOtherInfoData(),
                    'assetsUrl' => $assetsDirUrl,
                    'currentUser' => $invoice->assignedTo
                ]
            );

            // Determine format and orientation first
            $format = null;
            $orientation = null;

            // Configure mPDF with the determined format and orientation
            $mPdf = $this->configureMpdf($template, $business, $templateDir, $format, $orientation);

            // Generate PDF
            $output = $this->generatePdfOutput($mPdf, $htmlContent);

            // Store PDF data in the model
            $invoice->pdfOutputData = $output;
            $invoice->pdfHtmlContent = $htmlContent;
            $invoice->pdfFileBase64 = base64_encode($output);

            // Handle file naming and storage
            if ($generateFileName || empty($invoice->pdfFileName)) {
                $fileInitial = $invoice->getFileInitial();
                $fileName = $this->generateUniquePdfFilename($fileInitial, $invoice->id);
                $invoice->pdfFileName = $fileName;
                $invoice->updateAttributes(['pdfFileName']);
            }

            $isPdfStorageEnabled = $business->isPdfStorageEnabled();
            if ($hasFeatureAccess && $isPdfStorageEnabled) {
                FileManager::saveInvoiceFile($invoice);
            } elseif (!empty($invoice->pdfFileUrl)) {
                $invoice->pdfFileUrl = null;
                $invoice->updateAttributes(['pdfFileUrl']);
            }

            return true;
        } catch (Exception $e) {
            $this->logError($e);
            return false;
        }
    }

    /**
     * Generate PDF for a receipt
     *
     * @param Receipt $receipt The receipt model
     * @param bool $generateFileName Whether to generate a new filename
     * @return bool Whether the PDF was generated successfully
     */
    public function generateReceiptPdf(Receipt $receipt, bool $generateFileName = true): bool
    {
        try {
            $template = $receipt->template;
            if ($template == null) {
                throw new Exception('No template found for receipt');
            }

            $business = $receipt->business;

            // Check if business has feature access
            $hasFeatureAccess = $business->hasAccess(PlanFeature::RECEIPT);

            // Get template directory and publish assets
            $templateDir = $this->getTemplateDir($template);
            $assetsDir = $this->publishAssets($templateDir);
            $assetsDirUrl = $assetsDir[1];

            // Determine which view file and CSS to use
            $pdfViewFile = "pdfView.php";
            $cssName = "pdf-style.css";

            if ($receipt->isItemizedReceipt) {
                if ($business->isGstApplicable(Key::GROUP_RECEIPT)) {
                    $pdfViewFile = "pdfView-itemized.php";
                } else {
                    $pdfViewFile = "pdfView-itemized.php";
                }
                $cssName = "pdf-style-itemized.css";
            }

            // Get business settings
            $appSettings = $business->settings(Key::GROUP_APP);
            $customSettings = $business->settings(Key::GROUP_CUSTOM);
            $moduleSettings = $business->settings(Key::GROUP_RECEIPT);

            // Determine if watermark should be displayed
            $shouldDisplayWatermark = $this->shouldDisplayWatermark($business, $hasFeatureAccess, Key::GROUP_RECEIPT);
            $watermarkLink = $this->getWatermarkLink(Key::GROUP_RECEIPT, $business->regionCode);

            // Render the template
            $htmlContent = $this->renderTemplate(
                $templateDir . '/' . $pdfViewFile,
                [
                    'receipt' => $receipt,
                    'template' => $template,
                    'business' => $business,
                    'appSettings' => $appSettings,
                    'customSettings' => $customSettings,
                    'receiptItems' => $receipt->receiptItems,
                    'otherInfo' => $receipt->getOtherInfoData(),
                    'shouldDisplayWatermark' => $shouldDisplayWatermark,
                    'watermarkLink' => $watermarkLink,
                    'assetsUrl' => $assetsDirUrl,
                    'currentUser' => $receipt->createdBy
                ]
            );

            // Special configuration for receipts - determine format and orientation first
            $format = null;
            $orientation = null;

            if (!$receipt->isItemizedReceipt) {
                $format = 'A5'; // Set custom page size to half of A4
                $orientation = Pdf::ORIENT_LANDSCAPE;
            }

            // Configure mPDF with the determined format and orientation
            $mPdf = $this->configureMpdf($template, $business, $templateDir, $format, $orientation);

            // Generate PDF
            $output = $this->generatePdfOutput($mPdf, $htmlContent);

            // Store PDF data in the model
            $receipt->pdfOutputData = $output;
            $receipt->pdfHtmlContent = $htmlContent;
            $receipt->pdfFileBase64 = base64_encode($output);

            // Handle file naming and storage
            if ($generateFileName || empty($receipt->pdfFileName)) {
                $fileInitial = $receipt->isItemizedReceipt ? "RI" : "R";
                $fileName = $this->generateUniquePdfFilename($fileInitial, $receipt->id);
                $receipt->pdfFileName = $fileName;
                $receipt->updateAttributes(['pdfFileName']);
            }

            $isPdfStorageEnabled = $business->isPdfStorageEnabled();
            if ($hasFeatureAccess && $isPdfStorageEnabled) {
                FileManager::saveReceiptFile($receipt);
            } elseif (!empty($receipt->pdfFileUrl)) {
                $receipt->pdfFileUrl = null;
                $receipt->updateAttributes(['pdfFileUrl']);
            }

            return true;
        } catch (Exception $e) {
            $this->logError($e);
            return false;
        }
    }

    /**
     * Get template directory
     *
     * @param Template $template The template model
     * @return string The template directory
     */
    private function getTemplateDir(Template $template): string
    {
        return '@templates/' . $template->type . "/" . $template->dirName;
    }

    /**
     * Publish template assets
     *
     * @param string $templateDir The template directory
     * @return array The published assets [path, url]
     */
    private function publishAssets(string $templateDir): array
    {
        $assetsDir = $templateDir . "/assets/";
        $fullPath = Yii::getAlias($assetsDir);

        // Check if assets are already published in this request
        if (isset($this->publishedAssets[$fullPath])) {
            return $this->publishedAssets[$fullPath];
        }

        // Publish assets using Yii2's asset manager
        try {
            $published = Yii::$app->assetManager->publish($fullPath);
            $this->publishedAssets[$fullPath] = $published;
            return $published;
        } catch (Exception $e) {
            Yii::error('Failed to publish assets: ' . $e->getMessage());
            // Fallback to direct path
            return [$fullPath, $fullPath];
        }
    }

    /**
     * Determine if watermark should be displayed
     *
     * @param Business $business The business model
     * @param bool $hasFeatureAccess Whether the business has feature access
     * @param string|null $groupKey The document group key (e.g., 'quotation', 'invoice')
     * @return bool Whether to display watermark
     */
    private function shouldDisplayWatermark(Business $business, bool $hasFeatureAccess, string $groupKey = null): bool
    {
        // If the business has feature access, no watermark is needed
        if ($hasFeatureAccess) {
            return false;
        }

        // Check if free usage is available
        $freeUsageAvailable = true;
        $isWatermarkLimitOver = false;

        if (!$hasFeatureAccess) {
            $businessStats = $business->businessStats;

            // Determine if free usage is available based on the document type
            if ($groupKey) {
                $freeUsageAvailable = match ($groupKey) {
                    Key::GROUP_QUOTATION => $businessStats->quotationFreeUsageLimit > 0,
                    Key::GROUP_PURCHASE_ORDER => $businessStats->poFreeUsageLimit > 0,
                    Key::GROUP_BUDGET => $businessStats->budgetFreeUsageLimit > 0,
                    Key::GROUP_INVOICE => $businessStats->invoiceFreeUsageLimit > 0,
                    Key::GROUP_RECEIPT => $businessStats->receiptFreeUsageLimit > 0,
                    default => false,
                };
            }

            // Check if usage is over the watermark limit
            $usageLimitForWatermark = function_exists('env') ? env(Key::usageLimitForWatermark) : (Yii::$app->params[Key::usageLimitForWatermark] ?? 10);
            $isWatermarkLimitOver = $businessStats->totalUsageCount > $usageLimitForWatermark;
        }

        // Display watermark if no feature access, no free usage available, and over the watermark limit
        return !$hasFeatureAccess && !$freeUsageAvailable && $isWatermarkLimitOver;
    }

    /**
     * Generate watermark link with UTM parameters
     *
     * @param string|null $groupKey The document group key (e.g., 'quotation', 'invoice')
     * @param string|null $regionCode The business region code
     * @return string The watermark link
     */
    private function getWatermarkLink(string $groupKey = null, string $regionCode = null): string
    {
        // Get platform type from server or default to mobile-app
        $platformType = $_SERVER['HTTP_PLATFORM_TYPE'] ?? "mobile-app";

        // Base URL for the watermark link
        $quotationWebsite = "https://quotationmaker.app/";

        // Build and return the watermark link with UTM parameters
        return "{$quotationWebsite}?utm_source=pdf&utm_medium={$platformType}&utm_campaign={$groupKey}&utm_region={$regionCode}";
    }

    /**
     * Render template with variables
     *
     * @param string $templatePath The template path
     * @param array $variables The variables to pass to the template
     * @return string The rendered HTML
     */
    private function renderTemplate(string $templatePath, array $variables): string
    {
        return Yii::$app->controller->renderPartial($templatePath, $variables);
    }

    /**
     * Configure mPDF instance
     *
     * @param Template $template The template model
     * @param Business $business The business model
     * @param string $templateDir The template directory
     * @param string|null $format Optional format override (e.g., 'A4', 'A5')
     * @param string|null $orientation Optional orientation override ('P' or 'L')
     * @return Pdf The configured mPDF instance
     * @throws InvalidConfigException
     */
    private function configureMpdf(Template $template, Business $business, string $templateDir, $format = null, $orientation = null): Pdf
    {
        /** @var Pdf $mPdf */
        $mPdf = Yii::$app->pdf;

        // Set format if provided
        if ($format !== null) {
            $mPdf->format = $format;
        }else{
            $mPdf->format = 'A4';
        }

        // Set orientation - use provided orientation or from template
        if ($orientation !== null) {
            $mPdf->orientation = $orientation;
        } else {
            $mPdf->orientation = $template->orientation;
        }

        // Get margin settings from template
        $margin = $template->getMarginSettings();
        $defaultMargin = 10;

        // Set margins
        $mPdf->marginTop = $margin->top ?? $defaultMargin;
        $mPdf->marginRight = $margin->right ?? $defaultMargin;
        $mPdf->marginBottom = $margin->bottom ?? $defaultMargin;
        $mPdf->marginLeft = $margin->left ?? $defaultMargin;
        $mPdf->marginHeader = $margin->header ?? $defaultMargin;
        $mPdf->marginFooter = $margin->footer ?? $defaultMargin;

        // Add custom fonts
        $defaultConfig = (new \Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new \Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        // Define custom fonts directory
        $customFontsDir = Yii::getAlias($templateDir . "/fonts");

        // Configure font options
        $mPdf->options = ArrayHelper::merge($mPdf->options, [
            'fontDir' => array_merge($fontDirs, [$customFontsDir]),
            'fontdata' => array_merge($fontData, [
                'rocket_rinder' => [
                    'R' => 'Rocket Rinder.otf',
                ]
            ])
        ]);

        // Get app settings for header/footer configuration
        $appSettings = $business->settings(Key::GROUP_APP);
        $customSettings = $business->settings(Key::GROUP_CUSTOM);

        // Configure header and footer
        $isHeaderFooterEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED] ?? false;
        $isHeaderFooterEnabled = $isHeaderFooterEnabled && $template->isHeaderFooterEnable;
        $shouldRepeatHeaderFooter = $appSettings[Key::shouldRepeatHeaderFooter] ?? 0;

        // Adjust margins for header/footer images
        if (!empty($business->headerImg) && $isHeaderFooterEnabled) {
            if ($shouldRepeatHeaderFooter) {
                $mPdf->marginTop = $margin->topWithHeaderImage ?? $mPdf->marginTop;
            }
            $mPdf->marginLeft = $margin->leftRightWithHeaderImage ?? $mPdf->marginLeft;
            $mPdf->marginRight = $margin->leftRightWithHeaderImage ?? $mPdf->marginRight;
        }

        if (!empty($business->footerImg) && $isHeaderFooterEnabled && $shouldRepeatHeaderFooter) {
            $mPdf->marginBottom = $margin->bottomWithFooterImage ?? $mPdf->marginBottom;
        }

        // Set CSS files
        $mPdf->cssFile = ['@backend/web/css/bootstrap.min.css', $templateDir . "/assets/pdf-style.css"];

        // Add business name style if available
        $businessNameStyle = $customSettings[Key::businessNameStyle] ?? null;
        if ($businessNameStyle) {
            $mPdf->cssInline .= $businessNameStyle;
        }

        // Configure additional mPDF options
        $mPdf->options = ArrayHelper::merge($mPdf->options, [
            'allows_output_buffering' => true,
            'autoLangToFont' => true,
            'autoScriptToLang' => true,
            'ignore_invalid_utf8' => true,
            'useSubstitutions' => true,
        ]);

        // Allow unsafe SSL requests for images from other domains
        $mPdf->getApi()->curlAllowUnsafeSslRequests = true;

        return $mPdf;
    }

    /**
     * Generate PDF output
     *
     * @param Pdf $mPdf The mPDF instance
     * @param string $htmlContent The HTML content
     * @return string The PDF output
     * @throws MpdfException
     * @throws CrossReferenceException
     * @throws PdfParserException
     * @throws PdfTypeException
     * @throws InvalidConfigException
     */
    private function generatePdfOutput(Pdf $mPdf, string $htmlContent): string
    {
        try {

            // Set header and footer - append to existing cssInline, don't overwrite
            $mPdf->cssInline .= '@page {
                header: pdf_header;
                footer: pdf_footer;
            }';
            $mPdf->getCss();

            // Set up error handler to suppress unserialize warnings and file operation warnings (PHP 8.3 compatibility)
            $previousErrorHandler = set_error_handler(function ($errno, $errstr, $errfile, $errline) use (&$previousErrorHandler) {
                // Suppress unserialize warnings from mPDF
                if (strpos($errstr, 'unserialize():') !== false && strpos($errfile, 'mpdf') !== false) {
                    return true; // Suppress the warning
                }

                // Suppress file operation warnings from mPDF (like unlink for non-existent files)
                if ((strpos($errstr, 'unlink(') !== false || strpos($errstr, 'file_exists') !== false) &&
                    (strpos($errfile, 'mpdf') !== false || strpos($errfile, 'Cache.php') !== false)) {
                    return true; // Suppress the warning
                }

                // For all other errors, use the previous error handler
                if ($previousErrorHandler) {
                    return call_user_func($previousErrorHandler, $errno, $errstr, $errfile, $errline);
                }

                // Default error handling
                return false;
            });

            // Generate PDF
            $output = $mPdf->output($htmlContent, "", 'S');

            // Restore the previous error handler
            restore_error_handler();

            return $output;
        } catch (Exception $e) {
            // Log the error and rethrow it to be handled by the calling method
            $this->logError($e);
            throw $e;
        }
    }

    /**
     * Log error
     *
     * @param Exception $e The exception
     */
    private function logError(Exception $e): void
    {
        Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
    }

    /**
     * Generate a unique PDF filename
     *
     * @param string $fileInitial The file initial (e.g., "Q", "PO", "INV")
     * @param int|null $documentId The unique ID of the document (if available)
     * @return string A unique filename
     */
    private function generateUniquePdfFilename(string $fileInitial, ?int $documentId = null): string
    {

        if ($documentId) {
            $uniqueId = uniqid('', false);
            return $fileInitial . $documentId . '_' . $uniqueId . '.pdf';
        }

        // Make uniqid URL-compatible by replacing dots with underscores
        $uniqueId = str_replace('.', '_', uniqid('', true));
        return $fileInitial . '_' . $uniqueId . '.pdf';
    }
}