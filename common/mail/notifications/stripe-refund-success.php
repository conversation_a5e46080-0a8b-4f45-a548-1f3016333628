<?php

/* @var $this yii\web\View */
/* @var $user common\models\User */
/* @var $business common\models\Business */
/* @var $subscription common\models\Subscription */
/* @var $refundAmount float */

use common\models\enum\CountryDialPrefix;
use common\models\Region;

$todayDate = date("d.m.Y H:i:s");
$waMessage = urlencode("Your payment for $refundAmount is processed successfully! 
It may take 5-10 business days for the money to reach your bank account!

If you have any queries or questions then feel free to contact here! 

Your support PIN is *{$user->id}*");

?>
<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">


    <style type="text/css">
        @font-face {
            font-family: 'MyriadPro-Regular';
            src: url('#FONT_PATH#MyriadPro-Regular.eot?#iefix') format('embedded-opentype'),
            url('#FONT_PATH#MyriadPro-Regular.woff') format('woff'),
            url('#FONT_PATH#MyriadPro-Regular.ttf') format('truetype'),
            url('#FONT_PATH#MyriadPro-Regular.svg#svgFontName') format('svg');
        }

        @font-face {
            font-family: 'MyriadPro-Light';
            src: url('#FONT_PATH#MyriadPro-Light.eot?#iefix') format('embedded-opentype'),
            url('#FONT_PATH#MyriadPro-Light.woff') format('woff'),
            url('#FONT_PATH#MyriadPro-Light.ttf') format('truetype'),
            url('#FONT_PATH#MyriadPro-Light.svg#svgFontName') format('svg');
        }

        @font-face {
            font-family: 'Calibri';
            src: url('#FONT_PATH#Calibri.eot?#iefix') format('embedded-opentype'),
            url('#FONT_PATH#Calibri.woff') format('woff'),
            url('#FONT_PATH#Calibri.ttf') format('truetype'),
            url('#FONT_PATH#Calibri.svg#svgFontName') format('svg');
        }


        body {
            background: #dbdbdb;
            font-family: 'Calibri', Arial, Helvetica, sans-serif;
            font-weight: normal;
            background-repeat: no-repeat;
        }

        a {
            outline: none;
        }

        img {
            border: none;
        }

        .icon_txt {
            font-size: 15px;
            color: #2B5896;
            line-height: 30px;
        }

        .icon_txt strong {
            color: #000;
            font-weight: bold;
        }

        body, td, th {
            font-family: Calibri, Arial, Helvetica, sans-serif;
        }
    </style>

</head>

<body>
<table width="650" border="0" cellspacing="0" cellpadding="0" align="center">
    <tr>
        <td align="right">
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td width="455">&nbsp;</td>
                    <td style="background:#2B5896; color:#FFF; font-size:16px; text-align:center; line-height:30px; width:130px; display:block; text-transform:uppercase;">
                        <?= $todayDate ?>
                    </td>
                </tr>
            </table>
        </td>
        <td width="15">&nbsp;</td>
    </tr>
</table>


<table bgcolor="#FFFFFF" width="650" border="0" cellspacing="0" cellpadding="0" align="center"
       style="border-left:#b5b5b5 1px solid; border-right:#b5b5b5 1px solid;">
    <tr>
        <td bgcolor="#FFFFFF" style="border-top:#2B5896 5px solid;">
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td width="20">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td colspan="3">&nbsp;</td>
                    <td width="15">&nbsp;</td>
                </tr>

            </table>
        </td>
    </tr>

    <td height="165px" valign="top" bgcolor="#FFF" style="border-top:#c7c7c7 1px solid;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">


            <td align="center" valign="top">
                <table width="94%" border="0" cellspacing="0" cellpadding="0" style="border:none;">
                    <tr>


                        <td height="60" colspan="3"
                            style="font-family: Arial,Helvetica,sans-serif; margin: 0px; font-size: 26px; line-height: normal; color: #2B5896;">
                            Hello Admin,
                        </td>
                    </tr>


                    <tr>
                        <td height="50" colspan="3">
                            <table width="96%" border="0" cellspacing="0" cellpadding="0" align="right">
                                <tr>
                                    <td height="60"><strong style="color: darkmagenta">Stripe payment refunded successfully!</strong></td>
                                </tr>

                                <tr>
                                    <td>
                                        <table>
                                            <tr><td>Refund Amount</td> <td><?=$refundAmount?></td></tr>
                                            <tr><td>Subscription ID: </td> <td><?=$subscription->id?> <br/></td></tr>
                                            <?= $this->render('_info', [
                                                'waMessage' => $waMessage,
                                                'user' => $user,
                                                'business' => $business,
                                                'subscription' => $subscription,
                                                'plan' => $subscription->plan
                                            ]); ?>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>


                    <tr>
                        <td height="50" colspan="3">
                            <table width="96%" border="0" cellspacing="0" cellpadding="0" align="right">

                                <tr>
                                    <td height="60">This email was sent automatically, please do not reply to this email
                                        address.
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>


                    <tr>
                        <td height="70" colspan="3"
                            style="line-height: 24px; font-family:Arial, Helvetica, sans-serif; color: #000000; margin: 0px;">
                            Thank you,<br/><?= APP_NAME; ?>
                        </td>
                    </tr>
                    </tr>

                </table>
            </td>

            </tr>


            <tr>
                <td colspan="3" style="border-top:#c7c7c7 1px solid;">&nbsp;</td>
            </tr>
        </table>

    </td>
    </tr>
    <tr>
        <td bgcolor="#FFFFFF" style="border-bottom:#767676 6px solid;">&nbsp;</td>
    </tr>
</table>

</body>
</html>