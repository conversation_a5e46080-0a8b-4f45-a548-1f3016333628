<?php

/* @var $this yii\web\View */
/* @var $user common\models\User */
/* @var $subscription common\models\Subscription */
/* @var $business common\models\Business */
/* @var $plan common\models\SubscriptionPlan */

$expiryDate = formatStandardDate($subscription->endDate);
$expiryDate = (strtotime($subscription->endDate) > strtotime('today')) ? formatStandardDate(time()) : $expiryDate;
// Get similar plans for renewal suggestions
$similarPlans = \common\models\SubscriptionPlan::find()
    ->where(['type' => $plan->type])
    ->andWhere(['isActive' => 1])
    ->andWhere(['!=', 'id', $plan->id])
    ->orderBy(['price' => SORT_ASC])
    ->limit(2)
    ->all();

// If not enough similar plans, get any active plans
if (count($similarPlans) < 2) {
    $additionalPlans = \common\models\SubscriptionPlan::find()
        ->where(['isActive' => 1])
        ->andWhere(['!=', 'id', $plan->id])
        ->andWhere(['not in', 'id', array_map(function ($p) {
            return $p->id;
        }, $similarPlans)])
        ->orderBy(['price' => SORT_ASC])
        ->limit(2 - count($similarPlans))
        ->all();

    $similarPlans = array_merge($similarPlans, $additionalPlans);
}
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->render('@common/mail/layouts/_styles') ?>
</head>
<body>
<!-- Gmail fix -->
<div class="gmail-fix">&nbsp;</div>

<div class="wrapper">
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="center">
                <table class="email-container" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="content-cell">
                            <?= $this->render('@common/mail/layouts/_header') ?>

                            <div class="content">
                                <h2 class="greeting">Hello <?= $user->name ?>,</h2>

                                <div class="alert alert-warning">
                                    <p><strong>Subscription Ended</strong></p>
                                    <p>Your <strong><?= $plan->name ?></strong> subscription has ended. We hope you
                                        enjoyed using our premium features!</p>
                                </div>

                                <div class="message">
                                    <p>We wanted to let you know that your subscription has been cancelled or has
                                        expired. You can continue
                                        using our app with limited features, but to access all premium features again,
                                        you'll need to renew your
                                        subscription.</p>
                                </div>

                                <div class="details">
                                    <h3>Subscription Information</h3>
                                    <table class="responsive-table">
                                        <tr>
                                            <td><strong>Plan Name:</strong></td>
                                            <td><?= $plan->name ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>End Date:</strong></td>
                                            <td><?= $expiryDate ?></td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="details">
                                    <h3>Why renew your subscription?</h3>
                                    <ul>
                                        <li><strong>Unlimited Documents:</strong> Continue creating without restrictions
                                        </li>
                                        <li><strong>Premium Features:</strong> Access to all advanced features and
                                            templates
                                        </li>
                                        <li><strong>Cloud Backup:</strong> Keep your data safe and accessible across
                                            devices
                                        </li>
                                        <li><strong>Ad-Free Experience:</strong> No more advertisements</li>
                                        <li><strong>Priority Support:</strong> Get help faster when you need it</li>
                                    </ul>
                                </div>

                                <div class="details">
                                    <h3>Recommended plans for you:</h3>

                                    <!-- Original plan -->
                                    <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
                                        <h4 style="margin-bottom: 8px;"><?= $plan->name ?> (<?= $plan->period ?>)</h4>
                                        <?php
                                        $description = json_decode($plan->description, true);
                                        if (isset($description['subTitle'])):
                                            ?>
                                            <p><em><?= $description['subTitle'] ?></em></p>
                                        <?php endif; ?>
                                        <div style="margin-top: 12px;">
                                            <a href="<?= Yii::$app->params['androidAppLink'] ?>" class="button-primary">Renew
                                                Now</a>
                                        </div>
                                    </div>

                                    <?php foreach ($similarPlans as $similarPlan): ?>
                                        <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
                                            <h4 style="margin-bottom: 8px;"><?= $similarPlan->name ?>
                                                (<?= $similarPlan->period ?>)</h4>
                                            <?php
                                            $description = json_decode($similarPlan->description, true);
                                            if (isset($description['subTitle'])):
                                                ?>
                                                <p><em><?= $description['subTitle'] ?></em></p>
                                            <?php endif; ?>
                                            <div style="margin-top: 12px;">
                                                <a href="<?= Yii::$app->params['androidAppLink'] ?>"
                                                   class="button-primary">Subscribe</a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <?= $this->render('@common/mail/layouts/_footer', ['user' => $user]) ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
</body>
</html>