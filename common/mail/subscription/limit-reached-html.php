<?php

/* @var $this yii\web\View */
/* @var $user common\models\User */
/* @var $business common\models\Business */
/* @var $limitType string */
/* @var $recommendedPlans common\models\SubscriptionPlan[] */

// No date needed

// Map limit type to readable name
$limitTypeNames = [
    'quotation' => 'Quotations',
    'invoice' => 'Invoices',
    'purchase-order' => 'Purchase Orders',
    'proforma-invoice' => 'Proforma Invoices',
    'delivery-note' => 'Delivery Notes',
    'budget' => 'Budgets'
];

$limitTypeName = isset($limitTypeNames[$limitType]) ? $limitTypeNames[$limitType] : ucfirst($limitType);
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->render('@common/mail/layouts/_styles') ?>
</head>
<body>
<!-- Gmail fix -->
<div class="gmail-fix">&nbsp;</div>

<div class="wrapper">
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="center">
                <table class="email-container" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="content-cell">
    <?= $this->render('@common/mail/layouts/_header') ?>

    <div class="content">
        <h2 class="greeting">Hello <?= $user->name ?>,</h2>

        <div class="alert alert-warning">
            <p><strong>Usage Limit Reached</strong></p>
            <p>You've reached the free usage limit for <?= $limitTypeName ?> in your account.</p>
        </div>

        <div class="message">
            <p>We noticed that you've been making great use of our app and have reached the maximum number of
                free <?= strtolower($limitTypeName) ?> allowed. To continue creating <?= strtolower($limitTypeName) ?>
                and access all premium features, we recommend upgrading to one of our subscription plans.</p>
        </div>

        <div class="details">
            <h3>Benefits of upgrading:</h3>
            <ul>
                <li><strong>Unlimited <?= $limitTypeName ?>:</strong> Create as many as you need without restrictions</li>
                <li><strong>Premium Features:</strong> Access to all advanced features and templates</li>
                <li><strong>Cloud Backup:</strong> Keep your data safe and accessible across devices</li>
                <li><strong>Ad-Free Experience:</strong> No more advertisements</li>
                <li><strong>Priority Support:</strong> Get help faster when you need it</li>
            </ul>
        </div>

        <div class="details">
            <h3>Recommended plans for you:</h3>
            <?php foreach ($recommendedPlans as $index => $plan): ?>
                <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
                    <h4 style="margin-bottom: 8px;"><?= $plan->name ?> (<?= $plan->period ?>)</h4>
                    <?php
                    $description = json_decode($plan->description, true);
                    if (isset($description['subTitle'])):
                        ?>
                        <p><em><?= $description['subTitle'] ?></em></p>
                    <?php endif; ?>

                    <?php if (isset($description['lines']) && is_array($description['lines']) && count($description['lines']) > 0): ?>
                        <ul>
                            <?php foreach (array_slice($description['lines'], 0, 3) as $feature): ?>
                                <li><?= $feature ?></li>
                            <?php endforeach; ?>
                            <?php if (count($description['lines']) > 3): ?>
                                <li>And more...</li>
                            <?php endif; ?>
                        </ul>
                    <?php endif; ?>
                    <div style="margin-top: 12px;">
                        <a href="<?= Yii::$app->params['androidAppLink'] ?>" class="button-primary">Subscribe Now</a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="message">
            <p>Don't miss out on the full potential of our app! Upgrade today to continue
                creating <?= strtolower($limitTypeName) ?> without interruption.</p>
        </div>

        <?= $this->render('@common/mail/layouts/_footer', ['user' => $user]) ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
</body>
</html>