<?php /* @var $this yii\web\View */ ?>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style type="text/css">
/* Reset styles */
body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}

body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-family: '<PERSON><PERSON>ri', Arial, sans-serif;
    background-color: #f6f6f6;
    color: #333333;
    line-height: 1.5;
}

/* Main wrapper */
.wrapper {
    width: 100%;
    table-layout: fixed;
    background-color: #f6f6f6;
    padding: 20px 0;
}

/* Email container */
.email-container {
    max-width: 650px;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Content cell */
.content-cell {
    padding: 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #2B5896;
    font-weight: 600;
}

p {
    margin-top: 0;
    margin-bottom: 16px;
}

a {
    color: #2B5896;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Buttons */
.button-primary {
    background-color: #2B5896;
    color: #ffffff !important;
    padding: 12px 24px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    margin: 8px 0;
    border: 1px solid #2B5896;
}

.button-primary:hover {
    background-color: #1e4275;
    text-decoration: none;
}

.button-secondary {
    background-color: #25D366;
    padding: 12px 24px;
    border-radius: 4px;
    color: #ffffff !important;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    margin: 8px 0;
    border: 1px solid #25D366;
}

.button-secondary:hover {
    background-color: #128C7E;
    text-decoration: none;
}

/* Alerts */
.alert {
    padding: 16px;
    margin: 16px 0;
    border-radius: 4px;
    border-left: 4px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Tables */
.responsive-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    table-layout: fixed;
}

.responsive-table th {
    background-color: #f8f9fa;
    text-align: left;
    padding: 12px;
    border-bottom: 2px solid #e0e0e0;
}

.responsive-table td {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.responsive-table tr:last-child td {
    border-bottom: none;
}

/* Layout Components */
.header-table {
    width: 100%;
    margin-bottom: 24px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 16px;
}

.header-logo {
    max-height: 60px;
}

.header-logo-cell {
    text-align: left;
    width: 100%;
}

.app-name {
    font-size: 20px;
    font-weight: bold;
    color: #2B5896;
}

.footer {
    margin-top: 32px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
    color: #6c757d;
    font-size: 14px;
}

.greeting {
    font-size: 20px;
    margin-bottom: 24px;
    color: #2B5896;
}

.message {
    margin-bottom: 24px;
}

.details {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    margin: 16px 0;
}

/* WhatsApp Button */
.whatsapp-button {
    display: inline-block;
    background-color: #25D366;
    color: white !important;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    margin: 8px 0;
    font-weight: bold;
}

.whatsapp-button:hover {
    background-color: #128C7E;
    text-decoration: none;
}

.whatsapp-logo {
    height: 16px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Responsive Styles */
@media screen and (max-width: 600px) {
    .email-container {
        width: 100% !important;
    }

    .content-cell {
        padding: 15px !important;
    }

    .mobile-stack {
        display: block !important;
        width: 100% !important;
    }

    .responsive-table {
        width: 100% !important;
    }

    .header-logo-cell {
        display: block !important;
        width: 100% !important;
        text-align: center !important;
    }

    .button-primary, .button-secondary {
        display: block !important;
        text-align: center !important;
        width: 100% !important;
    }
}

/* Dark Mode Support - Limited in Gmail but included for other clients */
@media (prefers-color-scheme: dark) {
    body, .wrapper {
        background-color: #2d2d2d;
    }

    .email-container {
        background-color: #2d2d2d;
        color: #f0f0f0;
    }

    h1, h2, h3, h4, h5, h6 {
        color: #f0f0f0;
    }

    a {
        color: #6ea8fe;
    }

    .details {
        background-color: #3d3d3d;
    }

    .responsive-table th {
        background-color: #3d3d3d;
    }

    .responsive-table td, .responsive-table th {
        border-color: #4d4d4d;
    }

    .footer {
        border-color: #4d4d4d;
    }
}

/* Gmail-specific fixes */
.gmail-fix {
    display: none;
    line-height: 0;
    font-size: 0;
    max-height: 0;
    overflow: hidden;
}
</style>
