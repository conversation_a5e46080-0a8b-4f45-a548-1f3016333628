<?php

namespace common\jobs;

use common\services\EmailClient;
use common\services\EmailService;
use Yii;
use yii\queue\Queue;

/**
 * SendAdmin<PERSON>mail<PERSON><PERSON> sends an email to the admin using the EmailService.
 *
 * This job can be used to send admin emails asynchronously.
 */
class SendAdminEmail<PERSON>ob extends Job
{
    /**
     * @var string The email template to use
     */
    public $template;

    /**
     * @var array The parameters to pass to the template
     */
    public $params;

    /**
     * @var string The subject of the email
     */
    public $subject;

    /**
     * @var bool Whether to use SMTP for sending the email
     */
    public $useSmtp = true;

    /**
     * Constructor
     *
     * @param array $config The configuration array
     */
    public function __construct($config = [])
    {
        // Set default values
        $this->tries = 3;
        $this->retryAfter = 300; // 5 minutes
        $this->timeout = 60;
        $this->queue = 'emails';

        parent::__construct($config);
    }

    /**
     * Handles the job
     *
     * @param Queue|null $queue The queue that is executing this job (optional)
     * @return bool Whether the email was sent successfully
     */
    public function handle($queue = null)
    {
        try {
            $emailService = new EmailService($this->useSmtp);
            $date = EmailClient::getInstance()->getLocalizedDate();

            // Set the timezone to IST (Indian Standard Time)
            date_default_timezone_set('Asia/Kolkata');
            // Call sendMail directly to avoid circular reference
            return $emailService->sendMail(
                EmailClient::getInstance()->getMailer($this->useSmtp),
                ['html' => $this->template],
                $this->params,
                Yii::$app->params['adminEmail'],
                $this->subject . " - {$date}",
                [] // No additional options
            );
        } catch (\Exception $e) {
            Yii::error("Failed to send admin email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Called when the job has failed
     *
     * @param \Exception $exception The exception that caused the job to fail
     * @return void
     */
    public function failed($exception)
    {
        Yii::error("Failed to send admin email: " . $exception->getMessage());
    }
}
