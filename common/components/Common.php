<?php

namespace common\components;

use common\models\Business;
use common\models\Customer;
use common\models\ProductCategory;
use common\models\Region;
use common\models\User;
use Yii;
use yii\base\Component;

class Common extends Component
{
    /*
    * Display Labels
    */

    public function displayLabel($type, $value)
    {
        $return = '';
        if ($type == 'isDeleted') {
            if ($value == '0') {
                return 'Active';
            } else if ($value == '1') {
                return 'Deactive';
            }
        }
        if ($type == 'isAdmin') {
            if ($value == '0') {
                return 'No';
            } else if ($value == '1') {
                return 'Yes';
            }
        }
        return $return;
    }

    public function getParentName($type = '', $id = null)
    {
        $return = null;
        if (!empty($id) && !empty($type)) {
            if ($type == 'USER') {
                $user_name = User::findOne($id);
                if (!empty($user_name)) {
                    if (!empty($user_name->name)) {
                        $return = $user_name->name;
                    } else if (!empty($user_name->name)) {
                        $return = $user_name->name;
                    }
                }
            } else if ($type == 'CUSTOMER') {
                $customer_name = Customer::findOne($id);
                if (!empty($customer_name)) {
                    if (!empty($customer_name->name)) {
                        $return = $customer_name->name;
                    } else if (!empty($customer_name->name)) {
                        $return = $customer_name->name;
                    }
                }
            } else if ($type == 'COMPANY') {
                $customer = Customer::findOne($id);
                if (!empty($customer)) {
                    if (!empty($customer->companyName)) {
                        $return = $customer->companyName;
                    }
                }
            } else if ($type == 'BUSINESS') {
                $business_name = Business::findOne($id);
                if (!empty($business_name)) {
                    if (!empty($business_name->name)) {
                        $return = $business_name->name;
                    }
                }
            } else if ($type == 'CATEGORY') {
                $category_name = ProductCategory::findOne($id);
                if (!empty($category_name)) {
                    if (!empty($category_name->name)) {
                        $return = $category_name->name;
                    }
                }
            } else if ($type == 'REGION') {
                $region_name = Region::findOne($id);
                if (!empty($region_name)) {
                    if (!empty($region_name->name)) {
                        $return = $region_name->name;
                    }
                }
            }

        }
        return $return;
    }

    /*
     * Format data
     */

    public function formatDate($date_str = '', $type = '')
    {
        $return = $date_str;
        if (!empty($date_str)) {
            if ($type == '1') {
                $return = date('Y-m-d H:i:s', strtotime(str_replace('/', '-', $date_str)));
            } else {
                $return = date('Y-m-d', strtotime(str_replace('/', '-', $date_str)));
            }
        } else {
            $return = '';
        }
        return $return;
    }

    /**
     * Custom Formated date
     * @date_str: Datetime format with respect to timestamp
     * @type: For get different formated date time string
     */
    public function formatDateCustom($date_str = '', $type = '')
    {
        $return = $date_str;
        if (!empty($date_str)) {
            if ($type == '1') {
                $return = date('d/m/Y', strtotime($date_str));
            } else if ($type == '2') {
                $return = date('d/m/Y H:i:s', strtotime($date_str));
            } else if ($type == '3') {
                $return = date('d-m-Y', strtotime($date_str));
            } else if ($type == '4') {
                $return = date('d-m-Y H:i:s', strtotime($date_str));
            } else if ($type == '5') {
                $return = date('Y-m-d H:i:s', strtotime($date_str));
            } else if ($type == '6') {
                $return = date('d/m/Y H:i', strtotime($date_str));
            } else if ($type == '7') {
                $return = date('H:i:s', strtotime($date_str));
            } else if ($type == '8') {
                $return = date('d/m/Y h:i a', strtotime($date_str));
            } else if ($type == '9') {
                $return = date('d/m/Y h:i A', strtotime($date_str));
            }
        } else {
            $return = '';
        }
        return $return;
    }

    public function setLoginSessionData()
    {
        $session = Yii::$app->session;
        $model_reference = User::find()->where("id = '" . Yii::$app->user->identity->getId() . "'")->one();
        if (!empty($model_reference) && (empty($session['user_id']) || empty($session['user_role']))) {
            $session['user_id'] = !empty($model_reference->id) ? $model_reference->id : 0;
            $session['user_role'] = !empty($model_reference->designation) ? $model_reference->designation : 'sales_person';
            $session['isAdmin'] = !empty($model_reference->isAdmin) ? $model_reference->isAdmin : 0;
        }
    }
}