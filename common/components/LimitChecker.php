<?php

namespace common\components;

use common\models\User;
use common\services\EmailService;
use Yii;
use yii\base\Component;

/**
 * LimitChecker component for checking and handling usage limits
 */
class LimitChecker extends Component
{
    /**
     * Check if user has reached the final limit for a specific feature
     * 
     * @param User $user The user to check
     * @param string $limitType The type of limit to check (e.g., 'quotation', 'invoice')
     * @param int $currentCount The current count of items
     * @return bool Whether the user has reached the final limit
     */
    public function checkFinalLimit(User $user, $limitType, $currentCount): bool
    {
        $forceSubscriptionCount = Yii::$app->params['FORCE_SUBSCRIPTION_COUNT'] ?? 40;
        
        // If user is already a pro user, they haven't reached the limit
        if ($user->isProUser) {
            return false;
        }
        
        // Check if user has reached the force subscription count
        if ($currentCount >= $forceSubscriptionCount) {
            // Send email notification about reaching the limit
            $this->sendLimitReachedEmail($user, $limitType);
            return true;
        }
        
        return false;
    }
    
    /**
     * Send email notification when user reaches the final limit
     * 
     * @param User $user The user who reached the limit
     * @param string $limitType The type of limit reached
     * @return bool Whether the email was sent successfully
     */
    protected function sendLimitReachedEmail(User $user, $limitType)
    {
        $emailService = new EmailService(true);
        return $emailService->sendFinalLimitReachedEmail($user, $limitType);
    }
}