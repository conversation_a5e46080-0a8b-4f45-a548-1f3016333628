<?php


namespace common\helpers;


use Carbon\Carbon;
use common\models\Business;
use common\models\enum\Key;
use common\models\enum\PlanDuration;
use common\models\enum\ProviderType;
use common\models\enum\SubscriptionStatus;
use common\models\Region;
use common\models\StripePaymentIntent;
use common\models\StripeUser;
use common\models\Subscription;
use common\models\SubscriptionLog;
use common\models\SubscriptionPlan;
use common\models\SubscriptionPlanPricing;
use common\models\User;
use JetBrains\PhpStorm\Pure;
use Razorpay\Api\Errors\Error as RazorpayError;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\Event;
use Stripe\Exception\ApiErrorException;
use Stripe\Invoice;
use Stripe\PaymentIntent;
use Stripe\Plan;
use Stripe\Price;
use Stripe\Product;
use Stripe\Refund;
use Stripe\Stripe;
use Stripe\Subscription as StripeSubscription;
use Stripe\SubscriptionItem;
use UnexpectedValueException;
use Yii;
use yii\base\Exception;
use yii\db\Query;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;

class StripeHelper
{
    /**
     * @var StripeHelper
     */
    private static $instance = null;
    private $useOldAccount;
    public $account;
    public $secretKey;
    public $publishableKey;

    public $success = true;
    public $errorCode = 0;
    public $error;


    public static function sharedInstanceByRegion($region)
    {
        $stripeAccounts = env(Key::STRIPE_ACCOUNTS);
        $accountName = $region === "IN" ? Key::INDIA_ACCOUNT : Key::FOREIGN_ACCOUNT;
        $account = $stripeAccounts[$accountName];
        return self::sharedInstance($account);
    }

    public static function sharedInstance($account): StripeHelper
    {
        if (self::$instance === null) {
            self::$instance = new static;
        }
        self:: setApiKey($account);
        self::$instance->error = null;
        self::$instance->success = true;
        self::$instance->errorCode = 0;
        return self::$instance;
    }

    public static function setApiKey($account = null)
    {
        $stripeAccounts = env(Key::STRIPE_ACCOUNTS);
        if (self::$instance === null) {
            self::$instance = new static;
        }
        if ($account === null) {
            $account = $stripeAccounts[Key::DEFAULT_ACCOUNT];
        }
        self::$instance->account = $account;
        self::$instance->secretKey = $stripeAccounts[$account][Key::SECRET_KEY];
        self::$instance->publishableKey = $stripeAccounts[$account][Key::PUBLISHABLE_KEY];
        Stripe::setApiKey(self::$instance->secretKey);
    }

    public static function getCurrentAccount()
    {
        return self::$instance?->account;
    }


    public function getCustomerPortalUrl(User $user)
    {
        // This is the URL to which users are redirected after managing their billing
        // with the customer portal.
        $returnUrl = "https://praxinfo.com/contact-us";
        $stripeCustomerId = $this->getStripeCustomerId($user);
        if (empty($stripeCustomerId)) {
            throw new Exception("You don't have an active subscription! Please contact support, if you are having any issues!");
        }
        try {
            return \Stripe\BillingPortal\Session::create([
                'customer' => $stripeCustomerId,
                'return_url' => $returnUrl,
            ])->url;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function retrieveSession($checkoutSessionId): ?Session
    {
        return Session::retrieve($checkoutSessionId);
    }

    public function createCheckoutSession(SubscriptionPlan $plan, $userId): ?Session
    {

        // Create new Checkout Session for the order
        // Other optional params include:
        // [billing_address_collection] - to display billing address details on the page
        // [customer] - if you have an existing Stripe Customer ID
        // [payment_intent_data] - lets capture the payment later
        // [customer_email] - lets you prefill the email input in the form
        // [automatic_tax] - to automatically calculate sales tax, VAT and GST in the checkout page
        // For full details see https://stripe.com/docs/api/checkout/sessions/create

        // ?session_id={CHECKOUT_SESSION_ID} means the redirect will have the session ID set as a query param
        $successUrl = UrlHelper::billingUrl('subscription/stripe-success?sessionId={CHECKOUT_SESSION_ID}');
        $cancelUrl = UrlHelper::billingUrl('subscription/stripe-cancel');
        $user = User::findByPk($userId);
        if ($user === null) {
            throw new Exception("User not found!");
        }
        $checkoutSessionData = [
            'line_items' => [[
                'price' => $plan->stripePlanId,
                'quantity' => 1,
            ]],
            'mode' => 'subscription',
            'success_url' => $successUrl,
            'cancel_url' => $cancelUrl,
            'allow_promotion_codes' => true,
            'metadata' => [
                'planId' => $plan->id,
                'userId' => $user->id,
            ],
        ];
        if ($user->stripeCustomerId) { // if customer data available!
            $checkoutSessionData['customer'] = $user->stripeCustomerId;
        } else {
            $checkoutSessionData['customer_email'] = $user->email;
        }
        return Session::create($checkoutSessionData);
    }

    public function createStripePrice($planId)
    {

        $plan = SubscriptionPlan::findByPk($planId);
        if ($plan === null) {
            throw new Exception("Plan doesn't exists!");
        }
        if (!empty($plan->stripePlanId)) {
            throw new Exception("Stripe Plan ID already exists!");
        }

        $interval = ["monthly" => "month", "yearly" => "year"];

        // Create a product object
        $product = Product::create([
            'name' => $plan->name,
            'type' => 'service',
        ]);

        $currencyOptions = StripeCurrencyHelper::getCurrencyOptions($plan->price);
// Create a recurring price for INR
        $priceData = [
            'product' => $product->id,
            'unit_amount' => $plan->price * 100, // amount in paise
            'currency' => 'inr',
            'currency_options' => $currencyOptions,
        ];
        if ($plan->isRecurring) {
            $priceData['recurring'] = [
                'interval' => $interval[$plan->period],
                'interval_count' => 1
            ];
        }
        $price = Price::create($priceData);
        $plan->stripePlanId = $price->id;
        $plan->save();
        SubscriptionPlanPricing::updateAll(['isActive' => 0], ['planId' => $plan->id, 'provider' => ProviderType::STRIPE, 'providerAccount' => $this->account, 'isActive' => 1]);
        return $this->generatePrice($price->id);
    }

    #[Pure] public function convertNormalPriceToUnits($price, $currency)
    {
        $zeroDecimalCurrencies = StripeCurrencyHelper::getZeroDecimalCurrencies();
        if ($zeroDecimalCurrencies === null) {
            $zeroDecimalCurrencies = StripeCurrencyHelper::getZeroDecimalCurrencies();
        }
        if (!in_array(strtoupper($currency), $zeroDecimalCurrencies) || strtoupper($currency) == "UGX") {
            $price = $price * 100.00;
        }
        return $price;
    }

    #[Pure] public function convertUnitPriceToNormal($price, $currency)
    {
        $zeroDecimalCurrencies = StripeCurrencyHelper::getZeroDecimalCurrencies();
        if ($zeroDecimalCurrencies === null) {
            $zeroDecimalCurrencies = StripeCurrencyHelper::getZeroDecimalCurrencies();
        }
        if (!in_array($currency, $zeroDecimalCurrencies)) {
            $price = $price / 100.00;
        }
        return $price;
    }

    public function generatePrice($stripePriceId)
    {
        $plan = SubscriptionPlan::find()->byStripePlanId($stripePriceId)->one();
        if ($plan === null) {
            throw new Exception("Stripe Plan not found in database!");
        }
        $stripePriceData = Price::retrieve(['id' => $stripePriceId, 'expand' => ['currency_options']]);


        foreach ($stripePriceData->currency_options->toArray() as $currency => $priceData) {
            $mainPrice = $priceData['unit_amount'];
            $price = $this->convertUnitPriceToNormal($mainPrice, $currency);
            $region = Region::findByCurrencyCode($currency);
            $locale = "en_US";
            if ($region !== null) {
                $locale = $region->locale;
            }
            $fmtCurrency = formatAsCurrencyByLocale($price, $locale);
            $pricing = SubscriptionPlanPricing::findByProviderIdAndCurrency($stripePriceId, $currency);
            if ($pricing === null) {
                $pricing = new SubscriptionPlanPricing();
                $pricing->provider = ProviderType::STRIPE;
                $pricing->providerAccount = $this->account;
                $pricing->providerId = $stripePriceId;
                $pricing->planId = $plan->id;
            }
            $pricing->originalPrice = $price;
            $pricing->price = $price;
            $pricing->fmtPrice = $fmtCurrency;
            $pricing->currency = $currency;
            $pricing->save(false);
        }
        return $plan;
    }

    /**
     * Calculate the prorated refund amount for a subscription.
     *
     * @param string $subscriptionId The Stripe subscription id.
     * @return int The prorated refund amount in the smallest currency unit (e.g., cents).
     * @throws ApiErrorException
     */
    public function calculateProratedRefundAmount($subscriptionId)
    {
        $stripeSubscription = StripeSubscription::Retrieve($subscriptionId);
        $currentPeriodEnd = $stripeSubscription->current_period_end;
        $currentPeriodStart = $stripeSubscription->current_period_start;
        $currentBillingCycleSeconds = $currentPeriodEnd - $currentPeriodStart;
        $remainingBillingCycleSeconds = $currentPeriodEnd - time();

        // Calculate the prorated amount based on the remaining time in the billing period
        /** @var SubscriptionItem $stripeItem */
        $stripeItem = $stripeSubscription->items->first(); // last transaction item

        $totalAmount = $stripeItem->plan->amount;
        $proratedAmount = ($totalAmount / $currentBillingCycleSeconds) * $remainingBillingCycleSeconds;

        // Round the prorated amount to the nearest smallest currency unit (e.g., cents)
        $proratedAmount = round($proratedAmount);

        return $proratedAmount;
    }


    /**
     * Cancels the subscription on immediate basis and refund the prorated amount.
     *
     * @param StripeSubscription $stripeSubscription The Stripe subscription object.
     * @return array
     * @throws ApiErrorException
     */
    public function cancelSubscription($subscriptionId)
    {
        $stripeSubscription = StripeSubscription::Retrieve($subscriptionId);
        $refund = $this->stripeRefund($stripeSubscription, $subscriptionId);

        $stripeSubscription = $stripeSubscription->delete();
        return ['refund' => $refund, 'stripeSubscription' => $stripeSubscription];
    }

    /**
     * Sends payment notification email to admin
     */
    public function sendRefundSuccessMail($stripeSubscriptionId, $refundAmount)
    {
        // Set the timezone to IST (Indian Standard Time)
        date_default_timezone_set('Asia/Kolkata');

        $todayDate = date("d.m.Y");
        $subscription = Subscription::find()->byProviderSubscriptionId($stripeSubscriptionId)->one(); // in case receipt is sent again
        $user = null;
        $business = null;
        if ($subscription) {
            $user = $subscription->user;
            $business = $subscription->user->business;
        }
        $composeMail = Yii::$app
            ->mailer
            ->compose(
                ['html' => 'notifications/stripe-refund-success'],
                ['user' => $user, 'business' => $business, 'subscription' => $subscription,
                    'refundAmount' => $refundAmount],
            )
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']])
            ->setTo(Yii::$app->params['adminEmail'])
            ->setSubject('Subscription Refund - Success ' . Yii::$app->name . " - $todayDate");
        $isSent = $composeMail->send();
        if ($isSent !== true) {
            Yii::error("Error occurred in sending subscription refund email");
        }
    }


    /**
     * Sends payment notification email to admin
     */
    public function sendCancellationFailedEmail($stripeSubscriptionId, $refundAmount, $failureMessage)
    {
        // Set the timezone to IST (Indian Standard Time)
        date_default_timezone_set('Asia/Kolkata');

        $todayDate = date("d.m.Y");
        $subscription = Subscription::find()->byProviderSubscriptionId($stripeSubscriptionId)->one(); // in case receipt is sent again
        $user = null;
        $business = null;
        if ($subscription) {
            $user = $subscription->user;
            $business = $subscription->user->business;
        }
        $composeMail = Yii::$app
            ->mailer
            ->compose(
                ['html' => 'notifications/stripe-refund-failed'],
                ['user' => $user, 'business' => $business, 'subscription' => $subscription,
                    'refundAmount' => $refundAmount, 'failureMessage' => $failureMessage],
            )
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']])
            ->setTo(Yii::$app->params['adminEmail'])
            ->setSubject('Subscription Refund - Failed ' . Yii::$app->name . " - $todayDate");
        $isSent = $composeMail->send();
        if ($isSent !== true) {
            Yii::error("Error occurred in sending subscription refund email");
        }
    }

    public function setPaymentMethodBySubscriptionId($subscriptionId)
    {
        $invoices = \Stripe\Invoice::all(["subscription" => $subscriptionId]);
        foreach ($invoices->autoPagingIterator() as $invoice) {
            if ($invoice->status == 'paid') {
                $charge = \Stripe\Charge::retrieve($invoice->charge);
                $paymentMethodId = $charge->payment_method;
                \Stripe\Customer::update(
                    $invoice->customer,
                    [
                        'invoice_settings' => [
                            'default_payment_method' => $paymentMethodId,
                        ],
                    ]
                );
                return true;
            }
        }
        return false;
    }

    public function setDefaultPaymentMethod($stripeSubscription, $stripeInvoice = null)
    {
        try {
            if ($stripeInvoice == null) {
                $stripeInvoice = \Stripe\Invoice::retrieve($stripeSubscription->latest_invoice);
            }
            if ($stripeInvoice->status == 'paid') {
                $charge = \Stripe\Charge::retrieve($stripeInvoice->charge);
                $paymentMethodId = $charge->payment_method;
                \Stripe\Customer::update(
                    $stripeSubscription->customer,
                    [
                        'invoice_settings' => [
                            'default_payment_method' => $paymentMethodId,
                        ],
                    ]
                );
            }
        } catch (\Exception $e) {
            $message = "Error occurs while processing subscription id : {$stripeSubscription}" . " \n" . $e->getMessage();
            $message .= " \n\n" . $e->getTraceAsString();
            $data = ['stripeSubscriptionId' => $stripeSubscription];
            logError($message, $data);
        }
    }

    /**
     * @throws ApiErrorException
     */
    public
    function createCustomer(User $user)
    {
        $region = Region::findByCode($user->business->regionCode);
        $currencyCode = $region?->currencyCode;
        $supportedCurrency = StripeCurrencyHelper::getSupportedCurrencies();
        if ($currencyCode === null || !in_array($currencyCode, $supportedCurrency, false)) {
            $currencyCode = "USD";
        }
        $stripeUser = StripeUser::findByUserIdAndAccount($user->id, $this->account);
        if ($stripeUser) {
            $stripeCustomer = Customer::retrieve($stripeUser->stripeCustomerId);
            if ($stripeCustomer && isset($stripeCustomer->metadata)) {
                $metaData = $stripeCustomer->metadata->toArray();
                $stripeCurrencyCode = $metaData['currencyCode'];
                if ($currencyCode === $stripeCurrencyCode) {
                    return $stripeCustomer;
                }
            }
        }
        $userBusiness = $user->business;
        $stripeCustomer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'phone' => $user->phoneNumber,
            "address" => ["line1" => trim($userBusiness->addressLine1 ?? "") ?: "-", "line2" => trim($userBusiness->addressLine2 ?? "") ?: "-", "city" => "-", "country" => $userBusiness->regionCode, "postal_code" => "-", "state" => "-"],
            'metadata' => [
                'userId' => $user->id,
                'businessId' => $userBusiness->id,
                'currencyCode' => $currencyCode,
            ],
        ]);

        if ($stripeUser === null) {
            $stripeUser = new StripeUser();
            $stripeUser->userId = $user->id;
            $stripeUser->stripeAccount = $this->account;
        }
        $stripeUser->stripeCustomerId = $stripeCustomer->id;
        $stripeUser->save();

        return $stripeCustomer;
    }

    public function getCustomerFromStripeSubscriptionId($stripeSubscriptionId)
    {
        $stripeSubscription = StripeSubscription::Retrieve($stripeSubscriptionId);
        return $stripeSubscription->customer;
    }


    public static function removeIncompleteTransactions()
    {
        return Subscription::deleteAll("status = 'incomplete_expired' OR status = 'incomplete' AND isActive = 0"); // Remove incomplete subscription records!
    }

    public static function removePendingIntents($userId)
    {
        StripePaymentIntent::deleteAll(
            "userId = :userId AND paymentIntentClientSecret IS NOT NULL AND isActive = 0",
            [
                'userId' => $userId
            ]
        ); // Remove old unused subscription records!
    }

    /**
     * @param User $user
     * @param SubscriptionPlan $plan
     * @param string $intentType
     * @return string
     * @throws ApiErrorException
     * @throws Exception
     */
    public function createPaymentIntent(User $user, SubscriptionPlan $plan)
    {
        $business = $user->business;
        $activeSubscription = $business->getActiveSubscriptionByGroup($plan->group);

        if ($activeSubscription && $activeSubscription->plan->price > $plan->price) {
            throw new Exception("You cannot downgrade from the current plan!");
        }

        $paymentIntent = $this->createStripePaymentIntent($user, $plan);
        if ($plan->isRecurring) {
            if (!isset($paymentIntent->latest_invoice->payment_intent->client_secret)) {
                Yii::error("\n Subscription payment intent create issue! \n" . json_encode($paymentIntent, JSON_PRETTY_PRINT));
                throw new Exception("Error in subscription payment intent creation!");
            }
            $this->storeSubscriptionPaymentIntent($paymentIntent, $user, $plan);
            return $paymentIntent->latest_invoice->payment_intent->client_secret;
        } else {
            $isSent = Subscription::SendPaymentIntentEmailNotification($user, $plan, ProviderType::STRIPE);
        }

        if (!isset($paymentIntent->client_secret)) {
            Yii::error("\n Onetime Plan payment intent create issue! \n" . json_encode($paymentIntent, JSON_PRETTY_PRINT));
            throw new Exception("Error in one-time payment intent creation!");
        }
        return $paymentIntent->client_secret;
    }

    /**
     * @param User $user
     * @param SubscriptionPlan $plan
     * @param string $intentType
     * @return StripeSubscription|\Stripe\PaymentIntent
     * @throws ApiErrorException
     * @throws Exception
     */
    public function createStripePaymentIntent(User $user, SubscriptionPlan $plan)
    {
        $stripeCustomer = $this->createCustomer($user);
        $metaData = $stripeCustomer->metadata->toArray();
        $currencyCode = $metaData['currencyCode'];
        $stripePlan = SubscriptionPlanPricing::findByProviderAndCurrency($plan->id, ProviderType::STRIPE, $this->account, $currencyCode);

        if ($stripePlan === null) {
            Yii::error("Plan ID Not found for :: \nPlan ID : " . $plan->id . "\nProvider :: " . ProviderType::STRIPE . "\nProviderAccount :: " . $this->account . "\nCurrency :: " . $currencyCode);
            throw new Exception("Plan ID Not found for :: \nPlan ID : " . $plan->id . "\nProvider :: " . ProviderType::STRIPE);
        }

        $stripePlanId = $stripePlan->providerId;
        $intentData = [
            'currency' => $currencyCode,
            'description' => $plan->name,
            'customer' => $stripeCustomer->id,
            'metadata' => [
                'appId' => APP_ID,
                'isRecurring' => $plan->isRecurring,
                'planId' => $plan->id,
                'userId' => $user->id,
                'businessId' => $user->business->id,
            ],
        ];

        if ($plan->isRecurring) {
            $intentData['items'] = [['plan' => $stripePlanId]];
            $intentData['payment_behavior'] = 'default_incomplete';
            $intentData['expand'] = ['latest_invoice.payment_intent'];
            return StripeSubscription::create($intentData);
        }
        // when onetime product purchase is there!
        $intentData['amount'] = $this->convertNormalPriceToUnits($stripePlan->price, $currencyCode);
        return \Stripe\PaymentIntent::create($intentData);
    }


    /**
     * @throws ApiErrorException
     * @throws Exception
     */
    public
    function successPayment(string $checkoutSessionId, $userId): void
    {
        $checkoutSession = Session::retrieve($checkoutSessionId);
        $user = User::findByPk($userId);
        if ($user === null) {
            throw new Exception("User not found!");
        }

        $stripeUser = StripeUser::findByUserIdAndAccount($userId, $this->account);
        if ($stripeUser === null) {
            $stripeUser = new StripeUser();
            $stripeUser->userId = $user->id;
            $stripeUser->stripeAccount = $this->account;
        }
        $stripeUser->stripeCustomerId = $checkoutSession->customer;
        $stripeUser->save();

        StripeSubscription::update(
            $checkoutSession->subscription,
            [
                'cancel_at_period_end' => true,
            ]
        );

        $stripeSubscription = StripeSubscription::Retrieve($checkoutSession->subscription);
        if ($stripeSubscription) {
            $this->logWebhookEvent($stripeSubscription, 'subscription.checkout.success');
            $this->processSubscription($stripeSubscription);
            return;
        }
        throw new Exception("Subscription not found!");
    }

    public
    function logWebhookEvent($stripeSubscription, $event): void
    {
        /** @var SubscriptionItem $stripeItem */
        $stripeItem = $stripeSubscription->items->first(); // last transaction item

        /** @var Plan $stripePlan */
        $stripePlan = $stripeItem->plan;
        $plan = SubscriptionPlanPricing::find()->byStripePlanId($stripePlan->id)->one()?->plan;
        if ($plan === null) {
            throw new Exception("Plan not found with pricing ID : {$stripePlan->id} ");
        }
        $user = $this->getUserByStripeCustomerId($stripeSubscription->customer);
        if ($user === null) {
            throw new Exception("User not found with customer ID : {$stripeSubscription->customer} ");
        }
        $eventLog = new SubscriptionLog();
        $eventLog->providerSubscriptionId = $stripeSubscription->id;
        $eventLog->userId = $user->id;
        // update subscription data!
        $eventLog->planId = $plan->id;
        $eventLog->provider = ProviderType::STRIPE;
        $eventLog->providerAccount = $this->account;
        $eventLog->providerPlanId = $stripePlan->id;
        $eventLog->startDate = $stripeSubscription->current_period_start; // seconds
        if ($plan->isRecurring) {
            $eventLog->endDate = $stripeSubscription->current_period_end; // seconds
        } else {
            $currentTimestamp = Carbon::now()->timestamp;
            $eventLog->startDate = $currentTimestamp; // ms to seconds
            $eventLog->endDate = $currentTimestamp + $plan->interval * PlanDuration::getInSeconds($plan->period); // 1 x weekly duration in seconds
        }
        $eventLog->status = $stripeSubscription->status;
        if ($stripeSubscription->cancel_at !== null & $stripeSubscription->cancel_at > 0) {
            $eventLog->endDate = $stripeSubscription->cancel_at;
        }
        if (!$eventLog->validate()) {
            $errMessage = $eventLog->getErrorSummary(true)[0];
            Yii::error($errMessage);
            throw new Exception($errMessage);
        }
        $eventLog->event = $event;
        $eventLog->payloadData = Json::encode($stripeSubscription);
        $eventLog->save();
        $eventLog->refresh();
    }

    public
    function processSubscription(StripeSubscription|PaymentIntent $stripeSubscription)
    {
        /** @var SubscriptionItem $stripeItem */
        if ($stripeSubscription instanceof StripeSubscription) {
            $stripeItem = $stripeSubscription->items->first(); // last transaction item
            /** @var Plan $stripePlan */
            $stripePlan = $stripeItem?->plan;
            $stripePlanId = $stripePlan->id;
            $stripeCustomerId = $stripeSubscription->customer;
            $stripeSubscriptionId = $stripeSubscription->id;
            $stripeSubscriptionStatus = $stripeSubscription->status;
            $invoiceId = $stripeSubscription->latest_invoice;
            if (is_string($invoiceId)) {
                $stripeInvoice = \Stripe\Invoice::retrieve($invoiceId);
            } else {
                $stripeInvoice = $stripeSubscription->latest_invoice;
            }
            if ($stripeSubscriptionStatus == SubscriptionStatus::ACTIVE) {
                $this->setDefaultPaymentMethod($stripeSubscription, $stripeInvoice);
            }
            $stripeSubscriptionCreated = $stripeSubscription->created;
            $stripeSubscriptionCurrentPeriodStart = $stripeSubscription->current_period_start;
            $stripeSubscriptionCurrentPeriodEnd = $stripeSubscription->current_period_end;
            $stripeSubscriptionCancelAt = $stripeSubscription->cancel_at;
            $stripePaymentIntentClientSecret = $stripeSubscription?->latest_invoice?->payment_intent?->client_secret ?? null;
            /** @var SubscriptionPlan $plan */
            $plan = SubscriptionPlanPricing::find()->byStripePlanId($stripePlan->id)->one()?->plan;
        } else {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($stripeSubscription->id); // Replace 'pi_123456789' with your actual PaymentIntent ID
            $metaData = $paymentIntent->metadata;
            $planId = $metaData['planId'] ?? null;
            /** @var SubscriptionPlan $plan */
            $plan = SubscriptionPlan::findByPk($planId);
            $stripePlanId = $plan->stripePlanId;
            $stripeInvoice = null;
            if ($plan === null) {
                $message = "Plan not found in db - PlanID : " . $planId;
                $message .= "\n\n" . print_r($paymentIntent->metadata, true) . "\n\n";
                throw new Exception($message);
            }
            /** @var Plan $stripePlan */
            $stripeCustomerId = $paymentIntent->customer ?? null;
            $stripeSubscriptionId = $paymentIntent->id;
            $stripeSubscriptionStatus = $stripeSubscription->status;
            $stripeSubscriptionCreated = $stripeSubscription->created;
            $stripeSubscriptionCurrentPeriodStart = $stripeSubscription->current_period_start;
            $stripeSubscriptionCurrentPeriodEnd = $stripeSubscription->current_period_end;
            $stripeSubscriptionCancelAt = $stripeSubscription->cancel_at;
            $stripePaymentIntentClientSecret = $stripeSubscription?->latest_invoice?->payment_intent?->client_secret ?? null;
        }

        $user = $this->getUserByStripeCustomerId($stripeCustomerId);
        if ($user === null) {
            $message = "User is not associated OR Invalid user! for customer id : " . $stripeCustomerId;
            throw new Exception($message);
        }
        $business = $user->business;
        $subscription = Subscription::find()->byProviderSubscriptionId($stripeSubscriptionId)->one(); // in case receipt is sent again
        if ($subscription && $subscription->userId !== $user->id) {
            $message = "Subscription from other user";
            Yii::error($message);
            throw new Exception($message);
        }

        $activeSubscription = $business->getActiveSubscriptionByGroup($plan->group);
//        if ($activeSubscription && $activeSubscription->provider !== ProviderType::STRIPE) {
//            $message = "You have subscribed from {$activeSubscription->provider}! So, you can't upgrade your plan from Android.";
//            Yii::error($message);
//            throw new Exception($message);
//        }

        if ($activeSubscription && $plan->id !== $activeSubscription->planId
            && $stripeSubscriptionStatus === SubscriptionStatus::ACTIVE) {
            // handle subscription upgrade case!
            try {
                if ($activeSubscription->provider === ProviderType::STRIPE) {
                    $currentAccount = $this->account;
                    self::setApiKey($activeSubscription->providerAccount);
                    $this->cancelSubscription($activeSubscription->providerSubscriptionId);
                    $activeSubscription->isActive = 0; // mark it as inactive
                    $activeSubscription->linkedPurchaseToken = "Plan upgraded to {$plan->id}";
                    $activeSubscription->save(false);
                    self::setApiKey($currentAccount); // re-set to new API Key
                } elseif ($activeSubscription->provider === ProviderType::RAZORPAY) {
                    $stripeHelper = RazorpayHelper::sharedInstance($activeSubscription->providerAccount);
                    $stripeHelper->cancelSubscription($activeSubscription->providerSubscriptionId, $activeSubscription->linkedPurchaseToken);
                    $activeSubscription->isActive = 0; // mark it as inactive
                    $activeSubscription->save(false);
                } else {
                    $logData['activeSubscription'] = $activeSubscription?->toArray();
                    $logData['activeSubscription_Provider'] = $activeSubscription?->provider;
                    $logData['plan_type'] = $plan->type;
                    $logData['plan_id'] = $plan->id;
                    $logData['activeSubscription_planId'] = $activeSubscription?->planId;
                    Yii::error("Subscription Cancellation failed \n\n" . print_r($logData, true));
                }

            } catch (RazorpayError|ApiErrorException $e) {
                $logData['activeSubscription'] = $activeSubscription?->toArray();
                $logData['plan_type'] = $plan->type;
                $logData['plan_id'] = $plan->id;
                $logData['activeSubscription_planId'] = $activeSubscription?->planId;
                $logData['stripeSubscription_status'] = $stripeSubscriptionStatus;
                Yii::error("Subscription Cancellation failed \n\n" . print_r($logData, true));
                $this->error = $e->getMessage();
            }
        }

        if ($subscription === null) {
            $subscription = new Subscription();
            $subscription->providerSubscriptionId = $stripeSubscriptionId;
            $subscription->startedFromDate = $stripeSubscriptionCreated; //
            $subscription->userId = $user->id;
            $subscription->businessId = $business->id;
        }
        // update subscription data!
        $subscription->planId = $plan->id;
        $subscription->provider = ProviderType::STRIPE;
        $subscription->providerAccount = $this->account;
        $subscription->providerCustomerId = $stripeCustomerId;
        $subscription->providerPlanId = $stripePlanId;

        $currentTimestamp = Carbon::now()->timestamp;
        $endDateTimestamp = $currentTimestamp + $plan->interval * PlanDuration::getInSeconds($plan->period);
        if ($plan->isRecurring) {
            // For recurring subscriptions, only update dates if:
            // 1. This is a new subscription (no existing dates)
            // 2. The subscription is active AND the invoice is paid AND the status is not past_due
            if (!$subscription->startDate || !$subscription->endDate) {
                // New subscription - set initial dates
                $subscription->startDate = $stripeSubscriptionCurrentPeriodStart; // seconds
                $subscription->endDate = $stripeSubscriptionCurrentPeriodEnd; // seconds
            } else if (($stripeSubscriptionStatus == SubscriptionStatus::ACTIVE) &&
                $stripeInvoice &&
                $stripeInvoice->status == "paid" &&
                $subscription->status !== SubscriptionStatus::PAST_DUE) {
                // Update dates only for paid invoices on active subscriptions that aren't past_due
                $subscription->startDate = $stripeSubscriptionCurrentPeriodStart; // seconds
                $subscription->endDate = $stripeSubscriptionCurrentPeriodEnd; // seconds
            }
        } else {
            // For one-time purchases
            $subscription->startDate = $currentTimestamp; // ms to seconds
            $subscription->endDate = $endDateTimestamp; // 1 x weekly duration in seconds
        }
        $subscription->status = $stripeSubscriptionStatus;
        if (($plan->isRecurring && $subscription->status === SubscriptionStatus::ACTIVE) ||
            (!$plan->isRecurring && $subscription->status === SubscriptionStatus::SUCCEEDED)) {

            $subscription->isActive = 1;
            $subscription->paymentIntentClientSecret = null;
        } else {
            $subscription->isActive = 0;
        }
        if ($stripeSubscription->cancel_at !== null & $stripeSubscriptionCancelAt > 0) {
            $subscription->endDate = $stripeSubscriptionCancelAt;
        }
        if (!$subscription->validate()) {
            $errMessage = $subscription->getErrorSummary(true)[0];
            Yii::error($errMessage);
            throw new Exception($errMessage);
        }
        // Capture additional transaction details
        $subscription->captureUserIp();

        // Get actual amount paid from Stripe instead of using plan price
        try {
            $actualAmount = null;
            $actualCurrency = null;

            if ($stripeInvoice) {
                // Get the actual amount paid from the invoice
                $actualAmount = $this->convertUnitPriceToNormal($stripeInvoice->amount_paid, $stripeInvoice->currency);
                $actualCurrency = strtoupper($stripeInvoice->currency);
            } elseif ($stripeSubscription instanceof \Stripe\PaymentIntent) {
                // For one-time payments
                if ($stripeSubscription->amount_received > 0) {
                    $actualAmount = $this->convertUnitPriceToNormal($stripeSubscription->amount_received, $stripeSubscription->currency);
                    $actualCurrency = strtoupper($stripeSubscription->currency);
                }
            }

            // Set the price and currency with the actual amount paid
            if ($actualAmount !== null && $actualCurrency !== null) {
                $subscription->setPrice($actualAmount, $actualCurrency);
            } else {
                // Fallback to plan price if we couldn't get the actual amount
                $subscription->setPrice();
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the subscription process
            Yii::error("Error capturing actual payment amount: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }

        // Try to get payment receipt
        try {
            if ($stripeInvoice) {
                // Store invoice URL as receipt
                $subscription->setPaymentReceipt($stripeInvoice->hosted_invoice_url);

                // Store additional transaction details
                $subscription->addTransactionDetail('invoice_id', $stripeInvoice->id);
                $subscription->addTransactionDetail('invoice_number', $stripeInvoice->number);
                $subscription->addTransactionDetail('billing_reason', $stripeInvoice->billing_reason);
                $subscription->addTransactionDetail('invoice_pdf', $stripeInvoice->invoice_pdf);

                // Add charge details if available
                if ($stripeInvoice->charge) {
                    $charge = \Stripe\Charge::retrieve($stripeInvoice->charge);
                    $subscription->addTransactionDetail('charge_id', $charge->id);
                    $subscription->addTransactionDetail('payment_method_details', $charge->payment_method_details);
                    $subscription->addTransactionDetail('receipt_url', $charge->receipt_url);
                    $subscription->addTransactionDetail('receipt_number', $charge->receipt_number);
                }
            } elseif ($stripeSubscription instanceof \Stripe\PaymentIntent) {
                // For one-time payments
                if ($stripeSubscription->charges && count($stripeSubscription->charges->data) > 0) {
                    $charge = $stripeSubscription->charges->data[0];
                    $subscription->setPaymentReceipt($charge->receipt_url);
                    $subscription->addTransactionDetail('charge_id', $charge->id);
                    $subscription->addTransactionDetail('payment_method_details', $charge->payment_method_details);
                    $subscription->addTransactionDetail('receipt_url', $charge->receipt_url);
                    $subscription->addTransactionDetail('receipt_number', $charge->receipt_number);
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the subscription process
            Yii::error("Error capturing payment receipt: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }

        $subscription->save();
        if (isset($stripePaymentIntentClientSecret)) {
            $subscription->paymentIntentClientSecret = $stripePaymentIntentClientSecret;
            $subscription->save(false);
            $subscription->sendPaymentIntentEmail();
        }
        $subscription->refresh();
        return $subscription;
    }


    /**
     * @param $payload
     * @return bool
     * @throws \yii\db\Exception
     * @throws NotFoundHttpException
     * @throws Exception
     */
    public
    function handleWebhook($payload)
    {
        try {
            $event = Event::constructFrom(
                json_decode($payload, true)
            );
        } catch (UnexpectedValueException $e) {
            // Invalid payload
            $errMessage = '⚠️  Webhook error while parsing basic request.';
            Yii::error($errMessage);
            echo $errMessage;
            http_response_code(400);
            exit();
        }
        switch ($event->type) {
            case 'payment_intent.succeeded':
                // Handle successful payment intent for one-time purchase
                $paymentIntent = $event->data->object;
                $metadata = $paymentIntent->metadata;
                if (isset($metadata['isRecurring']) && $metadata['isRecurring'] == 0) {
                    // process payment intent
                    $this->processSubscription($paymentIntent);
                }
                break;
            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object;

                // Retrieve the necessary information from the payment intent
                $paymentIntentId = $paymentIntent->id;
                $failureCode = $paymentIntent->last_payment_error ? $paymentIntent->last_payment_error->code : '';
                $failureMessage = $paymentIntent->last_payment_error ? $paymentIntent->last_payment_error->message : '';
                StripePaymentIntent::sendPaymentFailedNotificationEmail($paymentIntentId, $failureCode, $failureMessage);
                break;
            case 'invoice.paid':
            case 'invoice.payment_succeeded':
                // Handle successful invoice payment
                $invoice = $event->data->object;
                $this->processInvoicePayment($invoice);
                break;
            case 'customer.subscription.trial_will_end':
                // Then define and call a method to handle the trial ending.
                // handleTrialWillEnd($subscription);
            case 'customer.subscription.created':
                // Then define and call a method to handle the subscription being created.
                // handleSubscriptionCreated($subscription);
            case 'customer.subscription.deleted':
                // Then define and call a method to handle the subscription being deleted.
                // handleSubscriptionDeleted($subscription);
            case 'customer.subscription.updated':
                $subscription = $event->data->object; // contains a \Stripe\Subscription
                // Then define and call a method to handle the subscription being updated.
                // handleSubscriptionUpdated($subscription);
                if (in_array($subscription->status, ["incomplete_expired", "incomplete"])) {
                    return true;
                }

                $this->logWebhookEvent($subscription, $event->type);
                $this->processSubscription($subscription);
                break;
            default:
                // Unexpected event type
                $errMessage = '⚠️  Received unknown event type: ' . $event->type;
                Yii::error($errMessage);
                echo $errMessage;
                http_response_code(400);
                exit();
        }
        return true;
    }

    /**
     * Process invoice payment event
     *
     * @param Invoice $invoice The Stripe invoice object
     * @return bool Whether the processing was successful
     * @throws Exception
     */
    public function processInvoicePayment(Invoice $invoice)
    {
        try {
            // Only process paid invoices
            if ($invoice->status !== 'paid') {
                return false;
            }

            // Check if this is a subscription invoice
            if (empty($invoice->subscription)) {
                return false;
            }

            // Retrieve the subscription
            $stripeSubscription = StripeSubscription::retrieve($invoice->subscription);
            if (!$stripeSubscription) {
//                Yii::error("Could not find subscription for subscription : " . $invoice->subscription);
                return false;
            }

            // Log the event
            $this->logWebhookEvent($stripeSubscription, 'invoice.paid');

            // Find the subscription in our database
            $subscription = Subscription::find()->byProviderSubscriptionId($invoice->subscription)->one();
            if (!$subscription) {
//                Yii::error("Could not find subscription in database for invoice: " . $invoice->id);
                return false;
            }

            // Don't update dates if the subscription is past_due
            if ($stripeSubscription->status === SubscriptionStatus::PAST_DUE) {
//                Yii::info("Skipping date update for past_due subscription: " . $subscription->id);
                return true;
            }

            // Update subscription dates based on the invoice period
            $subscription->startDate = $stripeSubscription->current_period_start;
            $subscription->endDate = $stripeSubscription->current_period_end;

            // Update payment details
            $subscription->lastTransactionId = $invoice->charge;
            $subscription->setPaymentReceipt($invoice->hosted_invoice_url);

            // Add transaction details
            $subscription->addTransactionDetail('invoice_id', $invoice->id);
            $subscription->addTransactionDetail('invoice_number', $invoice->number);
            $subscription->addTransactionDetail('billing_reason', $invoice->billing_reason);
            $subscription->addTransactionDetail('invoice_pdf', $invoice->invoice_pdf);

            // Set the actual amount paid
            $actualAmount = $this->convertUnitPriceToNormal($invoice->amount_paid, $invoice->currency);
            $actualCurrency = strtoupper($invoice->currency);
            $subscription->setPrice($actualAmount, $actualCurrency);
            $subscription->status = $stripeSubscription->status;
            if ($subscription->status === SubscriptionStatus::ACTIVE ||
                $subscription->status === SubscriptionStatus::SUCCEEDED) {
                $subscription->isActive = 1;
            } else {
                $subscription->isActive = 0;
            }

            // Save the subscription
            if (!$subscription->save()) {
                $errMessage = $subscription->getErrorSummary(true)[0];
                Yii::error("Error saving subscription after invoice payment: " . $errMessage);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Yii::error("Error processing invoice payment: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * @param Subscription $activeSubscription
     * @param SubscriptionPlan $plan
     * @return StripeSubscription
     * @throws ApiErrorException
     */
    public function createUpgradePaymentIntent(Subscription $activeSubscription, SubscriptionPlan $plan): StripeSubscription
    {
        $stripeSubscription = StripeSubscription::Retrieve($activeSubscription->providerSubscriptionId);
        $stripeSubscription = StripeSubscription::update(
            $activeSubscription->providerSubscriptionId,
            [
                'cancel_at_period_end' => false,
                'payment_behavior' => 'default_incomplete',
                'proration_behavior' => 'always_invoice',
                'expand' => ['latest_invoice.payment_intent'],
                'items' => [
                    [
                        'id' => $stripeSubscription->items->data[0]->id,
                        'price' => $plan->stripePlanId,
                    ],
                ],
            ]
        );
        return $stripeSubscription;
    }

    /**
     * @param User $user
     * @return mixed|string
     */
    public function getStripeCustomerId(User $user): mixed
    {
        $stripeAccount = $this->account;
        $stripeUser = StripeUser::findByUserIdAndAccount($user->id, $stripeAccount);
        $stripeCustomerId = $stripeUser?->stripeCustomerId;
        if ($stripeCustomerId === null) {
            $query = new Query();
            $results = $query->select('userId, stripeCustomerId')
                ->from('user_stripe')
                ->where(['userId' => $user->id])
                ->one();
            if ($results) {
                $stripeCustomerId = $results['stripeCustomerId'];
            }
        }
        return $stripeCustomerId;
    }


    /**
     * @param String $stripeCustomerId
     * @return User|null
     */
    public function getUserByStripeCustomerId($stripeCustomerId)
    {
        return StripeUser::find()->byStripeCustomerId($stripeCustomerId)->one()?->user;
    }

    /**
     * @param StripeSubscription $stripeSubscription
     * @param $subscriptionId
     * @return Refund|null
     * @throws ApiErrorException
     */
    public function stripeRefund(StripeSubscription $stripeSubscription, $subscriptionId): ?Refund
    {
// sample prorated invoice for a subscription with quantity of 0
        /** @var SubscriptionItem $subscriptionItem */
        $subscriptionItem = $stripeSubscription->items->data[0];
        $sample_subscription_item = array(
            "id" => $subscriptionItem->id,
            "plan" => $subscriptionItem->plan->id,
            "quantity" => 0,
        );

        $upcoming_prorated_invoice = Invoice::upcoming([
            "customer" => $stripeSubscription->customer,
            "subscription" => $stripeSubscription->id,
            "subscription_items" => array($sample_subscription_item),
//            "subscription_proration_date" => , // optional
        ]);

// find prorated amount
        $prorated_amount = 0;
        foreach ($upcoming_prorated_invoice->lines->data as $invoice) {
            if ($invoice->type == "invoiceitem") {
                $prorated_amount = ($invoice->amount < 0) ? abs($invoice->amount) : 0;
                break;
            }
        }

// find charge id on the active subscription's last invoice
        $latest_invoice = Invoice::retrieve($stripeSubscription->latest_invoice);
        $latest_charge_id = $latest_invoice->charge;

// refund amount from last invoice charge
        $refund = null;
        if ($prorated_amount > 0) {
            $refundAmount = $this->convertUnitPriceToNormal($prorated_amount, $latest_invoice->currency);
            $refundAmount = formatAsCurrency($refundAmount, $latest_invoice->currency);
            try {
                $refund = Refund::create([
                    'charge' => $latest_charge_id,
                    'amount' => $prorated_amount,
                ]);
            } catch (Exception|\Exception $e) {
                $this->sendCancellationFailedEmail($subscriptionId, $refundAmount, $e->getMessage() . "\n" . $e->getTraceAsString());
            }

            if ($refund && $refund->status == Refund::STATUS_SUCCEEDED) {
                $this->sendRefundSuccessMail($subscriptionId, $refundAmount);
            } else {
                $this->sendCancellationFailedEmail($subscriptionId, $refundAmount, $refund?->reason);
            }
        }
        return $refund;
    }

    function storeSubscriptionPaymentIntent(StripeSubscription|PaymentIntent $stripeSubscription, User $user, SubscriptionPlan $plan)
    {
        $stripeItem = $stripeSubscription->items->first(); // last transaction item
        /** @var Plan $stripePlan */
        $stripePlan = $stripeItem?->plan;
        $stripePlanId = $stripePlan->id;
        $stripeCustomerId = $stripeSubscription->customer;
        $stripeSubscriptionId = $stripeSubscription->id;
        $stripeSubscriptionStatus = $stripeSubscription->status;
        if ($stripeSubscriptionStatus == SubscriptionStatus::ACTIVE) {
            $this->setDefaultPaymentMethod($stripeSubscription);
        }
        $stripeSubscriptionCreated = $stripeSubscription->created;
        $stripeSubscriptionCurrentPeriodStart = $stripeSubscription->current_period_start;
        $stripeSubscriptionCurrentPeriodEnd = $stripeSubscription->current_period_end;
        $stripeSubscriptionCancelAt = $stripeSubscription->cancel_at;
        $stripePaymentIntentClientSecret = $stripeSubscription?->latest_invoice?->payment_intent?->client_secret ?? null;

        $subscription = new StripePaymentIntent();
        $subscription->providerSubscriptionId = $stripeSubscriptionId;
        $subscription->startedFromDate = $stripeSubscriptionCreated; //
        $subscription->userId = $user->id;
        $subscription->businessId = $user->business->id;

        // update subscription data!
        $subscription->planId = $plan->id;
        $subscription->provider = ProviderType::STRIPE;
        $subscription->providerAccount = $this->account;
        $subscription->providerCustomerId = $stripeCustomerId;
        $subscription->providerPlanId = $stripePlanId;
        $subscription->startDate = $stripeSubscriptionCurrentPeriodStart; // seconds
        if ($plan->isRecurring) {
            $subscription->endDate = $stripeSubscriptionCurrentPeriodEnd; // seconds
        } else {
            $currentTimestamp = Carbon::now()->timestamp;
            $subscription->startDate = $currentTimestamp; // ms to seconds
            $subscription->endDate = $currentTimestamp + $plan->interval * PlanDuration::getInSeconds($plan->period); // 1 x weekly duration in seconds
        }
        $subscription->status = $stripeSubscriptionStatus;

        $subscription->isActive = 0;
        if ($stripeSubscription->cancel_at !== null & $stripeSubscriptionCancelAt > 0) {
            $subscription->endDate = $stripeSubscriptionCancelAt;
        }
        if (!$subscription->validate()) {
            $errMessage = $subscription->getErrorSummary(true)[0];
            Yii::error($errMessage);
            throw new Exception($errMessage);
        }
        $subscription->paymentIntentClientSecret = $stripePaymentIntentClientSecret;
        $subscription->save();
        $subscription->sendPaymentIntentEmail();
        return $subscription;
    }

    public static function checkPastDueSubscriptions(Business $business, SubscriptionPlan $plan, string $providerSubscriptionId)
    {
        try {
            $oldSubscription = Subscription::find()->alias('s')
                ->innerJoin('subscription_plan sp', 'sp.id = s.planId')
                ->where(['s.businessId' => $business->id])
                ->andWhere(['sp.group' => $plan->group])
                ->andWhere(['status' => SubscriptionStatus::PAST_DUE])
                ->andWhere("providerSubscriptionId != '$providerSubscriptionId'")
                ->orderBy('s.id DESC')
                ->limit(1)
                ->one();
            if ($oldSubscription) {
                $currentAccount = self::getCurrentAccount(); // preserve current api key
                self::setApiKey($oldSubscription->providerAccount);
                $oldStripeSubscription = StripeSubscription::retrieve($oldSubscription->providerSubscriptionId);
                $oldStripeSubscription->delete(); // cancel the old subscription
                self::setApiKey($currentAccount); // re-set to new API Key
                $oldSubscription->sendPastdueCancelNotificationEmail();
            }
        } catch (Exception|\Exception $e) {
            Yii::error("Check PastDue Subscriptions & Cancellation flow \n\n " . $e->getMessage() . "\n\n" . $e->getTraceAsString());
        }
    }
}