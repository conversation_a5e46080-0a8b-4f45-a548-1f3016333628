<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 15:52
 */

namespace common\helpers;

use Yii;

// Define constants if not already defined
defined("BASE_DIR_PATH") || define("BASE_DIR_PATH", Yii::getAlias("@baseDir") . "/");
defined("UPLOAD_DIR_PATH") || define("UPLOAD_DIR_PATH", Yii::get<PERSON>lia<PERSON>("@uploadDir") . "/");

/**
 * Class FileManagerBase
 * Base class for file management operations
 * @package common\helpers
 */
class FileManagerBase
{
    /**
     * Get the file manager instance
     * @return FileManagerInterface
     */
    protected static function getFileManager(): FileManagerInterface
    {
        return FileManagerFactory::getInstance();
    }

    /**
     * Upload a file from a model
     * @param $model
     * @param $urlField
     * @param $fileField
     * @param $relativePath
     * @param null $id
     * @param null $prefix
     * @param bool $deleteTempFile
     * @return bool
     */
    public static function upload($model, $urlField, $fileField, $relativePath, $id = null, $prefix = null, $deleteTempFile = true): bool
    {
        return self::getFileManager()->upload($model, $urlField, $fileField, $relativePath, $id, $prefix, $deleteTempFile);
    }

    /**
     * Save an uploaded file
     * @param $file
     * @param $relativePath
     * @param null $id
     * @param null $prefix
     * @param bool $deleteTempFile
     * @return mixed|null
     */
    public static function saveFile($file, $relativePath, $id = null, $prefix = null, $deleteTempFile = true)
    {
        return self::getFileManager()->saveFile($file, $relativePath, $id, $prefix, $deleteTempFile);
    }

    /**
     * Save file content
     * @param $content
     * @param $relativePath
     * @param $fileName
     * @return mixed|null
     */
    public static function saveFileContent($content, $relativePath, $fileName)
    {
        return self::getFileManager()->saveFileContent($content, $relativePath, $fileName);
    }

    /**
     * Get the file path for a relative path
     * @param string $relativePath
     * @return string|null
     */
    public static function getFilePath($relativePath = '')
    {
        return self::getFileManager()->getFilePath($relativePath);
    }

    /**
     * Remove a file from its URL
     * @param $fileUrl
     * @return bool
     */
    public static function removeFileFromURL($fileUrl): bool
    {
        return self::getFileManager()->removeFileFromURL($fileUrl);
    }

    /**
     * Get the file path from a URL
     * @param $fileUrl
     * @return string|null
     */
    public static function getFilePathFromURL($fileUrl)
    {
        return self::getFileManager()->getFilePathFromURL($fileUrl);
    }

    /**
     * Remove a file
     * @param $filePath
     * @return bool
     */
    public static function removeFile($filePath): bool
    {
        return self::getFileManager()->removeFile($filePath);
    }

    /**
     * Check if a directory is empty
     * @param $dir
     * @return bool
     */
    public static function isDirEmpty($dir): bool
    {
        return self::getFileManager()->isDirEmpty($dir);
    }
}