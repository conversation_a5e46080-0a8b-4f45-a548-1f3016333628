<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-09-2020
 * Time: 02:31
 */

namespace common\helpers;

use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;

trait Dropdown
{
    /**
     * @param string $valueField
     * @param string $displayField
     * @param null $whereCondition
     * @return array
     */
    public static function dropdown($valueField = 'id', $displayField = 'name', $whereCondition = null): array
    {
        /** @var ActiveQuery $query */
        $query = self::find()->select([$valueField, $displayField]);
        if ($whereCondition) {
            $query->andWhere($whereCondition);
        }
        $arrayList = $query->asArray()->all();
        return ArrayHelper::map($arrayList, $valueField, $displayField);
    }


    /**
     * @param string $valueField
     * @param string $displayField
     * @param null $whereCondition
     * @return array
     */
    public static function selectDropdown($valueField = 'id', $displayField = 'name', $whereCondition = null): array
    {
        /** @var ActiveQuery $query */
        $query = self::find()->select([$valueField, 'text' => $displayField]);
        if ($whereCondition) {
            $query->andWhere($whereCondition);
        }
        return $query->asArray()->all();
    }

}