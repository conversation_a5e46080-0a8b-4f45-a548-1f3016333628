<?php

namespace common\helpers;

use Yii;
use yii\base\ErrorException;
use yii\base\Exception;
use yii\helpers\FileHelper;

/**
 * Class LocalFileSystem
 * Implements file operations for the local file system
 * @package common\helpers
 */
class LocalFileSystem implements FileManagerInterface
{
    /**
     * @inheritdoc
     */
    public function upload($model, $urlField, $fileField, $relativePath, $id = null, $prefix = null, $deleteTempFile = true): bool
    {
        Yii::info('upload start');

        if ($model->$fileField && $model->validate($fileField)) {
            Yii::info('validation passed');
            Yii::info($relativePath);
            $fileUrl = $this->saveFile($model->$fileField, $relativePath, $id, $prefix, $deleteTempFile);
            if ($fileUrl) {
                $isRemoved = $this->removeFileFromURL($model->$urlField);
                if (!$isRemoved) {
                    Yii::error('File delete issue! \n File : ' . $model->$urlField);
                }
                $model->$fileField = null;
                $model->$urlField = $fileUrl;
                if (!empty($model->id)) {
                    $model->updateAttributes([$urlField]);
                }
                return true;
            }
            Yii::error('image save failed!');
            return false;
        }
        if ($model->hasErrors($fileField)) {
            Yii::$app->session->setFlash("error", $model->getErrors($fileField));
            Yii::error($model->getErrors($fileField));
        }
        return false;
    }

    /**
     * @inheritdoc
     */
    public function saveFile($file, $relativePath, $id = null, $prefix = null, $deleteTempFile = true)
    {
        if ($file === null) {
            return null;
        }
        $fileName = '';
        if ($prefix !== null) {
            $fileName = $prefix . '_';
        }
        if ($id !== null) {
            $fileName .= $id . '_';
        } else {
            try {
                $uniqueRandomString = strtolower(Yii::$app->security->generateRandomString(5));
            } catch (Exception|\Exception $e) {
                Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
                $uniqueRandomString = '';
            }
            $fileName .= $uniqueRandomString . '_';
        }
        $fileName = uniqid($fileName, false);
        $fileName .= '.' . $file->extension;
        $filePath = $this->getFilePath($relativePath) . $fileName;
        if ($filePath && $file->saveAs($filePath, $deleteTempFile)) {
            return Yii::$app->urlManagerUpload->createAbsoluteUrl($relativePath . $fileName);
        }
        return null;
    }

    /**
     * @inheritdoc
     */
    public function saveFileContent($content, $relativePath, $fileName)
    {
        if (empty($content) || empty($fileName)) {
            return null;
        }
        
        $filePath = $this->getFilePath($relativePath) . $fileName;
        if ($filePath && file_put_contents($filePath, $content)) {
            return Yii::$app->urlManagerUpload->createAbsoluteUrl($relativePath . $fileName);
        }
        return null;
    }

    /**
     * @inheritdoc
     */
    public function getFilePath($relativePath = '')
    {
        $path = UPLOAD_DIR_PATH . $relativePath;
        if (!file_exists($path)) {
            try {
                FileHelper::createDirectory($path);
            } catch (Exception|\Exception $e) {
                Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
                return null;
            }
        }
        return $path;
    }

    /**
     * @inheritdoc
     */
    public function removeFileFromURL($fileUrl): bool
    {
        if (empty($fileUrl)) {
            return true; // file not available
        }
        $filePath = $this->getFilePathFromURL($fileUrl);
        if ($filePath) {
            return $this->removeFile($filePath);
        }
        return true; // file doesn't exist
    }

    /**
     * @inheritdoc
     */
    public function getFilePathFromURL($fileUrl)
    {
        $pathData = parse_url($fileUrl);
        if (empty($pathData) || empty($pathData['path'])) {
            return null;
        }
        
        $path = BASE_DIR_PATH . $pathData['path'];
        if (file_exists($path)) {
            return $path;
        }
        return null;
    }

    /**
     * @inheritdoc
     */
    public function removeFile($filePath): bool
    {
        $fileName = basename($filePath);
        if (!empty($fileName) && file_exists($filePath)) {
            @unlink($filePath); // remove file
            $fileDir = dirname($filePath);
            if ($this->isDirEmpty($fileDir)) {
                try {
                    FileHelper::removeDirectory($fileDir);
                } catch (ErrorException $e) {
                    Yii::error("error in removing directory - " . $fileDir);
                    Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
                }
            }
            return true;
        }
        return false;
    }

    /**
     * @inheritdoc
     */
    public function isDirEmpty($dir): bool
    {
        // PHP 5 >= 5.3.0
        $iterator = new \FilesystemIterator($dir);
        return !$iterator->valid();
    }
}
