<?php

use Carbon\Carbon;

function logMessage($message, $data = null)
{
    echo "\n";
    echo $message;
    if (isset($data)) {
        echo "\n";
        print_r($data);
    }
    echo "\n";
}


function logError($message, $data = null)
{
    $errorLog = [];
    $message = "\n" . $message . "\n";

    if (!empty($data)) {
        $errorLog['data'] = $data;
    }
    try {
        $errorLogJson = $message . json_encode($errorLog, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT) . "\n";
    } catch (JsonException $e) {
        Yii::error(message: $message . print_r($data, true));
    }
    Yii::error(message: $errorLogJson);
}

function getCurrentUrl()
{
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $host = $_SERVER['HTTP_HOST'];
    $requestUri = $_SERVER['REQUEST_URI'];

    return $protocol . $host . $requestUri;
}

function secondsToTime($inputSeconds)
{
    if ($inputSeconds <= 0.99) {
        $seconds = round($inputSeconds, 4);
        return "$seconds seconds";
    }
    $inputSeconds = ceil($inputSeconds);

    $secondsInAMinute = 60;
    $secondsInAnHour = 60 * $secondsInAMinute;
    $secondsInADay = 24 * $secondsInAnHour;

    // Extract days
    $days = floor($inputSeconds / $secondsInADay);

    // Extract hours
    $hourSeconds = $inputSeconds % $secondsInADay;
    $hours = floor($hourSeconds / $secondsInAnHour);

    // Extract minutes
    $minuteSeconds = $hourSeconds % $secondsInAnHour;
    $minutes = floor($minuteSeconds / $secondsInAMinute);

    // Extract the remaining seconds
    $remainingSeconds = $minuteSeconds % $secondsInAMinute;
    $seconds = ceil($remainingSeconds);

    // Format and return
    $timeParts = [];
    $sections = [
        'day' => (int)$days,
        'hour' => (int)$hours,
        'minute' => (int)$minutes,
        'second' => (int)$seconds,
    ];

    foreach ($sections as $name => $value) {
        if ($value > 0) {
            $timeParts[] = $value . ' ' . $name . ($value == 1 ? '' : 's');
        }
    }

    return implode(', ', $timeParts);
}

function clean($string)
{
    $string = preg_replace('/[^A-Za-z0-9\- ]/', '', $string); // Removes special chars.
    return preg_replace('/-+/', '-', $string); // Replaces multiple hyphens with single one.
}

function formatDate($strDate = '', $withTime = false)
{
    $return = $strDate;
    if (!empty($strDate)) {
        if ($withTime == true) {
            return date('Y-m-d H:i:s', strtotime(str_replace('/', '-', $strDate)));
        } else {
            return date('Y-m-d', strtotime(str_replace('/', '-', $strDate)));
        }
    }
    return $return;
}

function execSql($sql, $data = [])
{
    $connection = Yii::$app->getDb();
    $command = $connection->createCommand($sql, $data);
    return $command->queryAll();
}

/**
 * Calculates the distance between two points, given their
 * latitude and longitude, and returns an array of values
 * of the most common distance units
 *
 * @param $lat1
 * @param $lon1
 * @param $lat2
 * @param $lon2
 * @param string $unit
 * @return mixed {array}       Array of values in many distance units
 */
function getDistanceBetweenPoints($lat1, $lon1, $lat2, $lon2, $unit = "kilometers")
{
    $theta = $lon1 - $lon2;
    $miles = (sin(deg2rad($lat1)) * sin(deg2rad($lat2))) + (cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta)));
    $miles = acos($miles);
    $miles = rad2deg($miles);
    $miles = $miles * 60 * 1.1515;
    $feet = $miles * 5280;
    $yards = $feet / 3;
    $kilometers = $miles * 1.609344;
    $meters = $kilometers * 1000;
    $distance = compact('miles', 'feet', 'yards', 'kilometers', 'meters');
    return $distance[$unit];
}

function get_finacial_year_range()
{
    $year = date('Y');
    $month = date('m');
    if ($month < 4) {
        $year = $year - 1;
    }
    $start_date = date('d-m-Y', strtotime(($year) . '-04-01'));
    $end_date = date('d-m-Y', strtotime(($year + 1) . '-03-31'));
    return array('start_date' => $start_date, 'end_date' => $end_date);
}

function getFinancialYearPrefix()
{
    $response = get_finacial_year_range();

    $startYear = date('Y', strtotime($response['start_date']));
    $endYear = date('y', strtotime($response['end_date']));
    return $startYear . "-" . $endYear;
}

function slugify($text, string $divider = '-')
{
    // replace non letter or digits by divider
    $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

    // transliterate
    $text = iconv("ISO-8859-1", 'UTF-8//TRANSLIT', $text);

    // remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);

    // trim
    $text = safeTrim($text, $divider) ?? '';

    // remove duplicate divider
    $text = preg_replace('~-+~', $divider, $text);

    // lowercase
    $text = strtolower($text);

    if (empty($text)) {
        return 'n-a';
    }

    return $text;
}

// in a helper file
function getParam($name, $default = null)
{
    if (isset(Yii::$app->params[$name])) {
        return Yii::$app->params[$name];
    }
    return $default;
}

// in a helper file
function setParam($name, $value = null)
{
    if (isset(Yii::$app->params[$name])) {
        Yii::$app->params[$name] = $value;
    }

    return Yii::$app->params[$name];
}


// in a helper file
function env($name, $default = null)
{
    return getParam($name, $default);
}

function formatAsCurrencyByLocale($number, $locale, $precision = 2)
{
    if (is_numeric($number)) {
        $formatter = Yii::$app->formatter;
        $currentLocale = $formatter->locale;
        $formatter->locale = $locale;
        if ($formatter->locale === "en-PK") {
            $precision = 2;
            $formatter->numberFormatterOptions = [
                \NumberFormatter::MIN_FRACTION_DIGITS => 2,
                \NumberFormatter::MAX_FRACTION_DIGITS => $precision,
            ];
        }
        $number = round($number, $precision);
        $fmtCurrency = $formatter->asCurrency($number);
        $formatter->locale = $currentLocale;
        return $fmtCurrency;
    }
    return $number;

}

function formatAsCurrency($number, $currency = null, $precision = 4)
{
    if (is_numeric($number)) {
        $formatter = Yii::$app->formatter;
        if ($formatter->locale === "en-PK") {
            $precision = 2;
            $formatter->numberFormatterOptions = [
                \NumberFormatter::MIN_FRACTION_DIGITS => 2,
                \NumberFormatter::MAX_FRACTION_DIGITS => $precision,
            ];
        }
        $number = round($number, $precision);
        if ($currency) {
            return $formatter->asCurrency($number, $currency);
        }
        return $formatter->asCurrency($number);
    }
    return $number;
}

/**
 * Formats a number according to the Indian numbering system.
 * In the Indian numbering system, numbers are grouped by thousands, hundreds of thousands, and tens of millions.
 * For example, the number 1234567 should be formatted as 12,34,567.
 *
 * @param float $number The number to be formatted.
 * @param int $decimal The number of decimal places, default is 2.
 * @return string The formatted number string.
 */
function formatIndianNumber($number, $decimal = 2) {
    // Define the number of decimal places, decimal separator, and thousand separator
    $decimalSeparator = '.';
    $thousandSeparator = ',';

    // Format the number with the defined decimal places, decimal separator, and thousand separator
    $number = number_format($number, $decimal, $decimalSeparator, $thousandSeparator);

    // Split the formatted number into integer and decimal parts
    $parts = explode($decimalSeparator, $number);
    $integerPart = $parts[0];

    // Reverse the integer part to process it from right to left
    $integerPart = strrev($integerPart);

    // Add commas after every 2 digits except the first group
    $integerPart = preg_replace('/(\d{2})(?=\d)/', '$1,', $integerPart);

    // Reverse back to original order
    $integerPart = strrev($integerPart);

    // Reconstruct the number with the decimal part if it exists
    $formattedNumber = $integerPart;
    if (isset($parts[1])) {
        $formattedNumber .= $decimalSeparator . $parts[1];
    }

    // Return the formatted number
    return $formattedNumber;
}


# colour-formatted print_r shortcut
if (!function_exists('prd')) {
    function prd($object, $die = TRUE)
    {

        $output = "";
        # insert span tags
        $output = '<span class="die_value">' . $output;
        $output = str_replace('[', '<span class="die_key">[', print_r($object, TRUE));
        $output = str_replace(']', ']</span>', $output);
        $output = str_replace('=> ', '=> <span class="die_value">', $output);

        # temporarily swap these paterns
        $output = str_replace(")\n\n", ")#br##br#", $output);
        $output = str_replace(")\n", ")#br#", $output);
        $output = str_replace("(\n", "(#br#", $output);

        # close spans at remaining line breaks
        $output = str_replace("\n", "</span>\n", $output);

        # revert temporary swaps
        $output = str_replace(")#br##br#", ")\n\n", $output);
        $output = str_replace(")#br#", ")\n", $output);
        $output = str_replace("(#br#", "(\n", $output);

        echo '<style type="text/css">#die_object { font-size: 11px; padding: 10px; background: #eee; font-family: monospace; white-space: pre;} .die_key { color: #e00;} .die_value { color: #00e;}</style>';

        if ($die)
            die('<div id="die_object">' . $output . '</div>');
        else
            echo('<div id="die_object">' . $output . '</div>');
    }
}

function joinWithComma($data, $separator = ", ")
{
    $result = str_replace(",  ,", ",", implode($separator, $data));
    return safeTrim($result, $separator) ?? '';
}

if (!function_exists('decodePayload')) {
    function decodePayload($signedPayload)
    {
        $tokenParts = explode(".", $signedPayload);
        return json_decode(base64_decode($tokenParts[1]));
    }
}


function startsWith($string, $prefix)
{
    return substr($string, 0, strlen($prefix)) === $prefix;
}

function logDataIntoFile($data, $title = null, $filePrefix = "api_log")
{
    if (is_array($data)) {
        $data = print_r($data, true);
    }
    if (is_object($data)) {
        $data = var_export($data, true);
    }
    $indianTimezone = 'Asia/Kolkata';
    $todayDate = Carbon::today($indianTimezone)->format('Y-m-d');
    $logfile = Yii::getAlias("@api/runtime/logs/{$filePrefix }_{$todayDate}.log");
    file_put_contents($logfile, "\n", FILE_APPEND);
    if ($title) {
        file_put_contents($logfile, "\n $title \n", FILE_APPEND);
    }
    file_put_contents($logfile, $data, FILE_APPEND);
    //  return $this->_sendResponse([], "ok");
}


function getValue($type, $value)
{
    if (empty($value)) {
        return $value;
    }
    $value = match ($type) {
        'int' => intval($value),
        'float' => floatval($value),
        'bool' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
        'json' => json_decode($value, true),
        default => $value,
    };
    return $value;
}

function toBool($value)
{
    return filter_var($value, FILTER_VALIDATE_BOOLEAN);
}

function trimInside($subject)
{
    return str_replace([" ", "\t", "\n", "\r", "\0", "\x0B"], "", $subject);
}

/**
 * Safely trims a string, handling null values
 *
 * @param string|null $string The string to trim or null
 * @param string $charlist Optional list of characters to trim
 * @return string|null The trimmed string or null if input was null
 */
function safeTrim($string, $charlist = " \t\n\r\0\x0B")
{
    // PHP 8.1+ compatible approach
    if ($string === null) {
        return null;
    }
    return trim($string, $charlist);
}

function amountInWords($amount)
{
    $formatter = Yii::$app->formatter;
    $originalLocale = $formatter->locale;

    try {
        // Try to get currency code from formatter, or derive it from locale
        $currencyCode = $formatter->currencyCode;

        // If currency code is not available, try to derive it from locale
        if (empty($currencyCode)) {
            $currencyCode = getCurrencyCodeFromLocale($formatter->locale);
            $formatter->currencyCode = $currencyCode;
        }

        // Special case for Indian Rupees
        if ($currencyCode == "INR") {
            return amountToWordsINR($amount);
        }

        // Round to two decimal places
        $amount = round($amount, 2);
        $integralPart = floor($amount);
        $fractionalPart = round(($amount - $integralPart) * 100);

        // Get currency name based on currency code
        $currencyName = getCurrencyName($currencyCode);
        // Pluralize currency name if amount is not 1
        if ($integralPart != 1) {
            // Handle special pluralization cases
            if ($currencyName == 'Dollar') {
                $currencyName = 'Dollars';
            } elseif ($currencyName == 'Pound Sterling') {
                $currencyName = 'Pounds Sterling';
            } elseif ($currencyName == 'Euro') {
                $currencyName = 'Euros';
            } elseif ($currencyName == 'Rupee') {
                $currencyName = 'Rupees';
            } else {
                // General case: add 's' for pluralization
                $currencyName .= 's';
            }
        }

        // Spell out the integral part
        $integralWords = $formatter->asSpellout($integralPart);
        $result = ucwords($integralWords) . ' ' . $currencyName;

        // Handle fractional part if it exists
        if ($fractionalPart > 0) {
            $fractionalWords = ucwords($formatter->asSpellout($fractionalPart));

            // Get appropriate fractional unit based on currency
            $fractionalUnit = getFractionalUnitName($currencyCode);

            $result .= ' and ' . $fractionalWords . ' ' . $fractionalUnit;
            $result .= ' Only';
        } else {
            $result .= ' Only';
        }

        return $result;
    } catch (\Exception $e) {
        Yii::error('Error in amountInWords: ' . $e->getMessage());
        // Fallback to a simple conversion
        return ucwords($formatter->asSpellout($amount)) . ' ' . ($currencyCode ?? 'Dollar') . ' Only';
    } finally {
        // Always restore the original locale
        $formatter->locale = $originalLocale;
    }
}

function amountToWordsINR($amount)
{
    // Split the amount into integral and fractional parts
    $amount = round($amount, 2);
    $integralPart = floor($amount); // Before the decimal point
    $fractionalPart = round(round($amount - $integralPart, 2) * 100); // After the decimal point

    // Convert the integral part to words
    $integralPartInWords = spellOutNumber($integralPart);
    $totalAmountInWords = "$integralPartInWords Rupees";

    if ($fractionalPart > 0) {
        // Convert the fractional part to words
        $fractionalPartInWords = spellOutNumber($fractionalPart);
        $totalAmountInWords .= " and $fractionalPartInWords paisa";
    }
    $totalAmountInWords .= " only/-";
    return ucwords(trim($totalAmountInWords));
}

function spellOutNumber($number)
{
    $pattern = "    -x: minus >>;\n"
        . "    x.x: << point >>;\n"
        . "    zero; one; two; three; four; five; six; seven; eight; nine;\n"
        . "    ten; eleven; twelve; thirteen; fourteen; fifteen; sixteen;\n"
        . "        seventeen; eighteen; nineteen;\n"
        . "    20: twenty[->>];\n"
        . "    30: thirty[->>];\n"
        . "    40: forty[->>];\n"
        . "    50: fifty[->>];\n"
        . "    60: sixty[->>];\n"
        . "    70: seventy[->>];\n"
        . "    80: eighty[->>];\n"
        . "    90: ninety[->>];\n"
        . "    100: << hundred[ >>];\n"
        . "    1000: << thousand[ >>];\n"
        . "    100,000: << lakh[ >>];\n"
        . "    10,000,000: << crore[ >>];\n";
    $formatter = new \NumberFormatter('en_IN', \NumberFormatter::PATTERN_RULEBASED, $pattern);
    return $formatter->format($number);
}

function convertDateToTimestamp($dateString)
{
    try {
        // Create a DateTime object from the date string
        $dateTime = new \DateTime($dateString);
        // Convert the DateTime object to a Unix timestamp
        $timestamp = $dateTime->getTimestamp();
    } catch (Exception $e) {
        return null;
    }
    return $timestamp; // This will output the Unix timestamp
}

function getLastErrors($n = 100, $logFile = null)
{
    // If no log file specified, try to get the PHP error log path
    if ($logFile === null) {
        $logFile = ini_get('error_log');
        if (empty($logFile)) {
            return "Error log file not found or not configured";
        }
    }

    // Check if file exists and is readable
    if (!file_exists($logFile) || !is_readable($logFile)) {
        return "Error log file does not exist or is not readable: $logFile";
    }

    try {
        // Open file and seek to end
        $file = new SplFileObject($logFile, 'r');
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();

        // Calculate start line
        $startLine = max(0, $totalLines - $n);

        // Store the lines
        $lines = [];

        // Read last N lines
        for ($i = $startLine; $i < $totalLines; $i++) {
            $file->seek($i);
            $lines[] = $file->current();
        }

        return implode('', $lines);
    } catch (Exception $e) {
        return "Error reading log file: " . $e->getMessage();
    }
}


// A function to print limited stack trace
function printStackTrace($limit = 10)
{
    $trace = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS, $limit);
    $rootPath = Yii::getAlias('@webroot');
    $output = "Stack trace:\n";

    // Track the complete call chain
    $callChain = [];
    foreach ($trace as $index => $node) {
        $callLevel = str_repeat('  ', $index); // Indent based on call depth

        if (!empty($node['file'])) {
            $file = str_replace($rootPath, '', $node['file']);
            $line = isset($node['line']) ? $node['line'] : '?';
            $function = isset($node['function']) ? $node['function'] : '?';
            $class = isset($node['class']) ? $node['class'] . $node['type'] : '';

            // Build the call chain
            $currentCall = "{$class}{$function}";
            array_unshift($callChain, $currentCall);
            $callChainStr = implode(' -> ', $callChain);

            $output .= "{$callLevel}Call Level " . ($index + 1) . ":\n";
            $output .= "{$callLevel}Complete call chain: {$callChainStr}\n";
            $output .= "{$callLevel}at {$class}{$function}() in {$file}:{$line}\n";

            // Add additional debug info
            if (isset($node['args'])) {
                $output .= "{$callLevel}  Arguments: " . print_r($node['args'], true) . "\n";
            }
            if (isset($node['object'])) {
                $output .= "{$callLevel}  Object: " . get_class($node['object']) . "\n";
            }
            if (file_exists($node['file'])) {
                $lines = file($node['file']);
                $lineNumber = $node['line'] - 1;
                if (isset($lines[$lineNumber])) {
                    $output .= "{$callLevel}  Code: " . trim($lines[$lineNumber]) . "\n";
                }
            }
            $output .= "\n"; // Add spacing between stack levels
        }
    }
    return $output;
}

function debugTrace()
{
    $errorLog = "";
    $errorLog .= "SERVER VARS :: \n" . print_r($_SERVER, true);
    $errorLog .= "REQUEST VARS :: \n" . print_r($_REQUEST, true);
    $trace = debug_backtrace();
    $rootPath = $_SERVER['DOCUMENT_ROOT'];
    $errorLog .= "Calling hierarchy of the function is: \n";
    foreach ($trace as $node) {
        if (!empty($node['file'])) {
            $file = str_replace($rootPath, '', $node['file']);
            $errorLog .= "$file : $node[line] : function $node[function] \n";
        }
    }
    return $errorLog;
}

function logMail($message, $data, $body = "", $logEmail = null)
{
    date_default_timezone_set('Asia/Kolkata');
    $todayDate = date("d.m.Y");
    if ($logEmail == null) {
        $logEmail = env(logToEmail);
    }
    $composeMail = Yii::$app
        ->mailer
        ->compose(
            ['html' => 'logMail-html'],
            ['data' => $data, 'body' => $body, 'message' => $message, 'todayDate' => $todayDate],
        )
        ->setFrom([env(logFromEmail) => env(logFromName)])
        ->setTo($logEmail)
        ->setSubject("$todayDate - $message");
    $isSent = $composeMail->send();
    if ($isSent !== true) {
        Yii::error("Log email error!" . "\nmessage : \n" . $message);
    }
}

function printPre($object)
{
    $output = "";
    # insert span tags
    $output = '<span class="die_value" style="color: #00e;">' . $output;
    $output = str_replace('[', '<span class="die_key" style="color: #e00;">[', print_r($object, TRUE));
    $output = str_replace(']', ']</span>', $output);
    $output = str_replace('=> ', '=> <span class="die_value" style="color: #00e;">', $output);

    # temporarily swap these paterns
    $output = str_replace(")\n\n", ")#br##br#", $output);
    $output = str_replace(")\n", ")#br#", $output);
    $output = str_replace("(\n", "(#br#", $output);

    # close spans at remaining line breaks
    $output = str_replace("\n", "</span>\n", $output);

    # revert temporary swaps
    $output = str_replace(")#br##br#", ")\n\n", $output);
    $output = str_replace(")#br#", ")\n", $output);
    $output = str_replace("(#br#", "(\n", $output);

    $strOutput = '<div id="die_object" style="font-size: 11px; padding: 10px; background: #eee; font-family: monospace; white-space: pre;">' . $output . '</div>';
    return $strOutput;
}


function isAbsoluteUrl($url)
{
    $parsedUrl = parse_url($url);
    return isset($parsedUrl['scheme']) && isset($parsedUrl['host']);
}

function getLocalizedDateTime($timestamp = null, $format = 'd.m.Y H:i:s', $timezone = 'Asia/Kolkata'): string
{
    if ($timestamp) {
        $timestamp = "@$timestamp";
    } else {
        $timestamp = 'now';
    }
    return (new DateTime('now', new DateTimeZone($timezone)))
        ->format($format);
}

function getIndianDateTime($timestamp = null)
{
    return getLocalizedDateTime($timestamp);
}

/**
 * Formats a date in the standard DD MMM, YYYY format (e.g., 16 Apr, 2025)
 * This format is clear and unambiguous across different regions
 *
 * @param string|int|\DateTime $date The date to format
 * @param string $timezone The timezone to use (default: 'Asia/Kolkata')
 * @return string The formatted date
 */
function formatStandardDate($date, $timezone = 'Asia/Kolkata'): string
{
    if (empty($date)) {
        return '';
    }

    if (is_string($date) && !is_numeric($date)) {
        $dateObj = new DateTime($date, new DateTimeZone($timezone));
    } elseif (is_numeric($date)) {
        $dateObj = new DateTime('@' . $date, new DateTimeZone($timezone));
        $dateObj->setTimezone(new DateTimeZone($timezone));
    } elseif ($date instanceof \DateTime) {
        $dateObj = $date;
    } else {
        return '';
    }

    return $dateObj->format('d M, Y');
}

/**
 * Gets the currency symbol for a given currency code
 *
 * @param string $currencyCode The ISO 4217 currency code (e.g., USD, EUR, INR)
 * @return string The currency symbol (e.g., $, €, ₹)
 */
function getCurrencySymbol(string $currencyCode): string
{
    // Common currency symbols map for quick lookup
    $commonSymbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'INR' => '₹',
        'JPY' => '¥',
        'CNY' => '¥',
        'AUD' => 'A$',
        'CAD' => 'C$',
        'CHF' => 'Fr',
        'RUB' => '₽',
        'ZAR' => 'R',
        'BRL' => 'R$',
        'NZD' => 'NZ$',
        'SGD' => 'S$',
        'HKD' => 'HK$',
        'SEK' => 'kr',
        'NOK' => 'kr',
        'DKK' => 'kr',
        'PLN' => 'zł',
        'THB' => '฿',
        'KRW' => '₩',
        'MXN' => '$',
        'PHP' => '₱',
        'MYR' => 'RM',
        'IDR' => 'Rp',
        'AED' => 'د.إ',
        'SAR' => '﷼',
        'NGN' => '₦',
        'EGP' => 'E£',
        'PKR' => '₨',
        'TRY' => '₺',
        'ILS' => '₪',
        'ARS' => '$',
        'CLP' => '$',
        'COP' => '$',
        'PEN' => 'S/',
        'UYU' => '$U',
        'ZWL' => 'Z$',
        'ZiG' => 'ZiG$',
    ];

    // Return from the map if available
    if (isset($commonSymbols[strtoupper($currencyCode)])) {
        return $commonSymbols[strtoupper($currencyCode)];
    }

    // Use NumberFormatter as fallback
    try {
        $formatter = new \NumberFormatter(Yii::$app->language, \NumberFormatter::CURRENCY);
        $symbol = $formatter->getSymbol(\NumberFormatter::CURRENCY_SYMBOL);
        return $symbol ?: $currencyCode;
    } catch (\Exception $e) {
        // If all else fails, return the currency code itself
        return $currencyCode;
    }
}

/**
 * Gets the currency name for a given currency code
 *
 * @param string|null $currencyCode The ISO 4217 currency code (e.g., USD, EUR, INR)
 * @return string The currency name (e.g., Dollar, Euro, Rupee)
 */
function getCurrencyName($currencyCode): string
{
    // Handle null or empty currency code
    if ($currencyCode === null || $currencyCode === '') {
        // Try to get default currency from formatter
        try {
            $currencyCode = Yii::$app->formatter->currencyCode;

            // If still empty, try to derive from locale
            if (empty($currencyCode)) {
                $currencyCode = getCurrencyCodeFromLocale(Yii::$app->formatter->locale);
            }
        } catch (\Exception $e) {
            return 'Dollar'; // Default fallback
        }
    }

    // Common currency names map for quick lookup
    $commonNames = [
        'USD' => 'Dollar',
        'EUR' => 'Euro',
        'GBP' => 'Pound Sterling',
        'INR' => 'Rupee',
        'JPY' => 'Yen',
        'CNY' => 'Yuan',
        'AUD' => 'Australian Dollar',
        'CAD' => 'Canadian Dollar',
        'CHF' => 'Swiss Franc',
        'RUB' => 'Ruble',
        'ZAR' => 'Rand',
        'BRL' => 'Real',
        'NZD' => 'New Zealand Dollar',
        'SGD' => 'Singapore Dollar',
        'HKD' => 'Hong Kong Dollar',
        'SEK' => 'Swedish Krona',
        'NOK' => 'Norwegian Krone',
        'DKK' => 'Danish Krone',
        'PLN' => 'Zloty',
        'THB' => 'Baht',
        'KRW' => 'Won',
        'MXN' => 'Mexican Peso',
        'PHP' => 'Philippine Peso',
        'MYR' => 'Ringgit',
        'IDR' => 'Rupiah',
        'AED' => 'Dirham',
        'SAR' => 'Riyal',
        'NGN' => 'Naira',
        'EGP' => 'Egyptian Pound',
        'PKR' => 'Pakistani Rupee',
        'TRY' => 'Turkish Lira',
        'ILS' => 'Shekel',
        'ARS' => 'Argentine Peso',
        'CLP' => 'Chilean Peso',
        'COP' => 'Colombian Peso',
        'PEN' => 'Sol',
        'UYU' => 'Uruguayan Peso',
        'ZWL' => 'Zimbabwe Dollar',
        'ZiG' => 'ZiG Dollar',
    ];

    // Return from the map if available
    if (isset($commonNames[strtoupper($currencyCode)])) {
        return $commonNames[strtoupper($currencyCode)];
    }

    // Try to get the currency name using NumberFormatter
    try {
        $locale = Yii::$app->formatter->locale;
        $formatter = new \NumberFormatter($locale . '@currency=' . $currencyCode, \NumberFormatter::CURRENCY);
        $pattern = $formatter->getPattern();

        // Extract currency name from pattern if possible
        if (preg_match('/[\s]([a-zA-Z]+)[\s]*$/', $pattern, $matches)) {
            return $matches[1];
        }
    } catch (\Exception $e) {
        // Silently fail and use fallback
    }

    // If all else fails, return the currency code itself
    return $currencyCode;
}

/**
 * Gets the fractional unit name for a given currency code
 *
 * @param string|null $currencyCode The ISO 4217 currency code (e.g., USD, EUR, INR)
 * @return string The fractional unit name (e.g., Cent, Cent, Paisa)
 */
function getFractionalUnitName($currencyCode): string
{
    // Handle null or empty currency code
    if ($currencyCode === null || $currencyCode === '') {
        // Try to get default currency from formatter
        try {
            $currencyCode = Yii::$app->formatter->currencyCode;

            // If still empty, try to derive from locale
            if (empty($currencyCode)) {
                $currencyCode = getCurrencyCodeFromLocale(Yii::$app->formatter->locale);
            }
        } catch (\Exception $e) {
            return 'Cents'; // Default fallback
        }
    }

    // Common fractional unit names map for quick lookup
    $fractionalUnits = [
        'USD' => 'Cents',
        'EUR' => 'Cents',
        'GBP' => 'Pence',
        'INR' => 'Paisa',
        'JPY' => 'Sen',
        'CNY' => 'Fen',
        'AUD' => 'Cents',
        'CAD' => 'Cents',
        'CHF' => 'Rappen',
        'RUB' => 'Kopeks',
        'ZAR' => 'Cents',
        'BRL' => 'Centavos',
        'NZD' => 'Cents',
        'SGD' => 'Cents',
        'HKD' => 'Cents',
        'SEK' => 'Öre',
        'NOK' => 'Øre',
        'DKK' => 'Øre',
        'PLN' => 'Groszy',
        'THB' => 'Satang',
        'KRW' => 'Jeon',
        'MXN' => 'Centavos',
        'PHP' => 'Centavos',
        'MYR' => 'Sen',
        'IDR' => 'Sen',
        'AED' => 'Fils',
        'SAR' => 'Halalas',
        'NGN' => 'Kobo',
        'EGP' => 'Piastres',
        'PKR' => 'Paisa',
        'TRY' => 'Kuruş',
        'ILS' => 'Agorot',
        'ARS' => 'Centavos',
        'CLP' => 'Centavos',
        'COP' => 'Centavos',
        'PEN' => 'Céntimos',
        'UYU' => 'Centésimos',
        'ZWL' => 'Cents',
        'ZiG' => 'Cents',
    ];

    // Return from the map if available
    if (isset($fractionalUnits[strtoupper($currencyCode)])) {
        return $fractionalUnits[strtoupper($currencyCode)];
    }

    // Default fallback
    return 'Cents';
}

/**
 * Derives a currency code from a locale string
 *
 * @param string|null $locale The locale string (e.g., en-US, fr-FR)
 * @return string The derived currency code
 */
function getCurrencyCodeFromLocale($locale): string
{
    if (empty($locale)) {
        return 'USD'; // Default fallback
    }

    // Special case for locales like en-ZX
    if ($locale === 'en-ZX') {
        return 'ZWL'; // Zimbabwe Dollar
    }

    // Convert locale format from en-US to en_US if needed
    $locale = str_replace('-', '_', $locale);

    // Try to get the currency code directly from NumberFormatter
    try {
        $formatter = new \NumberFormatter($locale, \NumberFormatter::CURRENCY);
        $currencyCode = $formatter->getTextAttribute(\NumberFormatter::CURRENCY_CODE);

        if (!empty($currencyCode)) {
            return $currencyCode;
        }
    } catch (\Exception $e) {
        // Silently fail and continue to fallback methods
    }

    // Extract country code from locale as fallback
    $parts = explode('_', $locale);
    $countryCode = end($parts);

    // Map of common country codes to currency codes (as fallback)
    $countryCurrencyMap = [
        'US' => 'USD', // United States
        'GB' => 'GBP', // United Kingdom
        'EU' => 'EUR', // European Union
        'IN' => 'INR', // India
        'JP' => 'JPY', // Japan
        'CN' => 'CNY', // China
        'AU' => 'AUD', // Australia
        'CA' => 'CAD', // Canada
        'CH' => 'CHF', // Switzerland
        'RU' => 'RUB', // Russia
        'ZA' => 'ZAR', // South Africa
        'BR' => 'BRL', // Brazil
        'NZ' => 'NZD', // New Zealand
        'SG' => 'SGD', // Singapore
        'HK' => 'HKD', // Hong Kong
        'SE' => 'SEK', // Sweden
        'NO' => 'NOK', // Norway
        'DK' => 'DKK', // Denmark
        'PL' => 'PLN', // Poland
        'TH' => 'THB', // Thailand
        'KR' => 'KRW', // South Korea
        'MX' => 'MXN', // Mexico
        'PH' => 'PHP', // Philippines
        'MY' => 'MYR', // Malaysia
        'ID' => 'IDR', // Indonesia
        'AE' => 'AED', // United Arab Emirates
        'SA' => 'SAR', // Saudi Arabia
        'NG' => 'NGN', // Nigeria
        'EG' => 'EGP', // Egypt
        'PK' => 'PKR', // Pakistan
        'TR' => 'TRY', // Turkey
        'IL' => 'ILS', // Israel
        'AR' => 'ARS', // Argentina
        'CL' => 'CLP', // Chile
        'CO' => 'COP', // Colombia
        'PE' => 'PEN', // Peru
        'UY' => 'UYU', // Uruguay
        'ZW' => 'ZWL', // Zimbabwe
        // Euro countries
        'DE' => 'EUR', // Germany
        'FR' => 'EUR', // France
        'IT' => 'EUR', // Italy
        'ES' => 'EUR', // Spain
        'PT' => 'EUR', // Portugal
        'GR' => 'EUR', // Greece
        'IE' => 'EUR', // Ireland
        'FI' => 'EUR', // Finland
        'AT' => 'EUR', // Austria
        'BE' => 'EUR', // Belgium
        'NL' => 'EUR', // Netherlands
        'LU' => 'EUR', // Luxembourg
        'SK' => 'EUR', // Slovakia
        'SI' => 'EUR', // Slovenia
        'EE' => 'EUR', // Estonia
        'LV' => 'EUR', // Latvia
        'LT' => 'EUR', // Lithuania
        'CY' => 'EUR', // Cyprus
        'MT' => 'EUR', // Malta
    ];

    // Check if we have a mapping for this country code
    if (isset($countryCurrencyMap[strtoupper($countryCode)])) {
        return $countryCurrencyMap[strtoupper($countryCode)];
    }

    // Default fallback
    return 'USD';
}
