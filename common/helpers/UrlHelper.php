<?php


namespace common\helpers;


use Yii;
use yii\helpers\Url;

class UrlHelper
{
    public static function frontendUrl($url)
    {
        return Yii::$app->urlManagerFrontend->createAbsoluteUrl($url);
    }

    public static function backendUrl($url)
    {
        return Yii::$app->urlManagerBackend->createAbsoluteUrl($url);
    }

    public static function uploadUrl($url)
    {
        return Yii::$app->urlManagerUpload->createAbsoluteUrl($url);
    }

    public static function billingUrl($url)
    {
        /** var  UrlHelper */
        return Yii::$app->urlManagerBilling->createAbsoluteUrl($url);
    }


}