<?php

use kartik\mpdf\Pdf;

return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'vendorPath' => dirname(__DIR__, 2) . '/vendor',
    'bootstrap' => [
        'common\helpers\FileSystemBootstrap',
    ],
    'components' => [
        'limitChecker' => [
            'class' => 'common\components\LimitChecker',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'transport' => [
                'dsn' => 'native://default',
            ],
            'viewPath' => '@common/mail',
            'useFileTransport' => false,
        ],
        'i18n' => [
            'translations' => [
                'template*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@common/messages',
                    'sourceLanguage' => 'en-US',
                    'fileMap' => [
                        'template-default' => 'template-default.php',
                    ],
                ],
            ],
        ],
        'urlManagerUpload' => [
            'class' => 'yii\web\urlManager',
            'baseUrl' => '/uploads/',
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'urlManagerBilling' => [
            'class' => 'yii\web\urlManager',
            'baseUrl' => '/billing/',
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'urlManagerBackend' => [
            'class' => 'yii\web\urlManager',
            'baseUrl' => '/',
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],

        'urlManagerFrontEnd' => [
            'class' => 'yii\web\urlManager',
            'baseUrl' => '/frontend/',
            'enablePrettyUrl' => true,
            'showScriptName' => false,
        ],
        'formatter' => [
//            'dateFormat' => 'dd/MM/yyyy',
//            'decimalSeparator' => '.',
//            'thousandSeparator' => ',',
//            'currencyCode' => 'USD',
//            'locale' => 'en_US',
        ],
        'pdf' => [
            'class' => Pdf::classname(),
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_FILE,
            // refer settings section for all configuration options
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'queue' => null, // setup this in environment-specific config files
        'queueService' => [
            'class' => common\services\QueueService::class,
        ],
    ],
];
