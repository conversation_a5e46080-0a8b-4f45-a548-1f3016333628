<?php

return [
    'components' => [
        'queue' => [
            'class' => \yii\queue\redis\Queue::class,
            'redis' => [
                'class' => \yii\redis\Connection::class,
                'hostname' => 'localhost',
                'port' => 6379,
                'database' => 0,
            ],
            'channel' => 'default',
            'as log' => \yii\queue\LogBehavior::class,
            'attempts' => 3,
            'ttr' => 5 * 60, // 5 minutes
        ],
    ],
];
