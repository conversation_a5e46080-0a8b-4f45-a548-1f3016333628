<?php
// Define IS_PREMIUM_APP constant for this app
if (!defined('IS_PREMIUM_APP')) {
    define('IS_PREMIUM_APP', false);
}

return [
    'name' => APP_NAME,
    'id' => APP_ID,
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_quotation-pro',
            'username' => 'praxinfo_quotation-pro',
            'password' => 'quotation@pro',
            'charset' => 'utf8mb4',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'transport' => [
                'dsn' => 'native://default',
            ],
            'viewPath' => '@common/mail',
            'useFileTransport' => false,
        ],
        'queue' => [
            'class' => \yii\queue\redis\Queue::class,
            'redis' => [
                'class' => \yii\redis\Connection::class,
                'hostname' => '127.0.0.1',
                'port' => 6379,
                'database' => 0,
            ],
            'channel' => 'default',
            'as log' => \yii\queue\LogBehavior::class,
            'attempts' => 3,
            'ttr' => 5 * 60, // 5 minutes
        ],
    ],
];