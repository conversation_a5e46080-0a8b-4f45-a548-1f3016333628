<?php
// Define IS_PREMIUM_APP constant for this app
if (!defined('IS_PREMIUM_APP')) {
    define('IS_PREMIUM_APP', true);
}

return [
    'name' => APP_NAME,
    'id' => APP_ID,
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_quotation-custom',
            'username' => 'praxinfo_quotation-custom',
            'password' => 'quotation@pro',
            'charset' => 'utf8mb4',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'transport' => [
                'dsn' => 'native://default',
            ],
            'viewPath' => '@common/mail',
            'useFileTransport' => false,
        ],
    ],
];