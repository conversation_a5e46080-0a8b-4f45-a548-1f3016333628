<?php
// Define IS_PREMIUM_APP constant for this app
if (!defined('IS_PREMIUM_APP')) {
    define('IS_PREMIUM_APP', false);
}

return [
    'name' => APP_NAME,
    'id' => APP_ID,
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_estimate_pro',
            'username' => 'praxinfo_estimate_pro',
            'password' => 'estimate@pro',
            'charset' => 'utf8mb4',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'transport' => [
                'dsn' => 'native://default',
            ],
            'viewPath' => '@common/mail',
            'useFileTransport' => false,
        ],
        'queue' => [
            'class' => \yii\queue\db\Queue::class,
            'db' => 'db',
            'tableName' => '{{%queue}}',
            'channel' => 'default',
            'mutex' => \yii\mutex\MysqlMutex::class,
            'as log' => \yii\queue\LogBehavior::class,
            'attempts' => 3,
            'ttr' => 5 * 60, // 5 minutes
        ],
    ],
];