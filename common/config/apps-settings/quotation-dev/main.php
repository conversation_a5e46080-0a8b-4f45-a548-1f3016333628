<?php
// Define IS_PREMIUM_APP constant for this app
if (!defined('IS_PREMIUM_APP')) {
    define('IS_PREMIUM_APP', false);
}

return [
    'name' => APP_NAME,
    'id' => APP_ID,
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_quotation-dev',
            'username' => 'praxinfo_quotation-dev',
            'password' => 'dev@quotation9',
            'charset' => 'utf8mb4',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'transport' => [
                'dsn' => 'native://default',
            ],
            'viewPath' => '@common/mail',
            'useFileTransport' => false,
        ],
        'queue' => [
            'class' => \yii\queue\redis\Queue::class,
            'redis' => [
                'class' => \yii\redis\Connection::class,
                'hostname' => '127.0.0.1',
                'port' => 6379,
                'database' => 1,
            ],
            'channel' => 'default',
            'as log' => \yii\queue\LogBehavior::class,
            'attempts' => 3,
            'ttr' => 5 * 60, // 5 minutes
        ],
    ],
];