<?php

use common\models\enum\Key;
use common\models\enum\StripeAccount;

$firebaseKey = 'AAAAOkLVpW4:APA91bHV79OVarExRVKn16vU1ZcHUGzhMZEwXshohlF1WLiiAQzDscL2G9lzy_21YIkY8LEgMTEPx126lgpxAXPdrlfgMOfscbCNy6XbBSXr0wd87TKYn5ryoETXXTPKCBudqBVio2MR';

$appSettings = require __DIR__ . "/app-settings-params.php"; // app-settings (stored in db)

return array_merge($appSettings, [
    Key::IOS_REWARD_ADS_UNIT_ID => 'ca-app-pub-****************/**********', // LIVE Ads
    Key::ANDROID_REWARD_ADS_UNIT_ID => 'ca-app-pub-****************/**********',

    // iTunes keys - in-app purchase
    iTunesPurchaseSecret => '0f690a70d4b54021b36391ac6d24a3ed',
    inAppPurchaseMode => PROD_MODE, // dev OR prod

    iosAppName => "Quotation Maker",
    iosAppLink => "https://apps.apple.com/us/app/quotation-maker/id1618883268",

    androidAppName => "Quotation Maker",
    androidAppLink => "https://play.google.com/store/apps/details?id=com.praxinfo.quotationmaker&hl=en_US",

    STRIPE_OLD_ACCOUNT_LAST_ID => 0,

    FIREBASE_SERVER_KEY => $firebaseKey,

    Key::MAINTENANCE_MODE => false,
    Key::LOG_VALIDATION_ERRORS => false,

    isQueueManagementEnabled => true,
    isSMTPEnabled => false,
    isOptionalEmailsEnabled => false,


    // Need to define two default accounts. 1. for Indian payments 2. for Foreign accounts!
    Key::STRIPE_ACCOUNTS => [
        Key::INDIA_ACCOUNT => StripeAccount::PRAXINFO_SOLUTIONS,
        Key::FOREIGN_ACCOUNT => StripeAccount::PRAXINFO,
        Key::DEFAULT_ACCOUNT => StripeAccount::PRAXINFO_SOLUTIONS,
        StripeAccount::PRAXINFO_SOLUTIONS => [
            Key::PUBLISHABLE_KEY => 'pk_test_51N9jNJSGQ35XccqP10Ntm5r54snP6XFClUN6b5eZgYX6WbRQfAonv2vIEexrGxDPdqMDW4hZViu4wciH9RCPDUsX00uOaN5Uq7',
            Key::SECRET_KEY => 'sk_test_51N9jNJSGQ35XccqPoDCv2l2l9oR5etMtdsZuMsDmoZ44kyjQv88iQBb9FaMPoTNmsINFfbQ3V1igWz4dH0rCNYuS00DdmmlI3K',
            Key::WEBHOOK_SECRET => 'we_1N9pAiSGQ35XccqPyR4hWAzf',
        ],
        StripeAccount::PRAXINFO => [
            Key::PUBLISHABLE_KEY => 'pk_test_51Mr1dkSE0znpDYhhlYGuXezRWKrhtj5qXJaEOilTDiXBAXTsOH2ucD6UaKox51qspjxrlQKDRnhHylmk0pz9loD900gQCrZZWS',
            Key::SECRET_KEY => 'sk_test_51Mr1dkSE0znpDYhh1PhXWh3XMG9qw3Dt9fGkgIvGeMcwletlXQbtdRnNEm0dr4Ftt172KWjPYIbfV6wQFpMls89T00nHpxUZIL',
            Key::WEBHOOK_SECRET => 'we_1Mw1fASE0znpDYhhfRyRzg46',
        ],
    ],


]);
