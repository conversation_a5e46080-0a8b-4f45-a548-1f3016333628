# Quotation Pro App - API Technical Documentation

> **Note:** This documentation focuses exclusively on the API module of the Quotation Pro App. The backend, billing, and frontend modules have been removed to create a streamlined API-only version of the application.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Project Architecture and Directory Focus](#project-architecture-and-directory-focus)
3. [Technical Architecture](#technical-architecture)
4. [PDF Generation System](#pdf-generation-system)
5. [Email System](#email-system)
6. [Third-Party Integrations](#third-party-integrations)
7. [Developer Onboarding](#developer-onboarding)
8. [Detailed Directory Structure and Key Files](#detailed-directory-structure-and-key-files)
9. [Data Model and Relationships](#data-model-and-relationships)
10. [Best Practices for Development](#best-practices-for-development)
11. [Security Considerations](#security-considerations)
12. [Testing Procedures](#testing-procedures)
13. [Troubleshooting Guide](#troubleshooting-guide)
14. [Performance Optimization](#performance-optimization)

## Project Overview
The **Quotation Pro App** is a comprehensive business management API designed to facilitate the creation and management of quotations, invoices, budgets, and purchase orders. It supports a multi-tenant design, allowing multiple businesses to use the same instance while maintaining data isolation. This version of the application focuses exclusively on the API module, providing a streamlined RESTful interface for mobile and web clients.

### Key Functionalities
- **Quotations:** Create, manage, and send quotations to clients.
- **Invoices:** Generate and send invoices to clients.
- **Budgets:** Plan and manage budgets for projects.
- **Purchase Orders:** Manage purchase orders for suppliers.
- **Proforma Invoice Handling:** Handle proforma invoices.
- **Delivery Notes:** Generate and manage delivery notes.
- **Receipts:** Create and manage payment receipts.
- **Subscription Plans:** Manage subscription plans with tiered features.
- **WhatsApp Integration:** Generate support links in notification emails with userId as support pin.
- **Firebase Integration:** For real-time data synchronization and notifications.
- **PDF Generation:** For generating customizable PDF documents from all business documents.
- **Email Notifications:** Send templated email notifications to users and customers with AI-powered personalization.
- **Multi-Tenant Design:** Support multiple businesses on a single instance with complete data isolation.
- **Inventory Management:** Track product stock levels and transactions.
- **User Role Management:** Support for business owners and staff with appropriate permissions.
- **Admin Notifications:** Comprehensive notification system for administrators with detailed event information.

### Underlying Framework and Integrations
- **Framework:** Yii2 (PHP MVC Framework)
- **Database:** MySQL/MariaDB
- **API Architecture:** RESTful API with token-based authentication
- **Integrations:** Stripe, Razorpay, WhatsApp, Firebase, Google Contacts, PDF Generation

## Project Architecture and Directory Focus

### .gitignore Pre-Processing
- **Initial Check:** At the beginning of each update, re-read the project’s `.gitignore` file(s) and ignore all files and directories listed there.
- **Selective Focus:** This ensures that you concentrate solely on files vital to core development and business logic.

### Directory Focus Prioritization
#### Available Directories
- **`api/` Directory:** Core REST API implementation
- **`common/` Directory:** Shared components, configurations, base models
- **`console/` Directory:** Migration scripts, console command controllers
- **`templates/` Directory:** Document templates, PDF layouts

## Guided Update Process

### Step-by-Step Processing
1. **Identify and Process:** Start with new or modified files from prioritized directories.
2. **Context Consolidation:** Merge newly processed information with existing memory context.
3. **Ensure Consistency:** Confirm that all commit messages, code generation, or debugging requests reference the most current, consolidated context.

### Clarification and Conflict Resolution
If discrepancies arise between stored context and recent updates, request targeted clarifications rather than re-reading previously ingested files.

## Continuous and Dynamic Context Utilization

### For All Tasks
- **Code Generation & Debugging:** Use continuously updated memory context
- **Context-Aware Commits:** Commit messages should reference current project context
- **Avoid Redundancies:** Reprocess files only if modifications necessitate further analysis

## Database Structure Understanding

### Migrations
- **Location:** `/console/migrations`
- **Purpose:** Manage database schema evolution

### Base Models
- **Location:** `/common/models/base`
- **Purpose:** Represent database tables through Gii-generated models

### Business Settings Logic
- **Key Files/Classes:** `business.php`, `BusinessSettings` class, `DefaultBusinessSettings` class
- **Purpose:** Handle dynamic business configurations

### User Model & Other Key Models
- **Focus:** Understand how user data is managed and related to other entities

## Technical Architecture

### API-Only Architecture
- **RESTful API:** Comprehensive API for mobile and web clients
- **Versioned Endpoints:** Versioned for backward compatibility
- **Token-Based Authentication:** Secured via `AUTH_TOKEN` header validation
- **JSON Response Format:** Consistent JSON structure

### Multi-Tenant Implementation
- **Business Isolation:** Through `businessId` foreign key
- **User-Business Association:** Through junction table
- **Domain-Based Access:** Custom domains route to specific instances

### Authentication and Authorization
- **Token-Based Authentication:** In `BaseApiController::_checkAuth()`
- **Role-Based Access:** Business owners have full access
- **Social Authentication:** Through `UserSocialProvider` model

### Document Number Generation
- **Application-Managed Increments:** Document numbers managed in code
- **Business-Specific Prefixes:** Custom prefixes for different document types
- **Sequence Management:** Business table maintains counters

### Email System
- **Centralized Service:** EmailService singleton manages operations
- **Template-Based:** Templates stored in `common/mail`
- **SMTP Load Balancing:** Multiple SMTP configurations with automatic rotation

### PDF Generation System
- **Template Architecture:** Separation of content and layout
- **Dynamic Content Injection:** Using PHP variables
- **Generation Process:** Uses mPDF library for conversion

## Developer Onboarding

### Environment Setup
1. Clone repository
2. Install dependencies
3. Configure environment
4. Run migrations
5. Initialize application
6. Sync settings

### Key Configuration Files
- **`common/config/main.php`** - Core application configuration
- **`common/config/main-local.php`** - Environment-specific configuration
- **`common/config/params.php`** - Global parameters
- **`common/config/params-local.php`** - Environment-specific parameters

### Multi-Environment Configuration
- Defined by `APP_ID` in `main-local.php`
- Supports multiple environments (dev, prod, local, premium)

## Scheduled Maintenance Tasks

```bash
# Clean up old files monthly
0 2 4 * * /path/to/app/yii data-cleanup/cleanup-old-files

# Clean up files for inactive businesses weekly
0 3 * * 0 /path/to/app/yii data-cleanup/cleanup-inactive-business-files

# Sync application settings daily
0 4 * * * /path/to/app/yii batch-update/sync-app-settings
```

## Detailed Directory Structure and Key Files

### Application Structure
- **`api/`** - REST API implementation
    - **`modules/v1/controllers/`** - API controllers
    - **`modules/v1/models/`** - API-specific models
    - **`web/index.php`** - Entry point for the REST API
- **`common/`** - Shared code between applications
    - **`models/`** - Business logic and data models
    - **`helpers/`** - Utility functions
    - **`services/`** - Service classes for external integrations
    - **`config/`** - Shared configuration files
- **`console/`** - Command-line utilities
    - **`controllers/`** - Console command controllers
    - **`migrations/`** - Database migration scripts
    - **`yii`** - Console commands entry point
- **`templates/`** - PDF templates and layouts

### Key Model Files
- **`common/models/Business.php`** - Business entity and multi-tenant core
- **`common/models/User.php`** - User management and authentication
- **`common/models/Quotation.php`** - Quotation document logic
- **`common/models/Invoice.php`** - Invoice document logic
- **`common/models/BusinessSettings.php`** - Business-specific configuration

## Data Model and Relationships

### Core Entity Relationships
- **Business → User:** One-to-many through `user_business` junction table
- **Business → Document Types:** One-to-many
- **Document → Items:** One-to-many

### Soft Deletion Pattern
- Most entities implement soft deletion via an `isDeleted` boolean flag
- Deleted records maintain a `deletedAt` timestamp
- Queries automatically filter out soft-deleted records

### Business Settings Structure
- Settings stored in `business_settings` table with `group`, `key`, `value` pattern
- Default settings provided by `DefaultBusinessSettings` class

## Best Practices for Development

### Architecture and Design Principles
Follow SOLID, DRY, KISS, YAGNI principles
Implement OWASP Top 10 security best practices

### Code Structure Implementation
- Repository Pattern with interfaces
- Service Layer with dependency injection
- Model validation with Yii2's rules()
- Controller implementation following RESTful principles

### Multi-Tenant Development
- Always scope queries by `businessId`
- Use `Business::getBusiness()` for current business context
- Implement proper access controls

### Document Generation
- Follow existing patterns for new document types
- Implement number generation in `afterSave()` methods
- Use template system for consistent PDF generation

### API Development
- Extend `BaseApiController`
- Use `_checkAuth()` for token validation
- Follow RESTful principles
- Document endpoints with annotations
- Implement proper error handling

### Email Development
- Use the `EmailService` singleton
- Follow template structure
- Test across clients and devices
- Implement proper error handling
- Use AI-powered content generation

### Commit Message Best Practices
- Use Conventional Commit format
- Include type (`feat`, `fix`, `docs`, etc.)
- Include scope when relevant
- Write summary in imperative mood
- Add detailed explanation with bullet points
- Use optional footer for references

Examples:
```
feat(business): enable PDF storage for premium app

- Add condition to always return true for PDF storage availability if IS_PREMIUM_APP is true
- This change ensures that PDF storage is always enabled for premium app users`
```
```
feat(pdf): implement new PDF generation service

- Add PdfService class to handle PDF generation for various document types
- Create BaseTemplate class for common PDF template functionality
- Implement CustomMpdfCache to fix temporary file handling issues
- Add TemplateHelper class for utility functions in PDF templates
- Update main configuration to include new PDF components and services
- Modify Invoice and Quotation models to use the new PdfService
```

## Security Considerations

### Authentication and Authorization
- **API Authentication:** Secured via `AUTH_TOKEN` header validation
- **Password Security:** Passwords are hashed using Yii2's security component
- **RBAC Implementation:** Role-based access control for different user types

### Data Protection
- **Multi-Tenant Isolation:** Strict business ID filtering on all queries
- **Input Validation:** All user inputs are validated
- **SQL Injection Prevention:** Using Yii2's query builder

## Testing Procedures

### Unit Testing
- **Test Location:** `/tests/unit/`
- **Running Tests:** `./yii test/run`
- **Key Test Classes:** QuotationTest, InvoiceTest, BusinessSettingsTest

### API Testing
- **Postman Collection:** Available in `/docs/api-tests/`
- **Environment Setup:** Configure environment variables in Postman

### Manual Testing Checklist
1. **Document Generation:** Verify PDF generation for all document types
2. **Payment Processing:** Test Stripe and Razorpay payment flows
3. **Multi-Tenant Isolation:** Verify data isolation between businesses
4. **Subscription Management:** Test subscription creation, updates, and cancellations

## Troubleshooting Guide

### Common Issues

#### Document Generation Issues
- **Problem:** PDF generation fails
    - **Solution:** Check template paths and permissions
- **Problem:** Document numbers not incrementing correctly
    - **Solution:** Verify sequence counters in business table

#### API Authentication Issues
- **Problem:** `Invalid AUTH_TOKEN` errors
    - **Solution:** Verify token in headers, check expiration
- **Problem:** Permission denied errors
    - **Solution:** Verify user roles and permissions

#### Multi-Tenant Issues
- **Problem:** Data leakage between businesses
    - **Solution:** Ensure all queries are scoped by businessId
- **Problem:** Settings not applying correctly
    - **Solution:** Verify business settings storage and retrieval

## Performance Optimization

### Database Optimization
- Indexing of key columns (businessId, isDeleted, etc.)
- Query optimization to avoid N+1 queries

### Caching Strategy
- Data caching for frequently accessed data
- Query caching for complex queries

### API Optimization
- Response compression with gzip
- Pagination for large result sets
- Field filtering to reduce response size

## Migration Naming Convention
Always follows the Yii2 migration naming convention: mYYMMDD_HHMMSS_description
Example: m250422_205701_create_queue_table
where:
m is the prefix for migrations
250422 represents the date (April 22, 2025)
205701 represents the time (20:57:01)
create_queue_table describes the migration

## Augment Guidelines for Quotation Pro Project

These guidelines provide additional information for AI agents working with the Quotation Pro project, building upon the general project documentation.

    public function getPosts() {
        return $this->hasMany(Post::class, ['user_id' => 'id'])->lazy();
    }
}
```

##### Controller Implementation
- Controllers must extend `yii\web\Controller`
- Methods must use Yii2's action prefix (e.g., `actionGetUser`)
- Return type must be array with standardized response format
- Must handle exceptions with global exception handler
- Example:
```php
class UserController extends \yii\web\Controller {
    private $userService;

    public function __construct($id, $module, UserService $userService, $config = []) {
        $this->userService = $userService;
        parent::__construct($id, $module, $config);
    }

    public function actionGetUser(int $id) {
        try {
            $user = $this->userService->getUserById($id);
            return ['success' => true, 'data' => $user];
        } catch (\Exception $e) {
            \Yii::error($e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
```

##### API Response Format
- All responses must follow standard format:
```json
{
    "success": true,
    "data": null,
    "timestamp": 1234567890,
    "executionTime": "0.123s",
    "message": "Operation successful"
}
```

### Multi-Tenant Development
- Always scope queries by `businessId` to maintain data isolation
- Use `Business::getBusiness()` to get the current business context
- Implement proper access controls for cross-business operations

### Document Generation
- Follow existing patterns for new document types
- Implement proper number generation in `afterSave()` methods
- Use template system for consistent PDF generation

### API Development
- Extend `BaseApiController` for consistent response formatting
- Use `_checkAuth()` for token validation
- Follow RESTful principles for endpoint design
- Document API endpoints with appropriate annotations
- Implement proper error handling with consistent error codes
- Use pagination for endpoints returning large datasets

### Email Development
- Use the `EmailService` singleton for all email operations
- Follow the existing template structure for new email templates
- Test emails across different clients and devices
- Implement proper error handling for email sending failures
- Use AI-powered content generation for personalized emails

### Commit Message Best Practices
- **Commit messages must follow these rules:**
  - Use short summary (50 characters or less) in imperative mood
  - Include blank line after summary
  - Add detailed explanation with bullet points explaining why the change was made
  - Use optional footer with references or breaking changes
  - Use conventional commit types (feat, fix, docs, etc.)
  - Include scope when relevant (Business, pdf, invoice, etc.)
- **Use a Conventional Commit Message:**
-  ```
-  <type>(<scope>): <short summary in imperative mood>
-
-  <detailed bullet points, if necessary>
-
-  [Optional footer: references to issues or notes]
-  ```
-  Examples:
-- **Type**: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `chore`
-- **Scope**: `business`, `user`, `quotation`, `invoice`, `purchase_order`, `email`, `api`, `common`, `console`, `templates`
-- **Summary**: A short summary of the changes made.
-- **Detailed Bullet Points**: If necessary, provide a list of changes in bullet points.
-- **Footer**: If necessary, provide references to issues or notes.
-- **Example:**
-  ```
-  feat(business): Add subscription plan feature
-
-  - Implement subscription plan model
-  - Add plan selection in business settings
-  - Update user model to track plan
-  - Add plan validation in quotation and invoice models
-
-  Closes #123
-  ```
-- **Context-Aware Messages:**
-  Always reference the complete, up-to-date project context when drafting commit messages.
-- **Inclusion of Details:**
-  Include details about updates in high-priority areas (API, common, console, templates) and mention if any configurations from the database or business settings were affected.
-- **Clarity and Precision:**
-  Ensure that each commit message clearly explains what was modified and why, referencing the corresponding sections of this documentation as needed.
- Writing good commit messages is essential for maintaining a clean, understandable, and traceable codebase. Here are the **best practices and rules** for writing effective commit messages:

## ✅ **Best Practices for Commit Messages**

### 1. **Separate Subject from Body**
- Use a short summary (subject) followed by a blank line and then a detailed explanation (body), if needed.

### 2. **Limit the Subject Line**
- **50 characters or less**.
- Capitalize the first letter.
- Use the **imperative mood** (e.g., *Add*, *Fix*, *Update*, not *Added* or *Fixed*).
- Don't end with a period.

### 3. **Wrap the Body**
- Wrap the message body at **72 characters** to enhance readability in terminals and tools.

### 4. **Explain Why, Not Just What**
- In the body, explain the **reason for the change** and **what problem it solves**, not just what you did.

### 5. **Use Consistent Prefixes (Optional but Recommended)**
- Example prefixes:
    - `feat:` for a new feature
    - `fix:` for a bug fix
    - `refactor:` for code refactoring
    - `docs:` for documentation-only changes
    - `test:` for adding or updating tests
    - `chore:` for build, config, or maintenance
    - `style:` for formatting, no code logic change

---

Here's a well-defined **commit message format guide** based on your examples, tailored to your project’s context and teams. This structure uses **Conventional Commits**, adds **scope**, and ensures clarity, especially for larger Laravel/PHP projects.

---

## ✅ **Commit Message Format**

```
<type>(<scope>): <short summary in imperative mood>

<detailed bullet points, if necessary>

[Optional footer: references to issues or notes]
```

### 🔧 **Allowed Types** (same as conventional commits)

| Type       | Purpose                                            |
|------------|----------------------------------------------------|
| `feat`     | A new feature                                       |
| `fix`      | A bug fix                                           |
| `docs`     | Documentation only changes                         |
| `style`    | Formatting, no logic change (e.g., indentation)    |
| `refactor` | Code change that neither fixes a bug nor adds a feature |
| `test`     | Adding or updating tests                           |
| `chore`    | Maintenance tasks, package updates, build setup    |
| `perf`     | Performance improvements                           |
| `ci`       | CI/CD related changes                              |

---

## ✅ **Scopes Examples** (based on your codebase)

| Scope        | Use For                                           |
|--------------|---------------------------------------------------|
| `Business`   | Business logic, app configuration flags           |
| `pdf`        | PDF generation, rendering, templates              |
| `invoice`    | Invoice-specific logic or modules                 |
| `quotation`  | Quotation-specific logic or modules               |
| `auth`       | Authentication and authorization changes          |
| `api`        | APIs or controller-related updates                |
| `config`     | App or service configuration                      |
| `db`         | Database migrations, schema changes               |

#### Example Commit Messages
```
feat(Business): enable PDF storage for premium app

- Add condition to always return true for PDF storage availability if IS_PREMIUM_APP is true
- This change ensures that PDF storage is always enabled for premium app users`
```
```
feat(pdf): implement new PDF generation service

- Add PdfService class to handle PDF generation for various document types
- Create BaseTemplate class for common PDF template functionality
- Implement CustomMpdfCache to fix temporary file handling issues
- Add TemplateHelper class for utility functions in PDF templates
- Update main configuration to include new PDF components and services
- Modify Invoice and Quotation models to use the new PdfService for PDF generation
```
```
fix(db): update database name in quotation-local config

- Change database name from '1-quotation-pro-local' to 'quotation-pro-local'
```

## Security Considerations

### Authentication and Authorization
- **API Authentication:** Secured via `AUTH_TOKEN` header validation in `BaseApiController::_checkAuth()`
- **Password Security:** Passwords are hashed using Yii2's security component
- **RBAC Implementation:** Role-based access control for different user types
- **CSRF Protection:** Implemented for web forms in the backend application
- **Rate Limiting:** API endpoints have rate limiting to prevent abuse

### Data Protection
- **Multi-Tenant Isolation:** Strict business ID filtering on all queries
- **Input Validation:** All user inputs are validated using model rules
- **SQL Injection Prevention:** Using Yii2's query builder and ActiveRecord
- **XSS Prevention:** Output encoding in views
- **Sensitive Data:** API keys and credentials stored in environment-specific config files

### Third-Party Integrations
- **Stripe:** Server-side confirmation for payments
- **Firebase:** Service account with minimal required permissions
- **Webhook Security:** Signature verification for payment webhooks

## Testing Procedures

### Unit Testing
- **Test Location:** `/tests/unit/`
- **Running Tests:** `./yii test/run`
- **Key Test Classes:**
    - `QuotationTest`: Tests for quotation generation and numbering
    - `InvoiceTest`: Tests for invoice generation and numbering
    - `BusinessSettingsTest`: Tests for business settings management

### API Testing
- **Postman Collection:** Available in `/docs/api-tests/`
- **Environment Setup:** Configure environment variables in Postman
- **Authentication:** Set `AUTH_TOKEN` in request headers

### Manual Testing Checklist
1. **Document Generation:** Verify PDF generation for all document types
2. **Payment Processing:** Test Stripe and Razorpay payment flows
3. **Multi-Tenant Isolation:** Verify data isolation between businesses
4. **Subscription Management:** Test subscription creation, updates, and cancellations
5. **User Management:** Test user creation, role assignment, and permissions

## Troubleshooting Guide

### Common Issues

#### Document Generation Issues
- **Problem:** PDF generation fails
    - **Solution:** Check template paths and permissions, verify dynamic data is correctly passed to templates
- **Problem:** Document numbers not incrementing correctly
    - **Solution:** Check business table for correct sequence counters, verify `afterSave()` method is being called

#### API Authentication Issues
- **Problem:** `Invalid AUTH_TOKEN` errors
    - **Solution:** Verify token is correctly set in headers, check token expiration, ensure user is active
- **Problem:** Permission denied errors
    - **Solution:** Verify user has correct role and permissions for the requested action

#### Multi-Tenant Issues
- **Problem:** Data leakage between businesses
    - **Solution:** Ensure all queries are properly scoped by businessId, check access control in controllers
- **Problem:** Settings not applying correctly
    - **Solution:** Verify business settings are correctly stored and retrieved

#### Subscription and Payment Issues
- **Problem:** Webhook events not processing
    - **Solution:** Check webhook URL configuration in Stripe/Razorpay dashboard, verify signature validation
- **Problem:** Subscription status not updating
    - **Solution:** Check cron job for subscription status updates, verify webhook handlers

### Debugging Tools

#### Logging
- **Application Logs:** Check `runtime/logs/app.log` for application errors
- **API Logs:** Check `runtime/logs/api.log` for API request/response logs
- **Payment Logs:** Check `runtime/logs/payment.log` for payment processing logs
- **Email Logs:** Check `runtime/logs/email.log` for email sending logs

#### Testing Endpoints
- **API Test:** `/api/v1/api/test` for basic API connectivity testing
- **Webhook Test:** `/api/v1/webhook/test` for webhook testing

#### Database Tools
- **Query Profiling:** Enable query profiling in development environment
- **Explain Queries:** Use `EXPLAIN` to analyze slow queries

## Performance Optimization

### Database Optimization
- **Indexing:** Key columns are indexed for performance (businessId, isDeleted, etc.)
- **Query Optimization:** Use eager loading for related models to avoid N+1 query problems
- **Connection Pooling:** Configured in production environment

### Caching Strategy
- **Data Caching:** Frequently accessed data is cached using Yii2's caching component
- **Query Caching:** Complex queries are cached to reduce database load
- **Fragment Caching:** Used for PDF templates and email templates

### Asset Management
- **Asset Bundling:** CSS and JS files are bundled and minified
- **CDN Integration:** Static assets are served from CDN in production
- **Image Optimization:** Images are optimized for web delivery

### API Optimization
- **Response Compression:** Responses are gzipped to reduce bandwidth
- **Pagination:** Large result sets are paginated to improve performance
- **Field Filtering:** API supports field filtering to reduce response size

## Future Development Roadmap

### Planned Features

#### Short-Term (Next 3 Months)
- **Mobile App Integration:** Enhanced API endpoints for mobile app support
- **Advanced Reporting:** Business intelligence dashboards and custom reports
- **Multi-Currency Support:** Support for multiple currencies per business
- **Document Approval Workflow:** Multi-step approval process for documents
- **Enhanced AI Email Marketing:** Expand AI-powered email personalization capabilities

#### Medium-Term (6-12 Months)
- **AI-Powered Features:** Smart suggestions for pricing and product recommendations
- **Advanced Inventory Management:** Serial number tracking and batch processing
- **Customer Portal:** Self-service portal for customers to view and pay invoices
- **Integration Marketplace:** Plug-and-play integrations with third-party services

#### Long-Term (12+ Months)
- **Blockchain Integration:** Document verification and proof of existence
- **Machine Learning:** Predictive analytics for sales forecasting
- **Global Tax Compliance:** Automated tax calculation for multiple jurisdictions
- **White-Label Solution:** Reseller program for agencies and partners

### Technical Debt Reduction

- **Code Refactoring:** Modernize legacy code and improve code quality
- **Test Coverage:** Increase unit and integration test coverage
- **Documentation:** Enhance API documentation and developer guides
- **Performance Optimization:** Identify and optimize bottlenecks

## Contribution Guidelines

### Development Workflow

1. **Fork the Repository:** Create a fork of the main repository
2. **Create a Feature Branch:** `git checkout -b feature/your-feature-name`
3. **Implement Changes:** Follow coding standards and best practices
4. **Write Tests:** Add unit tests for new features
5. **Submit Pull Request:** Create a pull request with a detailed description

### Coding Standards

- **PSR-2:** Follow PSR-2 coding style guidelines
- **Documentation:** Add PHPDoc comments for all classes and methods
- **Testing:** Write unit tests for new features and bug fixes
- **Naming Conventions:** Follow existing naming conventions

### Review Process

1. **Automated Checks:** Code style and static analysis
2. **Manual Review:** Code review by team members
3. **Testing:** Automated tests and manual testing
4. **Approval:** Final approval by project maintainers

### Release Process

1. **Version Tagging:** Semantic versioning (MAJOR.MINOR.PATCH)
2. **Changelog:** Update CHANGELOG.md with new features and fixes
3. **Release Notes:** Detailed release notes for each version
4. **Deployment:** Automated deployment to staging and production

#### Deployment Process
```bash
# Pull latest changes
git pull

# Update dependencies
composer update

# Run database migrations
./yii migrate

# Sync application settings
./yii batch-update/sync-app-settings

# Sync business settings
./yii batch-update/sync-business-settings
```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

# Augment Guidelines for Quotation Pro Project

This document provides guidelines for AI agents working with the Quotation Pro project. It outlines the project structure, key components, and best practices for making changes to the codebase.

## Project Overview

Quotation Pro is a comprehensive business management system designed to facilitate the creation and management of quotations, invoices, budgets, and purchase orders. It supports a multi-tenant design, allowing multiple businesses to use the same instance while maintaining data isolation.

The application is built on the Yii2 PHP framework and follows a modular architecture with these primary components:

- **API Module**: RESTful API for mobile and web clients
- **Common Module**: Shared models, components, and services
- **Console Module**: Command-line utilities and scheduled tasks
- **Templates**: PDF templates for various document types

## Key Functionalities

- **Quotations:** Create, manage, and send quotations to clients
- **Invoices:** Generate and send invoices to clients
- **Budgets:** Plan and manage budgets for projects
- **Purchase Orders:** Manage purchase orders for suppliers
- **Proforma Invoice Handling:** Handle proforma invoices
- **Delivery Notes:** Generate and manage delivery notes
- **Receipts:** Create and manage payment receipts
- **Subscription Plans:** Manage subscription plans with tiered features
- **PDF Generation:** Generate customizable PDF documents from business documents
- **Email Notifications:** Send templated email notifications with AI-powered personalization
- **Multi-Tenant Design:** Support multiple businesses with complete data isolation
- **Inventory Management:** Track product stock levels and transactions
- **User Role Management:** Support for business owners and staff with appropriate permissions

## Directory Structure

```
quotation-pro/
├── api/                    # API implementation
│   ├── config/             # API-specific configuration
│   ├── modules/            # API modules (versioned)
│   │   └── v1/             # Version 1 of the API
│   │       ├── controllers/# API controllers
│   │       └── models/     # API-specific models
│   └── web/                # Web entry point for API
├── common/                 # Shared code
│   ├── app-config/         # Application-specific configurations
│   ├── components/         # Reusable components
│   ├── config/             # Common configuration
│   │   └── apps-settings/  # Environment-specific settings
│   ├── helpers/            # Utility functions
│   ├── mail/               # Email templates
│   ├── models/             # Data models
│   │   ├── base/           # Base models (Gii-generated)
│   │   ├── enum/           # Enumeration classes
│   │   ├── query/          # Query classes
│   │   └── traits/         # Shared traits
│   └── services/           # Service classes
├── console/                # Command-line utilities
│   ├── controllers/        # Console command controllers
│   ├── migrations/         # Database migrations
│   └── models/             # Console-specific models
├── templates/              # PDF templates
│   ├── budget/             # Budget templates
│   ├── delivery_note/      # Delivery note templates
│   ├── invoice/            # Invoice templates
│   ├── proforma_invoice/   # Proforma invoice templates
│   ├── purchase_order/     # Purchase order templates
│   ├── quotation/          # Quotation templates
│   └── receipt/            # Receipt templates
└── docs/                   # Documentation files
```

## Documentation Structure

The documentation is organized into several key files in the `docs/` directory:

1. **README.md**: Main entry point for documentation
2. **project_overview.md**: Detailed system architecture and data flow
3. **project_structure.md**: Directory structure and file organization
4. **email_system_documentation.md**: Email system architecture and components
5. **email_system_split.md**: Details about the EmailClient and EmailService split
6. **pdf_generation_system.md**: PDF template system and generation process
7. **API_DOCUMENTATION.md**: API endpoints and usage
8. **data_model_diagram.md**: Entity relationships and database structure

## Key Subsystems

### Email System

The email system has been refactored to follow a modular architecture with clear separation of concerns:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level email operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient for the underlying infrastructure

#### EmailClient Features
- Singleton pattern implementation for consistent configuration
- SMTP management with multiple providers and load balancing
- Mailer factory methods for different types of mailers
- Email tracking with counters for sent emails
- Rate limiting for SMTP providers
- Urgent admin notifications

#### EmailService Features
- Email composition with template support
- Specialized methods for different types of emails
- Error handling with centralized logging
- User and admin email sending capabilities

#### Email System Usage
- Create a new `EmailService` instance for email operations (not a singleton)
- Use `EmailClient` only for low-level mailer operations
- Follow the existing template structure for new email templates
- Always wrap email sending in try-catch blocks and log errors

### PDF Generation System

- Template-based using mPDF library
- Customizable templates for different document types
- Dynamic content generation based on document data
- Template selection based on business settings
- Support for custom headers, footers, and watermarks

### Multi-Tenant Architecture

- Business entity at the core of data model
- All queries scoped by businessId to maintain data isolation
- User-Business relationship for access control
- Business-specific settings stored in database

### API Architecture

- RESTful API design with versioning (currently v1)
- Token-based authentication via `accessToken`
- JSON request and response format
- Consistent response formatting with status codes and messages

## Development Guidelines

### General Approach

1. **Understand Before Changing**: Always thoroughly understand the existing code before making changes
2. **Detailed Planning**: Create a detailed plan before implementing changes
3. **Conservative Changes**: Be conservative with changes, especially to core components
4. **Documentation Updates**: Keep documentation updated with any code changes
5. **Testing**: Test changes thoroughly before considering them complete
6. **Follow DRY Principal**: Avoid repeating yourself in code
7. **SOLID Principal**: Adhere to SOLID principles for code design
8. **Recheck**: After making changes, recheck the implementation thoroughly to ensure it works as expected also check if you can apply DRY in implemented code and refactor.

## Review
- Take more time to thoroughly understand the codebase structure before implementing changes
- Pay closer attention to inheritance patterns and framework-specific conventions (like Yii2's base/extended model
  pattern)
- Think more carefully about database operations to ensure they're efficient and necessary
- Double-check my work for consistency and correctness before presenting solutions
- Consider the long-term maintainability of any changes I propose

### Code Style and Conventions

1. **PSR Standards**: Follow PSR-1, PSR-2, and PSR-4 coding standards
2. **Yii2 Conventions**: Follow Yii2 framework conventions for controllers, models, and views
3. **Naming Conventions**:
    - **Models**: Singular, CamelCase (e.g., `User.php`, `Quotation.php`)
    - **Controllers**: Plural, CamelCase with Controller suffix (e.g., `UsersController.php`)
    - **Services**: CamelCase with Service suffix (e.g., `EmailService.php`, `PdfService.php`)
4. **Documentation**: Use PHPDoc comments for classes, methods, and properties
5. **Type Hints**: Use PHP type for parameters and return types

### Multi-Tenant Development

1. **Business Isolation**: Always scope queries by businessId to maintain data isolation
2. **Business Context**: Use `$this->business` in controllers to access current business context
3. **Business-Scoped Queries**: Implement `findByBusiness()` methods in models
4. **Foreign Keys**: Add businessId foreign key to all business-specific tables

### Email System Development

1. **EmailClient vs EmailService**: Understand the separation of concerns between these classes
2. **Instance Creation**: Create a new `EmailService` instance for email operations instead of using a singleton
3. **Template Structure**: Follow the existing template structure for new email templates
4. **Error Handling**: Always wrap email sending in try-catch blocks and log errors
5. **Specialized Methods**: For new types of emails, add specialized methods to EmailService

### PDF Template Development

1. **Template Structure**: Follow the existing template structure for new templates
2. **Dynamic Content**: Use PHP variables for dynamic content
3. **Responsive Design**: Ensure templates work with different paper sizes and orientations
4. **Conditional Sections**: Implement conditional sections based on document data
5. **Testing**: Test templates with various data scenarios

### API Development

1. **Base Controller**: Extend BaseApiController for consistent response formatting
2. **Authentication**: Use `_checkAuth()` for token validation
3. **RESTful Design**: Follow RESTful principles for endpoint design
4. **Response Format**: Use standardized response methods
5. **Error Handling**: Implement proper error handling with consistent error codes

## Recent Changes and Considerations

### Email System Split

The email system has been recently refactored to split EmailService into:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient

When working with the email system:

- Replace `EmailService::getInstance()` with `new EmailService(true)`
- Use `EmailClient::getInstance()->getSmtpMailer()` for SMTP operations
- Use `self::$emailClient->getLocalizedDate()` for date formatting in EmailService
- Use `EmailClient::getInstance()->sendUrgentNotificationToAdmin()` for urgent admin notifications

This split improves maintainability and provides clearer separation of concerns.

## Best Practices for AI Agents

### Information Gathering

1. **Read Documentation**: Start by reading relevant documentation in the `docs/` directory
2. **Understand Context**: Understand the context and purpose of the code you're working with
3. **Examine Related Files**: Look at related files to understand dependencies and interactions
4. **Check Recent Changes**: Be aware of recent changes that might affect your work

### Planning and Implementation

1. **Create Detailed Plans**: Always create a detailed, step-by-step plan before making changes
2. **Conservative Approach**: Be conservative with changes, especially to core components
3. **Follow Patterns**: Follow existing patterns and conventions in the codebase
4. **Incremental Changes**: Make small, incremental changes rather than large rewrites
5. **Test Thoroughly**: Test changes thoroughly before considering them complete

### Code Quality

1. **Maintain Consistency**: Keep code style consistent with the existing codebase
2. **Add Documentation**: Document your changes with clear comments and PHPDoc blocks
3. **Error Handling**: Implement proper error handling in all code
4. **Performance Considerations**: Consider performance implications of changes
5. **Security Awareness**: Be aware of security implications, especially for API endpoints

### Multi-Tenant Awareness

1. **Business Isolation**: Always consider the multi-tenant nature of the application
2. **Query Scoping**: Ensure all queries are properly scoped by businessId
3. **Access Control**: Implement proper access control checks in controllers
4. **Settings Management**: Handle business-specific settings correctly

### Communication

1. **Clear Explanations**: Provide clear explanations of your changes and reasoning
2. **Documentation Updates**: Suggest updates to documentation when necessary
3. **Alternative Approaches**: Present alternative approaches when relevant
4. **Limitations**: Be upfront about limitations or potential issues with your changes

## Troubleshooting Common Issues

### Multi-Tenant Issues

- **Data Leakage**: If data from other businesses is visible, check for missing businessId conditions in queries
- **Access Control**: Verify proper access control checks in controllers

### Email System Issues

- **SMTP Configuration**: Check SMTP provider configuration in params
- **Template Variables**: Verify all required variables are passed to templates
- **Error Handling**: Check logs for email sending errors
- **Rate Limiting**: Monitor email sending limits and counters

### PDF Generation Issues

- **Template Paths**: Verify template paths and file existence
- **Dynamic Data**: Ensure all required data is passed to templates
- **mPDF Configuration**: Check mPDF configuration for errors
- **File Permissions**: Verify write permissions for PDF storage directories

## Conclusion

Following these guidelines will help ensure consistent, high-quality contributions to the Quotation Pro project. Always prioritize understanding the existing code and architecture before making changes, and maintain a conservative approach to modifications, especially to core components.

## Migration
Always follows the Yii2 migration naming convention with the format mYYMMDD_HHMMSS_description,
for e.g. : m250422_205701_create_queue_table
where:
m is the prefix for migrations
250422 represents the today's date (April 22, 2025)
205701 represents the current time (20:57:01)
create_queue_table is the description of what the migration does
This change ensures that the migration file has a unique name that follows the standard naming convention.