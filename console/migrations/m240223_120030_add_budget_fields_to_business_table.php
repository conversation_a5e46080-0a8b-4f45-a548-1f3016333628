<?php

use console\migrations\BaseMigration;

/**
 * Class m240223_120030_add_budget_fields_to_business_table
 */
class m240223_120030_add_budget_fields_to_business_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%business_stats}}', 'budgetFreeUsageLimit',  $this->integer()->unsigned()->defaultValue(0)->after('dnFreeUsageLimit'));
        $this->addColumn('{{%business_stats}}', 'budgetCount', $this->integer()->unsigned()->defaultValue(0)->after('dnCount'));

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%business_stats}}', 'budgetFreeUsageLimit');
        $this->dropColumn('{{%business_stats}}', 'budgetCount');

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m240223_120030_add_budget_fields_to_business_table cannot be reverted.\n";

        return false;
    }
    */
}
