<?php

use console\migrations\BaseMigration;

/**
 * Class m200611_092955_BaseSchema
 */
class m200611_092955_User extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    var $tableName = 'user';

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci ENGINE=InnoDB';
        }

        $this->createTable($this->tableName, [
            'id' => $this->primaryKey(),
            'email' => $this->string(191)->notNull()->unique(),
            'fromEmail' => $this->string(191),
            'name' => $this->string()->notNull(),
            'phoneNumber' => $this->string(50),
            'addressLine1' => $this->string(),
            'addressLine2' => $this->string(),
            'addressLine3' => $this->string(),
            'latitude' => $this->double(),
            'longitude' => $this->double(),
            'profilePic' => $this->string(),
            'designation' => 'ENUM("sales_director", "sales_manager", "sales_person") NOT NULL DEFAULT "sales_person"',

            'country' => $this->string(30),
            'state' => $this->string(30),
            'city' => $this->string(30),
            'pincode' => $this->string(10),

            'parentId' => $this->integer(),

            'accessToken' => $this->string(64),
            'otpCode' => $this->integer()->unsigned(),
            'otpExpiry' => $this->timestamp(),
            'auth_key' => $this->string(32),
            'password_hash' => $this->string()->notNull(),
            'password_reset_token' => $this->string(64)->unique(),
            'status' => 'ENUM("active", "inactive", "blocked") NOT NULL DEFAULT "active"',
            'isAdmin' => $this->boolean()->defaultValue(0),

            'isDeleted' => $this->boolean()->defaultValue(0),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'deletedAt' => $this->timestamp()->defaultValue(null),
        ], $tableOptions);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($foreignKey, $toTableName, $toColumn) = ['parentId', 'user', 'id'];
        $this->createIndex('createdAt', $this->tableName, 'createdAt');
        $this->createIndex('updatedAt', $this->tableName, 'updatedAt');

        $this->createIndex('idx-user-' . $foreignKey, $this->tableName, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $this->tableName . '-' . $foreignKey,
            $this->tableName,
            $foreignKey,
            $toTableName,
            $toColumn,
            null,
            'CASCADE'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        //drops foreign key for table `$tablename`
        $foreignKey = 'parentId';
        $this->dropForeignKey(
            'fk-' . $this->tableName . '-' . $foreignKey,
            $this->tableName
        );

        $this->dropIndex(
            'idx-' . $this->tableName . '-' . $foreignKey,
            $this->tableName
        );
        $this->dropTable('{{%user}}');

        echo "m200611_092955_User reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200611_092955_BaseSchema cannot be reverted.\n";

        return false;
    }
    */
}
