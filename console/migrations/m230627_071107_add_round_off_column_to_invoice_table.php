<?php

use console\migrations\BaseMigration;

/**
 * <PERSON>les adding columns to table `{{%invoice}}`.
 */
class m230627_071107_add_round_off_column_to_invoice_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%quotation}}', 'roundOffAmount', $this->float()->defaultValue(0)->after('subTotalAmount'));
        $this->addColumn('{{%invoice}}', 'roundOffAmount', $this->float()->defaultValue(0)->after('subTotalAmount'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%quotation}}', 'roundOffAmount');
        $this->dropColumn('{{%invoice}}', 'roundOffAmount');
    }
}
