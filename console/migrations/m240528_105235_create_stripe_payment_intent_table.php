<?php

/**
 * Handles the creation of table `{{%stripe_payment_intent}}`.
 */
class m240528_105235_create_stripe_payment_intent_table extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%stripe_payment_intent}}', [
            'id' => $this->primaryKey(),
            'businessId' => $this->integer()->notNull(),
            'userId' => $this->integer()->notNull(),
            'planId' => $this->integer()->notNull(),
            'provider' => $this->string(191)->defaultValue("stripe"),
            'providerAccount' => $this->string(191)->defaultValue(null),
            'providerPlanId' => $this->string(191)->defaultValue(null),
            'providerSubscriptionId' => $this->string(191)->unique(),
            'lastTransactionId' => $this->string(191)->unique(),
            'providerCustomerId' => $this->string(1000),
            'paymentIntentClientSecret' => $this->string(),
            'startedFromDate' => $this->integer()->notNull(),
            'startDate' => $this->integer()->notNull(),
            'endDate' => $this->integer()->notNull(),
            'status' =>  $this->string(191),
            'otherInfo' =>  $this->string(5000),
            'isActive' => $this->boolean()->notNull(),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ]);

        // creates index for column `userId`
        $this->createIndex(
            '{{%idx-stripe_payment_intent-userId}}',
            '{{%subscription}}',
            'userId'
        );

        // creates index for column `planId`
        $this->createIndex(
            '{{%idx-stripe_payment_intent-planId}}',
            '{{%subscription}}',
            'planId'
        );

        // creates index for column `businessId`
        $this->createIndex(
            '{{%idx-stripe_payment_intent-businessId}}',
            '{{%subscription}}',
            'businessId'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {

        // drops index for column `userId`
        $this->dropIndex(
            '{{%idx-stripe_payment_intent-userId}}',
            '{{%subscription}}'
        );

        // drops index for column `planId`
        $this->dropIndex(
            '{{%idx-stripe_payment_intent-planId}}',
            '{{%subscription}}'
        );

        // drops index for column `businessId`
        $this->dropIndex(
            '{{%idx-stripe_payment_intent-businessId}}',
            '{{%subscription}}'
        );

        $this->dropTable('{{%stripe_payment_intent}}');
    }
}
