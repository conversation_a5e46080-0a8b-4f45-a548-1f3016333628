<?php

/**
 * Class m240313_125216_import_subscription_plan_offer_table_data
 */
class m240313_125216_import_subscription_plan_offer_table_data extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $data = \common\models\SubscriptionPlanOffer::findAll('1=1');
        if ($data == null OR count($data) == 0){
            $this->importData("subscription_plan_offer.sql");
        }else{
            echo "\n\nsubscription_plan_offer table data already available!!.\n\n";
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "\n\nPlease truncate subscription_plan_offer table data manually!!.\n\n";
        // $this->truncateTable('{{%subscription_plan_offer}}');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m240313_125216_import_subscription_plan_offer_table_data cannot be reverted.\n";

        return false;
    }
    */
}
