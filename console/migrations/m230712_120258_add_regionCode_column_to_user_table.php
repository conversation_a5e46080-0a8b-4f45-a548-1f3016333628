<?php

use console\migrations\BaseMigration;

/**
 * <PERSON>les adding columns to table `{{%user}}`.
 * Has foreign keys to the tables:
 *
 * - `{{%region}}`
 */
class m230712_120258_add_regionCode_column_to_user_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%user}}', 'regionCode', $this->string(5)->after('country'));

        // creates index for column `regionCode`
        $this->createIndex(
            '{{%idx-user-regionCode}}',
            '{{%user}}',
            'regionCode'
        );

        // add foreign key for table `{{%region}}`
        $this->addForeignKey(
            '{{%fk-user-regionCode}}',
            '{{%user}}',
            'regionCode',
            '{{%region}}',
            'code',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops foreign key for table `{{%region}}`
        $this->dropForeign<PERSON>ey(
            '{{%fk-user-regionCode}}',
            '{{%user}}'
        );

        // drops index for column `regionCode`
        $this->dropIndex(
            '{{%idx-user-regionCode}}',
            '{{%user}}'
        );

        $this->dropColumn('{{%user}}', 'regionCode');
    }
}
