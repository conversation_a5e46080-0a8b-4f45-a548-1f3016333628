<?php

use console\migrations\BaseMigration;

/**
 * Class m231222_072224_add_proforma_invoice_type_to_template_table
 */
class m231222_072224_add_proforma_invoice_type_to_template_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('{{%template}}', 'type', 'ENUM("quotation", "purchase_order", "invoice", "proforma_invoice") NOT NULL DEFAULT "quotation"');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('{{%template}}', 'type', 'ENUM("quotation", "invoice", "purchase_order") NOT NULL DEFAULT "quotation"');
    }
}
