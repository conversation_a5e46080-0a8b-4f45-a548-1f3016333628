<?php

use yii\db\Migration;

/**
 * Handles adding columns to table `{{%quotation}}`.
 */
class m240223_155531_add_companyScope_column_to_quotation_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%quotation}}', 'companyScopeJson', $this->text()->after('termsJson'));
        $this->addColumn('{{%quotation}}', 'companyScopeIds', $this->string()->after('termsJson'));
        $this->addColumn('{{%quotation}}', 'customerScopeJson', $this->text()->after('companyScopeJson'));
        $this->addColumn('{{%quotation}}', 'customerScopeIds', $this->string()->after('customerScopeJson'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%quotation}}', 'companyScopeJson');
        $this->dropColumn('{{%quotation}}', 'customerScopeIds');
        $this->dropColumn('{{%quotation}}', 'companyScopeIds');
        $this->dropColumn('{{%quotation}}', 'customerScopeJson');
    }
}
