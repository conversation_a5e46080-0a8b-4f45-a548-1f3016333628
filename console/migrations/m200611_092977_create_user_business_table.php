<?php
use console\migrations\BaseMigration;

/**
 * Handles the creation of table `{{%user_business}}`.
 * Has foreign keys to the tables:
 *
 * - `{{%user}}`
 * - `{{%business}}`
 */
class m200611_092977_create_user_business_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    var $tableName = 'user_business';

    public function safeUp()
    {
        $this->createTable('{{%user_business}}', [
            'id' => $this->primaryKey(),
            'userId' => $this->integer()->notNull(),
            'businessId' => $this->integer()->notNull(),
            'isOwner' => $this->boolean()->notNull()->defaultValue(0),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ]);

        // creates index for column `userId`
        $this->createIndex(
            '{{%idx-user_business-userId}}',
            '{{%user_business}}',
            'userId'
        );

        $this->createIndex('createdAt', $this->tableName, 'createdAt');
        $this->createIndex('updatedAt', $this->tableName, 'updatedAt');

        // add foreign key for table `{{%user}}`
        $this->addForeignKey(
            '{{%fk-user_business-userId}}',
            '{{%user_business}}',
            'userId',
            '{{%user}}',
            'id',
            'CASCADE'
        );

        // creates index for column `businessId`
        $this->createIndex(
            '{{%idx-user_business-businessId}}',
            '{{%user_business}}',
            'businessId'
        );

        // add foreign key for table `{{%business}}`
        $this->addForeignKey(
            '{{%fk-user_business-businessId}}',
            '{{%user_business}}',
            'businessId',
            '{{%business}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops foreign key for table `{{%user}}`
        $this->dropForeignKey(
            '{{%fk-user_business-userId}}',
            '{{%user_business}}'
        );

        // drops index for column `userId`
        $this->dropIndex(
            '{{%idx-user_business-userId}}',
            '{{%user_business}}'
        );

        // drops foreign key for table `{{%business}}`
        $this->dropForeignKey(
            '{{%fk-user_business-businessId}}',
            '{{%user_business}}'
        );

        // drops index for column `businessId`
        $this->dropIndex(
            '{{%idx-user_business-businessId}}',
            '{{%user_business}}'
        );

        $this->dropTable('{{%user_business}}');
    }
}
