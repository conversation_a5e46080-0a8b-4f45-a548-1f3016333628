<?php

use console\migrations\BaseMigration;


/**
 * Class m240719_090400_alter_email_field_in_business_table
 */
class m240719_090400_alter_email_field_in_business_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('{{%business}}', 'email', $this->string(191)->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m240719_090400_alter_email_field_in_business_table reverted\n";

        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m240719_090400_alter_email_field_in_business_table cannot be reverted.\n";

        return false;
    }
    */
}
