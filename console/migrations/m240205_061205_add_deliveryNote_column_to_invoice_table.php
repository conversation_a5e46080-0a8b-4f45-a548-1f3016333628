<?php

/**
 * Handles adding columns to table `{{%invoice}}`.
 */
class m240205_061205_add_deliveryNote_column_to_invoice_table extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%invoice}}', 'isDeliveryNote', $this->boolean()->defaultValue(0)->after('isProformaInvoice'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%invoice}}', 'isDeliveryNote');
    }
}
