<?php

use console\migrations\BaseMigration;

/**
 * <PERSON>les adding order and updatedAt columns to quotation_items and invoice_items tables.
 * Similar to the receipt_items table structure where order and updatedAt fields are used for sorting items and tracking modifications.
 */
class m250526_184243_add_order_and_updatedAt_to_quotation_and_invoice_items extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add order column to quotation_items table (similar to receipt_items)
        $this->addColumn('{{%quotation_items}}', 'order', $this->unsignedInteger()->defaultValue(0)->after('quotationId'));

        // Add order column to invoice_items table (similar to receipt_items)
        $this->addColumn('{{%invoice_items}}', 'order', $this->unsignedInteger()->defaultValue(0)->after('invoiceId'));

        // Add updatedAt column to quotation_items table (similar to receipt_items)
        $this->addColumn('{{%quotation_items}}', 'updatedAt', $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')->after('createdAt'));

        // Add updatedAt column to invoice_items table (similar to receipt_items)
        $this->addColumn('{{%invoice_items}}', 'updatedAt', $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')->after('createdAt'));

        // Create indexes for order columns to improve query performance
        $this->createIndex(
            '{{%idx-quotation_items-order}}',
            '{{%quotation_items}}',
            'order'
        );

        $this->createIndex(
            '{{%idx-invoice_items-order}}',
            '{{%invoice_items}}',
            'order'
        );

        // Create indexes for updatedAt columns to improve query performance
        $this->createIndex(
            '{{%idx-quotation_items-updatedAt}}',
            '{{%quotation_items}}',
            'updatedAt'
        );

        $this->createIndex(
            '{{%idx-invoice_items-updatedAt}}',
            '{{%invoice_items}}',
            'updatedAt'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop indexes for order columns
        $this->dropIndex(
            '{{%idx-quotation_items-order}}',
            '{{%quotation_items}}'
        );

        $this->dropIndex(
            '{{%idx-invoice_items-order}}',
            '{{%invoice_items}}'
        );

        // Drop indexes for updatedAt columns
        $this->dropIndex(
            '{{%idx-quotation_items-updatedAt}}',
            '{{%quotation_items}}'
        );

        $this->dropIndex(
            '{{%idx-invoice_items-updatedAt}}',
            '{{%invoice_items}}'
        );

        // Drop order columns
        $this->dropColumn('{{%quotation_items}}', 'order');
        $this->dropColumn('{{%invoice_items}}', 'order');

        // Drop updatedAt columns
        $this->dropColumn('{{%quotation_items}}', 'updatedAt');
        $this->dropColumn('{{%invoice_items}}', 'updatedAt');
    }
}
