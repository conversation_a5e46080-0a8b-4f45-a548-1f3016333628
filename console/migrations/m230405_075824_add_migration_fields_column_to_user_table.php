<?php

use console\migrations\BaseMigration;

/**
 * <PERSON>les adding columns to table `{{%user}}`.
 */
class m230405_075824_add_migration_fields_column_to_user_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableName = '{{%user}}';
        $this->addColumn($tableName, 'isMigrated', $this->boolean()->after('status'));
        $this->addColumn($tableName, 'migratedAt', $this->timestamp()->after('isMigrated'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $tableName = '{{%user}}';
        $this->dropColumn($tableName, 'isMigrated');
    }
}
