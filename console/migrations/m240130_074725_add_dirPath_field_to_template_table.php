<?php

/**
 * Class m240130_074725_add_dirPath_field_to_template_table
 */
class m240130_074725_add_dirPath_field_to_template_table extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%template}}', 'dirName', $this->string()->after('slug')->defaultValue('default')->notNull());
        $this->addColumn('{{%template}}', 'isHeaderFooterEnable', $this->boolean()->after('dirName')->defaultValue(0)->notNull());
        // Update the subscription_log table as per subscription table.
        $sql = "UPDATE template
                SET `dirName` = `slug`
                WHERE `dirName` IS NULL";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%template}}', 'dirName');
        $this->dropColumn('{{%template}}', 'isHeaderFooterEnable');
    }
}
