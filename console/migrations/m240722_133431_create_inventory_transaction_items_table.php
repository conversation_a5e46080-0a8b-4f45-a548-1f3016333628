<?php

/**
 * Handles the creation of table `{{%inventory_transaction_items}}`.
 * Has foreign keys to the tables:
 *
 * - `{{%inventory_transaction}}`
 * - `{{%product}}`
 * - `{{%inventory_location}}`
 * - `{{%user}}`
 */
class m240722_133431_create_inventory_transaction_items_table extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%inventory_transaction_items}}', [
            'id' => $this->primaryKey(),
            'transactionId' => $this->integer()->notNull(),
            'txnDate' => $this->dateTime()->notNull(),
            'txnType' => "ENUM('inward','outward') NOT NULL",
            'productId' => $this->integer()->notNull(),
            'quantity' => $this->float(),
            'finalQuantity' => $this->float(),
            'isEdited' => $this->boolean()->defaultValue(0),
            'version' => $this->integer()->defaultValue(0),
            'locationId' => $this->integer()->notNull(),
            'updatedById' => $this->integer()->notNull(),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ]);

        // creates index for column `transactionId`
        $this->createIndex(
            '{{%idx-inventory_transaction_items-transactionId}}',
            '{{%inventory_transaction_items}}',
            'transactionId'
        );

        // add foreign key for table `{{%inventory_transaction}}`
        $this->addForeignKey(
            '{{%fk-inventory_transaction_items-transactionId}}',
            '{{%inventory_transaction_items}}',
            'transactionId',
            '{{%inventory_transaction}}',
            'id',
            'CASCADE'
        );

        // creates index for column `productId`
        $this->createIndex(
            '{{%idx-inventory_transaction_items-productId}}',
            '{{%inventory_transaction_items}}',
            'productId'
        );

        // add foreign key for table `{{%product}}`
        $this->addForeignKey(
            '{{%fk-inventory_transaction_items-productId}}',
            '{{%inventory_transaction_items}}',
            'productId',
            '{{%product}}',
            'id',
            'CASCADE'
        );

        // creates index for column `locationId`
        $this->createIndex(
            '{{%idx-inventory_transaction_items-locationId}}',
            '{{%inventory_transaction_items}}',
            'locationId'
        );

        // add foreign key for table `{{%inventory_location}}`
        $this->addForeignKey(
            '{{%fk-inventory_transaction_items-locationId}}',
            '{{%inventory_transaction_items}}',
            'locationId',
            '{{%inventory_location}}',
            'id',
            'CASCADE'
        );

        // creates index for column `updatedById`
        $this->createIndex(
            '{{%idx-inventory_transaction_items-updatedById}}',
            '{{%inventory_transaction_items}}',
            'updatedById'
        );

        // add foreign key for table `{{%user}}`
        $this->addForeignKey(
            '{{%fk-inventory_transaction_items-updatedById}}',
            '{{%inventory_transaction_items}}',
            'updatedById',
            '{{%user}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops foreign key for table `{{%inventory_transaction}}`
        $this->dropForeignKey(
            '{{%fk-inventory_transaction_items-transactionId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops index for column `transactionId`
        $this->dropIndex(
            '{{%idx-inventory_transaction_items-transactionId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops foreign key for table `{{%product}}`
        $this->dropForeignKey(
            '{{%fk-inventory_transaction_items-productId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops index for column `productId`
        $this->dropIndex(
            '{{%idx-inventory_transaction_items-productId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops foreign key for table `{{%inventory_location}}`
        $this->dropForeignKey(
            '{{%fk-inventory_transaction_items-locationId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops index for column `locationId`
        $this->dropIndex(
            '{{%idx-inventory_transaction_items-locationId}}',
            '{{%inventory_transaction_items}}'
        );

        // drops foreign key for table `{{%user}}`
        $this->dropForeignKey(
            '{{%fk-inventory_transaction_items-updatedById}}',
            '{{%inventory_transaction_items}}'
        );

        // drops index for column `updatedById`
        $this->dropIndex(
            '{{%idx-inventory_transaction_items-updatedById}}',
            '{{%inventory_transaction_items}}'
        );

        $this->dropTable('{{%inventory_transaction_items}}');
    }
}
