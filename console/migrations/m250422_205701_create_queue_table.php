<?php

use console\migrations\BaseMigration;

/**
 * Handles the creation of table `{{%queue}}`.
 */
class m250422_205701_create_queue_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%queue}}', [
            'id' => $this->primaryKey(),
            'channel' => $this->string(255)->notNull(),
            'job' => $this->binary()->notNull(),
            'pushed_at' => $this->integer()->notNull(),
            'ttr' => $this->integer()->notNull(),
            'delay' => $this->integer()->notNull()->defaultValue(0),
            'priority' => $this->integer()->unsigned()->notNull()->defaultValue(1024),
            'reserved_at' => $this->integer(),
            'attempt' => $this->integer(),
            'done_at' => $this->integer(),
        ]);

        $this->createIndex('{{%queue_channel}}', '{{%queue}}', 'channel');
        $this->createIndex('{{%queue_reserved_at}}', '{{%queue}}', 'reserved_at');
        $this->createIndex('{{%queue_priority}}', '{{%queue}}', 'priority');
        $this->createIndex('{{%queue_delay}}', '{{%queue}}', 'delay');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%queue}}');
    }
}
