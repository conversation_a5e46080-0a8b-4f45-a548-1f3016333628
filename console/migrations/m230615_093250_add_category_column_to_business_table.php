<?php

use console\migrations\BaseMigration;


/**
 * <PERSON>les adding columns to table `{{%business}}`.
 * Has foreign keys to the tables:
 *
 * - `{{%business_category}}`
 */
class m230615_093250_add_category_column_to_business_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%business}}', 'categoryId', $this->integer()->after('name'));
        $this->addColumn('{{%business}}', 'otherCategory', $this->string(100)->after('categoryId'));

        // creates index for column `categoryId`
        $this->createIndex(
            '{{%idx-business-categoryId}}',
            '{{%business}}',
            'categoryId'
        );

        // add foreign key for table `{{%business_category}}`
        $this->addForeignKey(
            '{{%fk-business-categoryId}}',
            '{{%business}}',
            'categoryId',
            '{{%business_category}}',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops foreign key for table `{{%business_category}}`
        $this->dropForeignKey(
            '{{%fk-business-categoryId}}',
            '{{%business}}'
        );

        // drops index for column `categoryId`
        $this->dropIndex(
            '{{%idx-business-categoryId}}',
            '{{%business}}'
        );

        $this->dropColumn('{{%business}}', 'categoryId');
        $this->dropColumn('{{%business}}', 'otherCategory');
    }
}
