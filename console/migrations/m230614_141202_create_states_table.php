<?php

use console\migrations\BaseMigration;

/**
 * Handles the creation of table `{{%states}}`.
 */
class m230614_141202_create_states_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%states}}', [
            'code' => $this->string(5),
            'name' => $this->string(50),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ]);
        // creates index for column `code`
        $this->addPrimaryKey(
            '{{%idx-states-code}}',
            '{{%states}}',
            'code'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%states}}');
    }
}
