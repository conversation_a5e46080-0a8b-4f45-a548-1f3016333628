<?php

/**
 * Class m240206_113940_add_delivery_fields_to_business_stats_table
 */
class m240206_113940_add_delivery_fields_to_business_stats_table extends \console\migrations\BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%business_stats}}', 'dnFreeUsageLimit',  $this->integer()->unsigned()->defaultValue(0)->after('piFreeUsageLimit'));
        $this->addColumn('{{%business_stats}}', 'dnCount', $this->integer()->unsigned()->defaultValue(0)->after('piCount'));

        // set dnFreeUsageLimit for older users.
        $sql = "UPDATE business_stats SET dnFreeUsageLimit = 5;";
        $this->execute($sql);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%business_stats}}', 'piFreeUsageLimit');
        $this->dropColumn('{{%business_stats}}', 'piCount');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m240206_113940_add_delivery_fields_to_business_stats_table cannot be reverted.\n";

        return false;
    }
    */
}
