<?php

use console\migrations\BaseMigration;

/**
 * Handles the creation of table `{{%business_category}}`.
 */
class m230615_093220_create_business_category_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%business_category}}', [
            'id' => $this->primaryKey(),
            'title' => $this->string(150),
            'parentId' => $this->integer(),
            'position' => $this->unsignedInteger(),
            'isDeleted' => $this->boolean()->defaultValue(0),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'deletedAt' => $this->timestamp()->defaultValue(null),
        ]);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['business_category', 'parentId', 'business_category', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );

        //  index-key-name, tablename
        list($table, $indexKey) = ['business_category', 'position'];
        $this->createIndex('idx-' . $table . '-' . $indexKey, $table, $indexKey);


    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['business_category', 'parentId'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        list($table, $indexKey) = ['business_category', 'position'];
        $this->dropIndex(
            'idx-' . $table . '-' . $indexKey,
            $table
        );

        $this->dropTable('{{%business_category}}');
    }
}
