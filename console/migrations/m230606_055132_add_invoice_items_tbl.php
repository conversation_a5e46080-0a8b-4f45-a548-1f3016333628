<?php
use console\migrations\BaseMigration;

/**
 * Class m200612_103529_add_invoice_items_tbl
 */
class m230606_055132_add_invoice_items_tbl extends BaseMigration
{

    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('invoice_items', [
            'id' => $this->primaryKey(),
            'invoiceId' => $this->integer()->notNull(),
            'productId' => $this->integer()->notNull(),
            'description' => $this->string(2000),

            'quantity' => $this->float(),
            'price' => $this->double(4),
            'amount' => $this->double(4),

            'taxAmount' => $this->double(4),
            'discountType' => 'ENUM("flat", "percentage") NOT NULL DEFAULT "percentage"',
            'discountPercentage' => $this->float(2)->defaultValue(0),
            'discountAmount' => $this->double(4)->defaultValue(0),

            'totalAmount' => $this->double(4),

            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

       // $this->createIndex('createdAt', 'invoice', 'createdAt');

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['invoice_items', 'invoiceId', 'invoice', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );


        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['invoice_items', 'productId', 'product', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['invoice_items', 'invoiceId'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'createdAt',
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['invoice_items', 'productId'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        $this->dropTable($table); // Drop invoice-Items table

    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200612_103529_add_invoice_items_tbl cannot be reverted.\n";

        return false;
    }
    */
}
