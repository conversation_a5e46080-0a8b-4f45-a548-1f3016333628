<?php

use console\migrations\BaseMigration;

/**
 * Handles the creation of table `{{%payment_transaction}}`.
 * Has foreign keys to the tables:
 *
 * - `{{%user}}`
 * - `{{%pricing_plan}}`
 */
class m221116_125344_create_subscription_log_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%subscription_log}}', [
            'id' => $this->primaryKey(),
            'userId' => $this->integer()->notNull(),
            'planId' => $this->integer()->notNull(),
            'provider' => "ENUM('apple','stripe') NOT NULL",
            'providerPlanId' => $this->string(191),
            'providerSubscriptionId' => $this->string(191)->comment("first transaction id for apple and subscriptionId for razorpay"),
            'startDate' => $this->integer(),
            'endDate' => $this->integer(),
            'status' => $this->string(),
            'event' => $this->string(),
            'payloadData' => 'LONGTEXT',
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ]);

        $this->createIndex('createdAt', 'subscription_log', 'createdAt');
        $this->createIndex('updatedAt', 'subscription_log', 'updatedAt');


        // creates index for column `userId`
        $this->createIndex(
            '{{%idx-subscription_log-providerSubscriptionId}}',
            '{{%subscription_log}}',
            'providerSubscriptionId'
        );


        // creates index for column `userId`
        $this->createIndex(
            '{{%idx-subscription_log-userId}}',
            '{{%subscription_log}}',
            'userId'
        );

        // creates index for column `planId`
        $this->createIndex(
            '{{%idx-subscription_log-planId}}',
            '{{%subscription_log}}',
            'planId'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops index for column `userId`
        $this->dropIndex(
            '{{%idx-subscription_transaction-userId}}',
            '{{%subscription_log}}'
        );

        // drops index for column `planId`
        $this->dropIndex(
            '{{%idx-subscription_transaction-planId}}',
            '{{%subscription_log}}'
        );

        $this->dropTable('{{%subscription_log}}');
    }
}
