<?php

use console\migrations\BaseMigration;
/**
 * Class m230718_110914_import_state_table_data
 */
class m230804_132622_import_business_category_table_data extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $data = \common\models\BusinessCategory::findAll('1=1');
        if ($data == null OR count($data) == 0){
            $this->importData("business_category.sql");
        }else{
            echo "\n\nbusiness_category.sql table data already available!!.\n\n";
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->truncateTable('{{%business_category}}');
    }
}
