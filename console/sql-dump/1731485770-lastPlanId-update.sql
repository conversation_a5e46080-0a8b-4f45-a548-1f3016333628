
CREATE INDEX idx_subscription_businessId_isActive_endDate ON subscription(businessId, isActive, endDate);

-- Perform update
UPDATE 
    business b
JOIN 
    (SELECT 
        s.businessId, 
        s.planId 
     FROM 
        subscription s
     LEFT JOIN 
        subscription_plan sp ON s.planId = sp.id
     WHERE 
        s.isActive = 0 
     AND 
        sp.type = 'base' 
     AND 
        s.endDate = (SELECT MAX(endDate) 
                     FROM subscription 
                     WHERE businessId = s.businessId 
                     AND isActive = 0 
                     AND planId IN 
                        (SELECT id FROM subscription_plan WHERE type = 'base'))
    ) sub 
ON 
    b.id = sub.businessId 
SET 
    b.lastPlanId = sub.planId;