-- fix region codes in business table.
UPDATE `business` SET regionCode = NULL WHERE regionCode not in (select code from region);


-- Copy all region code from business to User where RegionCode Is NULL in User Table.
UPDATE `user` u
    JOIN business b ON b.ownerId = u.id
    SET u.regionCode = b.regionCode WHERE u.regionCode IS NULL;
-- Copy all User's name to Business ContactName field => where contactName is NULL in Business Table.
UPDATE `business` b
    JOIN `user` u ON b.ownerId = u.id
    SET b.contactName = u.name WHERE b.contactName IS NULL;


-- Display business settings where KEY is Same but Value is Different for Quotation and Invoice Settings!
SELECT  b1.id, b1.businessId, b1.group, b1.key, b1.value,b2.group, b2.key, b2.value FROM `business_settings` b1 INNER JOIN business_settings b2 ON b1.businessId = b2.businessId WHERE b1.key = b2.key AND b1.group <> b2.group AND b1.`type` LIKE 'bool' AND b1.value = "" AND b1.group = "invoice"
ORDER BY `b1`.`businessId` DESC;


-- UPDATE business settings where KEY is Same but Value is Different for Quotation and Invoice Settings!
-- UPDATE Invoice Settings as per Quotation Settings
UPDATE `business_settings` b1 INNER JOIN business_settings b2 ON b1.businessId = b2.businessId
SET b1.value = b2.value WHERE b1.key = b2.key AND b1.group <> b2.group AND b1.`type` LIKE 'bool' AND b1.value <> b2.value AND b1.group = "invoice"