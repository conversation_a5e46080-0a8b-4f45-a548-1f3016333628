-- Just for testing purpose queries - not required to run on the PROD until it is necessary

-- Query to identify Active Subscription Records which are marked with Incomplete due to WEBHOOK Failed Requests!
select id, planId from user where id in (SELECT sl1.userId  FROM `subscription_log` sl1 inner join subscription_log sl2 on sl1.providerSubscriptionId = sl2.providerSubscriptionId WHERE sl1.status = "active" and sl2.status = "incomplete" AND sl2.id > sl1.id);

select u.id, u.planId, sl1.planId as newPlanId from user u inner join subscription_log sl1 on sl1.userId = u.id inner join subscription_log sl2 on sl1.providerSubscriptionId = sl2.providerSubscriptionId WHERE sl1.status = "active" and sl2.status = "incomplete" AND sl2.id > sl1.id;


select s.id, s.userId, s.status, sl1.status as newStatus from subscription s inner join subscription_log sl1 on s.providerSubscriptionId = sl1.providerSubscriptionId inner join subscription_log sl2 on sl1.providerSubscriptionId = sl2.providerSubscriptionId WHERE sl1.status = "active" and sl2.status = "incomplete" AND sl2.id > sl1.id group BY s.id;



UPDATE user u inner join subscription_log sl1 on sl1.userId = u.id inner join subscription_log sl2 on sl1.providerSubscriptionId = sl2.providerSubscriptionId SET u.planId = sl1.planId WHERE sl1.status = "active" and sl2.status = "incomplete" AND sl2.id > sl1.id;


UPDATE subscription s inner join subscription_log sl1 on s.providerSubscriptionId = sl1.providerSubscriptionId inner join subscription_log sl2 on sl1.providerSubscriptionId = sl2.providerSubscriptionId SET s.status = sl1.status WHERE sl1.status = "active" and sl2.status = "incomplete" AND sl2.id > sl1.id group BY s.id;

-- Check users who has active subscription in Subscription table but PlanID is NULL
SELECT u.id, u.planId, s.id, s.provider, s.providerPlanId, s.providerSubscriptionId, s.isActive, s.status, s.createdAt from user u INNER JOIN subscription s ON s.userId = u.id WHERE s.isActive = 1 AND u.planId is NULL;


-- Issue with subscription data :
SELECT u.id, u.planId, s.planId, s.id, s.provider, s.providerPlanId, s.providerSubscriptionId, s.isActive, s.status, s.createdAt from subscription s INNER JOIN  user u ON s.userId = u.id WHERE u.planId is not null and s.isActive = 0 AND s.status = "active";

UPDATE subscription s INNER JOIN  user u ON s.userId = u.id SET s.isActive = 1 WHERE u.planId is not null and s.isActive = 0 AND s.status = "active";