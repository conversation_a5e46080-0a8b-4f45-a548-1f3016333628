[{"key": "transpotationCharge", "label": "Transpotation Charge", "type": "json", "value": "{\"amount\":55555,\"taxAmount\":250}", "subType": "currencyTax", "taxPercentage": "18", "operation": "plus", "group": "addition"}, {"key": "installationCharge", "label": "Installation Charge", "type": "json", "value": "{\"amount\":6000,\"taxAmount\":50}", "subType": "currencyTax", "taxPercentage": "18", "operation": "plus", "group": "addition"}, {"key": "waveOffCharges", "label": "Wave-off charges", "type": "json", "value": "{\"amount\":600}", "subType": "currencyTax", "taxPercentage": "0", "operation": "minus", "group": "subtraction"}, {"key": "additionalDiscount", "label": "Additional Discount", "type": "json", "value": "{\"amount\":5000}", "subType": "currencyTax", "taxPercentage": "0", "operation": "minus", "group": "info"}, {"key": "subject", "label": "Subject", "type": "string", "maxLength": 20, "value": null, "group": "info"}, {"key": "displayProductPrintName", "label": "Display Product Print Name", "type": "bool", "subType": "tick<PERSON><PERSON><PERSON>", "value": "true", "group": "settings"}]