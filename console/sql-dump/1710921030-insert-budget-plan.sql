INSERT INTO `subscription_plan` (`id`, `type`, `applePlanId`, `googlePlanId`, `googlePlanBaseId`, `razorpayPlanId`, `stripePlanId`, `slug`, `name`, `features`, `description`, `settings`, `interval`, `period`, `originalPrice`, `price`, `currency`, `isRecurring`, `group`, `isDisplayAppSide`, `isActive`, `order`, `createdAt`, `updatedAt`) VALUES (NULL, 'base', 'deliverynote.plan.yearly.one', NULL, NULL, '-', NULL, 'DeliveryNotePlanOnetime', 'Delivery Notes Plan', 'budget', '{\r\n \"subTitle\": \"Create unlimited Delivery Notes\",\r\n \"lines\": [\r\n \"Unlimited Delivery Notes\",\r\n \"Cloud Backup\",\r\n \"Sync Data\",\r\n \"Ads Free\",\r\n \"Email & Phone Support\"\r\n ]\r\n}', NULL, '1', 'yearly', '699', '599', 'INR', '0', NULL, NULL, '1', '4', NOW(), NOW());

INSERT INTO `subscription_plan` (`id`, `type`, `applePlanId`, `googlePlanId`, `googlePlanBaseId`, `razorpayPlanId`, `stripePlanId`, `slug`, `name`, `features`, `description`, `settings`, `interval`, `period`, `originalPrice`, `price`, `currency`, `isRecurring`, `group`, `isDisplayAppSide`, `isActive`, `order`, `createdAt`, `updatedAt`) VALUES (NULL, 'base', 'budget.plan.yearly.one', NULL, NULL, '-', NULL, 'BudgetPlanOnetime', 'Budget Plan', 'budget', '{\r\n \"subTitle\": \"Create unlimited Budget\",\r\n \"lines\": [\r\n \"Unlimited Budgets\",\r\n \"Cloud Backup\",\r\n \"Sync Data\",\r\n \"Ads Free\",\r\n \"Email & Phone Support\"\r\n ]\r\n}', NULL, '1', 'yearly', '699', '599', 'INR', '0', NULL, NULL, '1', '4', NOW(), NOW());

INSERT INTO `subscription_plan_offer` (`id`, `planId`, `offerCode`, `description`, `isActive`, `createdAt`, `updatedAt`) VALUES (NULL, '12', 'manual@subscription', NULL, '1', NOW(), NOW());

INSERT INTO `subscription_plan_offer` (`id`, `planId`, `offerCode`, `description`, `isActive`, `createdAt`, `updatedAt`) VALUES (NULL, '13', 'manual@subscription', NULL, '1', NOW(), NOW());
