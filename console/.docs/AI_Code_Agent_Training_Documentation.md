# AI Code Agent Training Documentation for Quotation Pro App

## Project Overview
The **Quotation Pro App** is a comprehensive business management application designed to facilitate the creation and management of quotations, invoices, budgets, and purchase orders. It supports a multi-tenant design, allowing multiple businesses to use the same instance while maintaining data isolation.

### Key Functionalities
- **Quotations:** Create, manage, and send quotations to clients.
- **Invoices:** Generate and send invoices to clients.
- **Budgets:** Plan and manage budgets for projects.
- **Purchase Orders:** Manage purchase orders for suppliers.
- **Proforma Invoice Handling:** Handle proforma invoices.
- **Subscription Plans:** Manage subscription plans.
- **WhatsApp Links:** in notification emails with userId as support pin.
- **Firebase Integration:** For real-time data synchronization.
- **PDF Generation:** For generating PDF documents from quotations and invoices.
- **Email Notifications:** Send email notifications to users.
- **Multi-Tenant Design:** Support multiple businesses on a single instance.

### Underlying Framework and Integrations
- **Framework:** Yii2
- **Integrations:**
  - **Stripe:** For payment processing.
  - **WhatsApp:** Links in emails with userId as support pin.
  - **Firebase:** For real-time data synchronization.
  - **PDF Generation:** For generating PDF documents from quotations and invoices.

## Project Architecture and Directory Focus

### .gitignore Pre-Processing
- **Initial Check:** At the beginning of each update, re-read the project’s `.gitignore` file(s) and ignore all files and directories listed there.
- **Selective Focus:** This ensures that you concentrate solely on files vital to core development and business logic.

### Directory Focus Prioritization
#### High Priority Focus
- **`api/` Directory:**  
  Deeply analyze the REST API implementation—configurations, modules, controllers, and models.
- **`common/` Directory:**  
  Focus on shared components, configurations, base models, traits, and other reusable logic.
- **`console/` Directory:**  
  Examine migration scripts, console command controllers, and other essential command-line utilities.
- **`templates/` Directory:**  
  Process document templates, PDF layouts, and associated styles and asset files.


## Guided Update Process

### Step-by-Step Processing
1. **Identify and Process:**  
   Start with new or modified files from the prioritized directories.
2. **Context Consolidation:**  
   Merge the newly processed information with the existing memory context.
3. **Ensure Consistency:**  
   Confirm that all commit messages, code generation, or debugging requests reference the most current, consolidated context.

### Clarification and Conflict Resolution
If discrepancies arise between stored context and recent updates, request targeted clarifications rather than re-reading previously ingested files.

## Continuous and Dynamic Context Utilization

### For All Tasks
- **Code Generation & Debugging:**  
  Use the continuously updated memory context when generating code, troubleshooting issues, or drafting commit messages.
- **Context-Aware Commits:**  
  Commit messages should reference the complete, current project context, incorporating the latest changes from the high-priority directories.
- **Avoid Redundancies:**  
  Reprocess files only if modifications necessitate further analysis or clarification.

## Database Structure Understanding

### Migrations
- **Location:** `/console/migrations`
- **Purpose:**  
  Manage database schema evolution, including the definition of primary keys, foreign keys, indexes, and constraints.

### Base Models
- **Location:** `/common/models/base`
- **Purpose:**  
  Represent database tables through Gii-generated models. These files define field mappings, relationships, validations, and default behaviors.

### Business Settings Logic
- **Key Files/Classes:**
  - `business.php`
  - `BusinessSettings` class
  - `DefaultBusinessSettings` class
- **Purpose:**  
  Handle dynamic business configurations, allowing for flexible adjustments of business rules and settings without modifying the code. These settings integrate with data operations across the application.

### User Model & Other Key Models
- **Focus:**
  - **User Model:** Understand how user data is managed, validated, and related to other entities.
  - **Other Models:** Capture how various models enforce validations, establish relationships (HAS_ONE, HAS_MANY, etc.), and implement custom business logic.

## Document Number Generation
- **Auto-Increment in DB:**  
  The database only handles auto-increment for primary keys.
- **Application-Managed Increments:**  
  Document numbers for quotations and invoices are managed in code. Inspect `Quotation.php` and `Invoice.php` in `/common/models` for the implementation logic.
- **Tracking in Business Table:**  
  The Business table maintains fields like `quotationNumber` and `invoiceNumber` to track the next available document numbers.

## Outstanding Questions and Their Answers

### Document Number Increments
- **Question:** Are there database triggers handling document number increments?
- **Answer:**  
  No. Only primary keys have auto-increment support. The document numbers (for quotations, invoices, etc.) are managed by the application code.

### JSON Settings Fields Indexing
- **Question:** How are JSON settings fields indexed for query performance?
- **Answer:**  
  JSON settings are rarely used. The system typically uses structured configurations through `BusinessSettings` with fields such as `group`, `key`, and `value`.

### Retention Policy for Soft-Deleted Records
- **Question:** What is the retention policy for soft-deleted records (using `isDeleted` flags)?
- **Answer:**  
  Most tables implement a soft-deletion mechanism via an `isDeleted` flag, marking records as deleted without physically removing them from the database.

## Iterative Memory Consolidation Process
- **Processing Strategy:**
  - Start with the prioritized directories (`api`, `common`, `console`, `templates`).
  - Ignore files and directories specified in `.gitignore`.
  - Process files in batches; if token limits are reached, use checkpoints and resume later.
- **Context Integration:**
  - Merge each batch’s new insights with the existing memory context.
  - Avoid reprocessing already ingested files unless modifications are detected.
- **Outcome:**  
  Maintain a comprehensive, up-to-date memory context for all future tasks, from generating code and commit messages to debugging and feature enhancements.

## Using This Documentation for Future Training
- **Resource for New AI Code Agents:**  
  This Markdown file serves as a foundational resource for training future AI Code Agents on the Quotation Pro App.
- **Review Requirement:**  
  New agents should begin by thoroughly reviewing this document to understand the application architecture, database design, key business logic, and best practices for context updating.
- **Application:**  
  Use the documented insights in all subsequent tasks, ensuring that every generated commit message, code snippet, or debugging suggestion is rooted in the complete and current understanding of the project.

## Detailed Directory Structure and Key Files

### Extended Directory Structure
- **`api/web/index.php`** - Entry point for the REST API.
- **`console/yii`** - Console commands entry point.
- **`console/migrations/`** - Contains all database migration scripts.
- **`common/models/`** - Hosts shared model classes.
- **`common/models/base/`** - Contains base models representing database tables.
- **`templates/`** - Holds document templates and PDF layouts.


### Key Files
- **`api/controllers/`** - REST API controllers.
- **`console/controllers/`** - Console command controllers.
- **`common/models/Quotation.php`** - Defines the quotation logic.
- **`common/models/Invoice.php`** - Defines the invoice logic.
- **`common/models/BusinessSettings.php`** - Handles business settings.
- **`common/models/User.php`** - Manages user data and relationships.
- **`common/config/main.php`** - Core configuration file.
- **`common/config/main-local.php`** - Local environment configurations.
- **`common/config/params.php`** - Global application parameters.
- **`common/config/params-local.php`** - Local application parameters.
- **`common/.docs/quotation-db-structure.sql`** - Database schema in sql format.
- **`console/migrations/`** - Contains scripts for tracking database schema changes.
- **`templates/`** - Document templates including PDF layouts.
- **`README.md`** - Provides an overview and high-level documentation of the project.

## Commit Message Best Practices
- **Context-Aware Messages:**  
  Always reference the complete, up-to-date project context when drafting commit messages.
- **Inclusion of Details:**  
  Include details about updates in high-priority areas (API, common, console, templates) and mention if any configurations from the database or business settings were affected.
- **Clarity and Precision:**  
  Ensure that each commit message clearly explains what was modified and why, referencing the corresponding sections of this documentation as needed.

---

*End of Document*