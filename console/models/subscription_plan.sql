INSERT INTO `subscription_plan` (`id`, `type`, `applePlanId`, `googlePlanId`, `googlePlanBaseId`, `stripePlanId`, `razorpayPlanId`, `slug`, `name`, `features`, `description`, `settings`, `interval`, `period`, `originalPrice`, `price`, `currency`, `isRecurring`, `group`, `isDisplayAppSide`, `isActive`, `order`) VALUES
                                                                                                                                                                                                                                                                                                                            (1, 'base', NULL, NULL, NULL, NULL, NULL, 'QuotationPlanYearly', 'Basic Plan (Quotation)', 'quotation', '{\n  \"subTitle\": \"Create unlimited Quotations\",\n  \"lines\": [\n    \"Unlimited Quotations\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 699, 599, 'INR', 1, 1, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (2, 'base', NULL, NULL, NULL, NULL, NULL, 'InvoicePlanYearly', 'PRO Plan (Invoice & Quotation)', 'quotation,invoice', '{\n  \"subTitle\": \"Create unlimited Quotations, Invoices\",\n  \"lines\": [\n    \"Unlimited Quotations\",\n    \"Unlimited Invoices\",\n    \"Convert Quotation to Invoice\",\n    \"Invoice status management\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & WhatsApp/Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 999, 899, 'INR', 1, 1, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (3, 'base', NULL, NULL, NULL, NULL, NULL, 'PlatinumPlanYearly', 'Platinum Plan', 'quotation,invoice,purchase-order,proforma-invoice', '{\n  \"subTitle\": \"Create unlimited Quotations, Invoices, Purchase Orders & Proforma Invoices\",\n  \"lines\": [\n    \"Unlimited Quotations\",\n    \"Unlimited Invoices\",\n    \"Unlimited Purchase Orders\",\n    \"Unlimited Proforma Invoices\",\n    \"Convert Quotation to Proforma Invoice\",\n    \"Convert Quotation to Invoice\",\n    \"Invoice status management\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & WhatsApp/Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 1299, 1199, 'INR', 1, 1, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (4, 'multi-user', NULL, NULL, NULL, NULL, NULL, 'MultiuserPlanYearly', 'Multi-User Plan', 'multi-user', '{\n  \"subTitle\": \"Multiple Users Support\",\n  \"sampleThumbImageUrl\": \"https://quotationmaker.app/sample/quotation-img-multi-user.png\",\n  \"sampleUrl\": \"https://quotationmaker.app/sample/Quotation-sample-multi-user-plan.pdf\",\n  \"lines\": [\n    \"Supports Multi-user login (Upto 5 Users)\",\n    \"Add Sales persons and Admin users\",\n    \"Category wise product management\",\n    \"Track Performance of each sales person by checking Sales person wise report\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Priority Support on Email & WhatsApp/Phone\"\n  ]\n}', '{\n  \"businessFields\": {},\n  \"businessSettings\": {\n    \"total_user_limit\": {\n      \"group\": \"app\",\n      \"value\": 5,\n      \"resetValue\": 1\n    },\n    \"total_product_limit\": {\n      \"group\": \"app\",\n      \"value\": 2000,\n      \"resetValue\": 2000\n    }\n  }\n}', 1, 'yearly', 6099, 5999, 'INR', 1, 2, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (5, 'addon', NULL, NULL, NULL, NULL, NULL, 'ImageYearly', 'Product Images', 'productImage', '{\n  \"subTitle\": \"Product Images Support\",\n  \"sampleThumbImageUrl\": \"https://praxinfo.quotationmaker.app/img/preview/image-thumb-sample.png\",\n  \"sampleUrl\": \"https://praxinfo.quotationmaker.app/img/preview/Quotation-sample-image.pdf\",\n  \"lines\": [\n    \"Add Product Image & Display Product Image in Quotation\",\n    \"Supports Upto 200 Products\"\n    \n  ]\n}', '{\n  \"businessFields\": {\n    \n  },\n  \"businessSettings\": {\n    \"is_product_image_enabled\": {\n      \"group\": \"app\",\n      \"value\": 1,\n      \"resetValue\": 0\n    },\n    \"total_product_limit\": {\n      \"group\": \"app\",\n      \"value\": 200,\n      \"resetValue\": 2000\n    }\n  }\n}', 1, 'yearly', 2099, 1999, 'INR', 1, 3, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (6, 'base', NULL, NULL, NULL, NULL, NULL, 'QuotationPlanOnetime', 'Quotation Plan', 'quotation', '{\n  \"subTitle\": \"Create unlimited Quotations\",\n  \"lines\": [\n    \"Unlimited Quotations\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 699, 599, 'INR', 0, NULL, NULL, 1, 4),
                                                                                                                                                                                                                                                                                                                            (7, 'base', NULL, NULL, NULL, NULL, NULL, 'InvoicePlanOnetime', 'Invoice Plan', 'invoice', '{\n  \"subTitle\": \"Create unlimited Invoices\",\n  \"lines\": [\n    \"Unlimited Invoices\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 699, 599, 'INR', 0, NULL, NULL, 1, 4),
                                                                                                                                                                                                                                                                                                                            (8, 'base', NULL, NULL, NULL, NULL, NULL, 'PurchaseOrderPlanOnetime', 'Purchase Order Plan', 'purchase-order', '{\n  \"subTitle\": \"Create unlimited Purchase Orders\",\n  \"lines\": [\n    \"Unlimited Purchase Orders\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 699, 599, 'INR', 0, NULL, NULL, 1, 4),
                                                                                                                                                                                                                                                                                                                            (9, 'base', NULL, NULL, NULL, NULL, NULL, 'ProformaInvoicePlanOnetime', 'Proforma Invoice Plan', 'proforma-invoice', '{\n  \"subTitle\": \"Create unlimited Proforma Invoices\",\n  \"lines\": [\n    \"Unlimited Proforma Invoices\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Email & Phone Support\"\n  ]\n}', NULL, 1, 'yearly', 699, 599, 'INR', 0, NULL, NULL, 1, 4),
                                                                                                                                                                                                                                                                                                                            (10, 'multi-user', NULL, NULL, NULL, NULL, NULL, 'MultiuserPlanYearlyOnetime', 'Multi-User Plan', 'multi-user', '{\n  \"subTitle\": \"Multiple Users Support\",\n  \"sampleThumbImageUrl\": \"https://quotationmaker.app/sample/quotation-img-multi-user.png\",\n  \"sampleUrl\": \"https://quotationmaker.app/sample/Quotation-sample-multi-user-plan.pdf\",\n  \"lines\": [\n    \"Supports Multi-user login (Upto 5 Users)\",\n    \"Add Sales persons and Admin users\",\n    \"Category wise product management\",\n    \"Track Performance of each sales person by checking Sales person wise report\",\n    \"Cloud Backup\",\n    \"Sync Data\",\n    \"Ads Free\",\n    \"Priority Support on Email & WhatsApp/Phone\"\n  ]\n}', '{\n  \"businessFields\": {},\n  \"businessSettings\": {\n    \"total_user_limit\": {\n      \"group\": \"app\",\n      \"value\": 5,\n      \"resetValue\": 1\n    },\n    \"total_product_limit\": {\n      \"group\": \"app\",\n      \"value\": 2000,\n      \"resetValue\": 2000\n    }\n  }\n}', 1, 'yearly', 6099, 5999, 'INR', 0, NULL, NULL, 1, 0),
                                                                                                                                                                                                                                                                                                                            (11, 'addon', NULL, NULL, NULL, NULL, NULL, 'ImageYearlyOnetime', 'Product Images', 'productImage', '{\n  \"subTitle\": \"Product Images Support\",\n  \"sampleThumbImageUrl\": \"https://praxinfo.quotationmaker.app/img/preview/image-thumb-sample.png\",\n  \"sampleUrl\": \"https://praxinfo.quotationmaker.app/img/preview/Quotation-sample-image.pdf\",\n  \"lines\": [\n    \"Add Product Image & Display Product Image in Quotation\",\n    \"Supports Upto 200 Products\"\n    \n  ]\n}', '{\n  \"businessFields\": {\n    \n  },\n  \"businessSettings\": {\n    \"is_product_image_enabled\": {\n      \"group\": \"app\",\n      \"value\": 1,\n      \"resetValue\": 0\n    },\n    \"total_product_limit\": {\n      \"group\": \"app\",\n      \"value\": 200,\n      \"resetValue\": 2000\n    }\n  }\n}', 1, 'yearly', 2099, 1999, 'INR', 0, NULL, NULL, 1, 0);

INSERT INTO `user` (`id`, `email`, `fromEmail`, `name`, `phoneNumber`, `addressLine1`, `addressLine2`, `addressLine3`, `latitude`, `longitude`, `profilePic`, `designation`, `freeUsageCount`, `quotationCount`, `invFreeUsageCount`, `invoiceCount`, `planId`, `country`, `regionCode`, `state`, `city`, `pincode`, `parentId`, `accessToken`, `otpCode`, `otpExpiry`, `auth_key`, `password_hash`, `password_reset_token`, `stripeCustomerId`, `razorpayCustomerId`, `status`, `isMigrated`, `migratedAt`, `isAdmin`, `isPhoneVerified`, `isContactsAllowed`, `isDeleted`, `createdAt`, `updatedAt`, `deletedAt`) VALUES
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        (3, '<EMAIL>', NULL, 'Sugam Poojary', '+917090076624', NULL, NULL, NULL, NULL, NULL, NULL, 'sales_person', 10, NULL, 5, NULL, NULL, '-', 'IN', '-', '-', NULL, NULL, 'oya2WofdIH4hMKh3tcUH3YpgkGqxyb-x', 746445, '2024-03-07 12:02:48', NULL, '$2y$13$d6J3FgpQ6Hvn22FL/U237OEYP.FoZYPB7D57nOF1K5n/trHPKPpSC', NULL, NULL, NULL, 'active', NULL, NULL, 1, 1, 0, 0, '2024-03-07 11:52:48', '2024-04-03 11:02:44', NULL),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        (4, '<EMAIL>', NULL, 'Umang Kathiyara', '+919377168718', NULL, NULL, NULL, NULL, NULL, NULL, 'sales_person', 10, NULL, 5, NULL, NULL, '-', 'IN', '-', '-', NULL, NULL, 'dXuSqqhrFnXJdQLuTqsM9vymo_kLJ96B', 746445, '2024-03-07 12:02:48', NULL, '$2y$13$Rh9xRgul0p27JhfRHn/EBeCy6C8eC7zhgwDmK29kL1/mjIl4zh.NG', NULL, NULL, NULL, 'active', NULL, NULL, 1, 1, 0, 0, '2024-03-19 07:12:19', '2024-04-02 07:50:25', NULL),
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        (5, '<EMAIL>', NULL, 'SKT', '+919845027058', 'APIT', '', NULL, NULL, NULL, NULL, 'sales_person', 10, NULL, 5, NULL, NULL, '-', 'IN', '-', '-', NULL, NULL, NULL, NULL, '2024-03-29 05:41:28', NULL, '$2y$13$qrfOkAyJDLcfGmRi58ZZSO32y80o0kkFTSWhkFE3UF4RXa2eKILaq', NULL, NULL, NULL, 'active', NULL, NULL, 1, 0, 0, 0, '2024-03-29 05:41:28', '2024-03-29 06:22:00', NULL);

