<?php

namespace console\controllers;

use Yii;
use yii\helpers\Console;

/**
 * Controller for managing cache operations from the console.
 */
class MyCacheController extends BaseConsoleController
{
    /**
     * @var string The application ID to use for cache operations
     */
    public $appId = 'api';

    /**
     * @var string The cache directory path
     */
    private $_cachePath;

    /**
     * @inheritdoc
     */
    public function options($actionID)
    {
        return array_merge(parent::options($actionID), [
            'appId',
        ]);
    }

    /**
     * @inheritdoc
     */
    public function optionAliases()
    {
        return array_merge(parent::optionAliases(), [
            'a' => 'appId',
        ]);
    }

    /**
     * @inheritdoc
     */
    public function init()
    {
        parent::init();

        // Set the cache path based on the application ID
        $this->_cachePath = Yii::getAlias("@{$this->appId}/runtime/cache");

        if (!is_dir($this->_cachePath)) {
            $this->stderr("Cache directory does not exist: {$this->_cachePath}\n", Console::FG_RED);
            $this->stderr("Make sure the application ID is correct and the cache directory exists.\n", Console::FG_RED);
            Yii::$app->end(1);
        }
        Yii::$app->cache->cachePath = $this->_cachePath;
    }

    /**
     * Get the cache path
     *
     * @return string The cache path
     */
    protected function getCachePath()
    {
        return $this->_cachePath;
    }

    /**
     * Get a value from the cache
     *
     * @param string $key The cache key to retrieve
     */
    public function actionGet($key)
    {
        $value = Yii::$app->cache->get($key);

        if ($value === false) {
            $this->stdout("Cache key '{$key}' not found or has expired.\n", Console::FG_YELLOW);
        } else {
            $this->stdout("Value for cache key '{$key}':\n", Console::FG_GREEN);

            if (is_scalar($value) || is_null($value)) {
                $this->stdout(var_export($value, true) . "\n");
            } else {
                $this->stdout(print_r($value, true) . "\n");
            }
        }
    }

    /**
     * Set a value in the cache
     *
     * @param string $key The cache key to set
     * @param string $value The value to store
     * @param int $duration The duration in seconds (0 = never expire)
     */
    public function actionSet($key, $value, $duration = 0)
    {
        // Try to convert string value to appropriate type
        if (is_numeric($value)) {
            if (strpos($value, '.') !== false) {
                $value = (float)$value;
            } else {
                $value = (int)$value;
            }
        } elseif ($value === 'true') {
            $value = true;
        } elseif ($value === 'false') {
            $value = false;
        } elseif ($value === 'null') {
            $value = null;
        }

        if (Yii::$app->cache->set($key, $value, $duration)) {
            $this->stdout("Cache key '{$key}' set successfully", Console::FG_GREEN);
            if ($duration > 0) {
                $this->stdout(" with duration of {$duration} seconds", Console::FG_GREEN);
            }
            $this->stdout(".\n", Console::FG_GREEN);
        } else {
            $this->stderr("Failed to set cache key '{$key}'.\n", Console::FG_RED);
        }
    }

    /**
     * Delete a cache key
     *
     * @param string $key The cache key to delete
     */
    public function actionDelete($key)
    {
        if (Yii::$app->cache->delete($key)) {
            $this->stdout("Cache variable '{$key}' has been deleted.\n", Console::FG_GREEN);
        } else {
            $this->stderr("Failed to delete cache variable '{$key}'.\n", Console::FG_YELLOW);
        }
    }

    /**
     * Check if a cache key exists
     *
     * @param string $key The cache key to check
     */
    public function actionExists($key)
    {
        if (Yii::$app->cache->exists($key)) {
            $this->stdout("Cache key '{$key}' exists.\n", Console::FG_GREEN);
        } else {
            $this->stdout("Cache key '{$key}' does not exist or has expired.\n", Console::FG_YELLOW);
        }
    }

    /**
     * Flush all cache data
     */
    public function actionFlush()
    {
        if ($this->confirm('Are you sure you want to flush all cache data? This cannot be undone!')) {
            if (Yii::$app->cache->flush()) {
                $this->stdout("Cache flushed successfully.\n", Console::FG_GREEN);
            } else {
                $this->stderr("Failed to flush cache.\n", Console::FG_RED);
            }
        } else {
            $this->stdout("Operation cancelled.\n", Console::FG_YELLOW);
        }
    }

    /**
     * List cache keys and their values
     *
     * This is a simplified version that works with FileCache by scanning the cache directory
     */
    public function actionList()
    {
        $cache = Yii::$app->cache;
        $cacheClass = get_class($cache);

        $this->stdout("Cache Component: ", Console::FG_CYAN);
        $this->stdout($cacheClass . "\n");

        // For FileCache, we can scan the directory
        if ($cacheClass === 'yii\\caching\\FileCache') {
            $cachePath = Yii::getAlias($cache->cachePath);

            if (!is_dir($cachePath)) {
                $this->stderr("Cache directory does not exist: {$cachePath}\n", Console::FG_RED);
                return;
            }

            $this->stdout("\nScanning cache directory...\n", Console::FG_CYAN);

            $keys = [];
            $fileCount = 0;

            try {
                $dirIterator = new \RecursiveDirectoryIterator(
                    $cachePath,
                    \FilesystemIterator::SKIP_DOTS | \FilesystemIterator::FOLLOW_SYMLINKS
                );
                $iterator = new \RecursiveIteratorIterator($dirIterator);

                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getExtension() === 'bin') {
                        $fileCount++;

                        // Try to extract the key from the file
                        $content = @file_get_contents($file->getPathname());
                        if ($content !== false) {
                            try {
                                $data = @unserialize($content);
                                if (is_array($data) && isset($data[0])) {
                                    $keys[] = $file->getBasename('.bin');
                                }
                            } catch (\Exception $e) {
                                // Silently fail and continue
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                $this->stderr("Error scanning cache directory: {$e->getMessage()}\n", Console::FG_RED);
                return;
            }

            $this->stdout("Found {$fileCount} cache files.\n", Console::FG_GREEN);

            if (empty($keys)) {
                $this->stdout("No cache keys could be extracted.\n", Console::FG_YELLOW);
                return;
            }

            // Sort keys for better readability
            sort($keys);

            $this->stdout("\nCache Keys:\n", Console::FG_CYAN);
            foreach ($keys as $index => $key) {
                $this->stdout(($index + 1) . ". ", Console::FG_CYAN);
                try {
                    $this->stdout($key);

                    // Try to get the value
                    $value = $cache->get($key);
                    if ($value !== false) {
                        $this->stdout(" = ", Console::FG_YELLOW);

                        if (is_scalar($value) || is_null($value)) {
                            $this->stdout(var_export($value, true));
                        } elseif (is_array($value)) {
                            $this->stdout("array(" . count($value) . " elements)");

                            // Optionally show array contents for small arrays
                            if (count($value) <= 5) {
                                $this->stdout(": " . var_export($value, true));
                            }
                        } elseif (is_object($value)) {
                            $this->stdout("object(" . get_class($value) . ")");
                        } else {
                            $this->stdout(gettype($value));
                        }
                    }

                    $this->stdout("\n");
                } catch (\Exception $e) {
                    $this->stderr("Error retrieving value for key '{$key}': {$e->getMessage()}\n", Console::FG_RED);
                }
            }
        } else {
            $this->stdout("\nListing cache keys is only supported for FileCache.\n", Console::FG_YELLOW);
        }
    }
}
