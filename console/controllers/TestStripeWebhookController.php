<?php

namespace console\controllers;

use common\helpers\StripeHelper;
use common\models\enum\StripeAccount;
use Stripe\Invoice;
use Stripe\Stripe;
use Stripe\Subscription;
use yii\console\Controller;
use yii\helpers\Json;

/**
 * Test controller for Stripe webhook events
 */
class TestStripeWebhookController extends Controller
{
    /**
     * Test processing an invoice.paid event
     * 
     * @param string $subscriptionId The Stripe subscription ID to test with
     * @return int Exit code
     */
    public function actionInvoicePaid($subscriptionId)
    {
        try {
            // Initialize Stripe helper
            $stripeHelper = StripeHelper::sharedInstance(StripeAccount::PRAXINFO);
            
            // Retrieve the subscription from Stripe
            $subscription = Subscription::retrieve($subscriptionId);
            if (!$subscription) {
                $this->stderr("Could not find subscription with ID: $subscriptionId\n");
                return 1;
            }
            
            // Get the latest invoice
            $invoiceId = $subscription->latest_invoice;
            if (!$invoiceId) {
                $this->stderr("No invoice found for subscription: $subscriptionId\n");
                return 1;
            }
            
            $invoice = Invoice::retrieve($invoiceId);
            if (!$invoice) {
                $this->stderr("Could not retrieve invoice: $invoiceId\n");
                return 1;
            }
            
            // Create a mock event
            $eventData = [
                'id' => 'evt_test_' . time(),
                'object' => 'event',
                'api_version' => Stripe::getApiVersion(),
                'created' => time(),
                'data' => [
                    'object' => $invoice->toArray()
                ],
                'livemode' => false,
                'pending_webhooks' => 0,
                'request' => [
                    'id' => null,
                    'idempotency_key' => null
                ],
                'type' => 'invoice.paid'
            ];
            
            // Convert to JSON
            $payload = Json::encode($eventData);
            
            // Process the webhook
            $this->stdout("Processing mock invoice.paid event for subscription: $subscriptionId\n");
            $result = $stripeHelper->handleWebhook($payload);
            
            if ($result) {
                $this->stdout("Successfully processed invoice.paid event\n");
                return 0;
            } else {
                $this->stderr("Failed to process invoice.paid event\n");
                return 1;
            }
        } catch (\Exception $e) {
            $this->stderr("Error: " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n");
            return 1;
        }
    }
}
