<?php

namespace console\controllers;

use Carbon\Carbon;
use common\helpers\StripeHelper;
use common\models\enum\SubscriptionStatus;
use common\models\Subscription;

class CronJobController extends BaseConsoleController
{

    public function actionTest()
    {
        // Response params - start
        $response = [];
        $message = "Test from CronJobController!";
        return $this->_sendResponse($response, $message);
        // Response params - end
        // to send logical errors - status code will be always 200 but errorCode will be provided at last
//        return $this->_sendErrorResponse(200, "No input parameters are given", 101);
    }


    public function actionCheckSubscriptions()
    {
        $response = [];
        $currentTimestamp = Carbon::now()->timestamp;
        $incompleteTransactions = StripeHelper::removeIncompleteTransactions();
        $response['incompleteTransactions'] = $incompleteTransactions;
        $expiredSubscriptions = Subscription::find()->where(['<', 'endDate', $currentTimestamp])->andWhere(['isActive' => 1])->all();
        foreach ($expiredSubscriptions as $subscription) {
            $subscription->deactivate(SubscriptionStatus::EXPIRED); // de-activate subscription
        }
        $totalExpiredSubscriptions = count($expiredSubscriptions);
        if ($totalExpiredSubscriptions > 0) {
            $response = [
                'totalExpiredSubscriptions' => $totalExpiredSubscriptions
            ];
            logMail("CRON-JOB: Expired Subscriptions", $response);
        }
        $message = "check subscription command run successfully!";
        return $this->_sendResponse($response, $message);
    }


}