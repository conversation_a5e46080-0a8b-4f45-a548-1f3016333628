<?php

namespace console\controllers;

use Yii;
use yii\base\BaseObject;
use yii\db\Exception;
use yii\db\Migration;

class DataImportController extends BaseConsoleController
{

    public function actionTest()
    {
        // Response params - start
        $response = [];
        $message = "Test from DataImportController!";
        return $this->_sendResponse($response, $message);
        // Response params - end
        // to send logical errors - status code will be always 200 but errorCode will be provided at last
        return $this->_sendErrorResponse(200, "No input parameters are given", 101);
    }

    public function actionDumpSql($file="dump.sql")
    {
        $sqlFile = Yii::getAlias('@console/models/' . $file);
        $dbMigration = new Migration();
        $dbMigration->maxSqlOutputLength = 80 * 15;
        $dbMigration->execute(file_get_contents($sqlFile));
        echo "Sql User data Imported successfully!";
        $this->actionImportPoiData();
    }
}