<?php

namespace console\controllers;

use common\helpers\StripeHelper;
use common\models\AppSettings;
use common\models\Business;
use common\models\BusinessSettings;
use common\models\enum\CountryDialPrefix;
use common\models\enum\DefaultBusinessSettings;
use common\models\enum\Key;
use common\models\enum\StripeAccount;
use common\models\Quotation;
use common\models\Region;
use common\models\StripeUser;
use common\models\Subscription;
use common\models\User;
use Stripe\Exception\ApiErrorException;
use yii\base\ErrorException;
use yii\base\Exception;

class BatchUpdateController extends BaseConsoleController
{

    public function actionTest()
    {
        // Response params - start
        $response = [];
        $message = "Test from BatchUpdateController!";
        return $this->_sendResponse($response, $message);
        // Response params - end
        // to send logical errors - status code will be always 200 but errorCode will be provided at last
        return $this->_sendErrorResponse(200, "No input parameters are given", 101);
    }


    public function actionSyncAppSettings()
    {
        // Response params - start
        $response = [];
        echo "\nUpdating App Settings!";

        AppSettings::syncSettings();

        echo "\nDone!";
    }


    public function actionGenerateQuotationCount()
    {
        $i = 0;
        $users = User::find()->where(['quotationCount' => null])->orderBy('id');
        /** @var User $user */
        foreach ($users->each() as $user) {
            $user->quotationCount = $user->getTotalQuotations();
            $user->save(false);
            $i++;
        }
        echo "\nTotal Users Processed :: $i";
    }

    /**
     * @throws \JsonException
     */
    public function actionProcessSubscriptionsForPaymentMethod()
    {
        $i = 0;
        $errorCount = 0;
        $response = [];
        $message = "ProcessSubscriptionsForPaymentMethod - Done!";
        $errMessages = "";
        $lastProcessedId = \Yii::$app->cache->get('lastProcessedId');
        if (empty($lastProcessedId)){
            $lastProcessedId = 0;
        }
        echo "\nLast Processed ID : $lastProcessedId\n";
        $subscriptions = Subscription::find()->where(['not in', 'status', ['incomplete', '']])
            ->andWhere(['provider'=>'stripe'])->andWhere(['>','id',$lastProcessedId])
            ->orderBy('id');
        /** @var Subscription $subscription */
        foreach ($subscriptions->each() as $subscription) {
            $stripeSubscriptionId = $subscription->providerSubscriptionId;
            $stripeAccount = $subscription->providerAccount;
            $stripeApi = StripeHelper::sharedInstance($stripeAccount);
            try {
                $success = $stripeApi->setPaymentMethodBySubscriptionId($stripeSubscriptionId);
                if (!$success)
                {
                    $errorCount++;
                }
            }catch (\Exception $e){
                $errorCount++;
                $data = [
                    'StripeSubscription'=>$subscription->providerSubscriptionId,
                    'StripeAccount'=>$subscription->providerAccount,
                    'subscriptionId'=>$subscription->id,
                    'userId'=>$subscription->userId,
                    'businessId'=>$subscription->businessId,
                    'errMessage' => $e->getMessage(),
                ];
                $message = "\n\n ======================== Error ========== \n\n" . print_r($data, true) ;
                $errMessages .= "\n" . $message;
            }
            $i++;
            \Yii::$app->cache->set('lastProcessedId', $subscription->id);
        }
        $response['totalCount'] = $i;
        $response['errorCount'] = $errorCount;
        $response['errorMessage'] = $errorCount;
        $errMessages = "Total Errors :: {$errorCount} \n\n" . $errMessages;
        logMail("ProcessSubscriptionsForPaymentMethod process completed!", $response,$errMessages);
        return $this->_sendResponse($response, $message);
    }


    public function actionGenerateDialPrefix()
    {
        $dialCodes = CountryDialPrefix::getArray();
        $regions = Region::find()->where(['dialPrefix' => null])->orderBy('code');
        /** @var Region $region */
        foreach ($regions->each() as $region) {
            $region->dialPrefix = $dialCodes[strtolower($region->code)] ?? null;
            echo "\n {$region->code} => {$region->dialPrefix}";
            if ($region->dialPrefix) {
                $region->dialPrefix = "+" . $region->dialPrefix;
            }
            $region->save(false);
        }
        echo "\nRegion data processed!";
    }

    /**
     * @throws \JsonException
     */
    public function actionProcessMissingStripeCustomers()
    {
        $i = 0;
        $errorCount = 0;
        $response = [];
        $message = "ProcessMissingStripeCustomers - Done!";
        $errMessages = "";
        $lastProcessedId = \Yii::$app->cache->get('lastProcessedId');
        if (empty($lastProcessedId)){
            $lastProcessedId = 0;
        }
        echo "\nLast Processed ID : $lastProcessedId\n";
        $subscriptions = Subscription::find()->where(['isActive'=>1])
            ->andWhere(['provider'=>'stripe'])->andWhere(['providerCustomerId'=>null])
            ->orderBy('id');
        /** @var Subscription $subscription */
        foreach ($subscriptions->each() as $subscription) {
            $stripeSubscriptionId = $subscription->providerSubscriptionId;
            $stripeAccount = $subscription->providerAccount;
            if (empty($subscription->providerAccount)){
                $stripeAccount = StripeAccount::PRAXINFO_SOLUTIONS;
            }
            $stripeCustomerId = null;
            try {
                $stripeApi = StripeHelper::sharedInstance($stripeAccount);
                $stripeCustomerId = $stripeApi->getCustomerFromStripeSubscriptionId($stripeSubscriptionId);
            } catch (ApiErrorException|Exception $e) {
                if ($e->getError()->code == "resource_missing")
                    try {
                        if ($stripeAccount == StripeAccount::PRAXINFO_SOLUTIONS){
                            $stripeAccount = StripeAccount::PRAXINFO;
                        }else{
                            $stripeAccount = StripeAccount::PRAXINFO_SOLUTIONS;
                        }
                        $stripeApi = StripeHelper::sharedInstance($stripeAccount);
                        $stripeCustomerId = $stripeApi->getCustomerFromStripeSubscriptionId($stripeSubscriptionId);
                    } catch (ApiErrorException|Exception $e) {
                        $errorCount++;
                        $data = [
                            'StripeSubscription'=>$subscription->providerSubscriptionId,
                            'StripeAccount'=>$subscription->providerAccount,
                            'subscriptionId'=>$subscription->id,
                            'userId'=>$subscription->userId,
                            'businessId'=>$subscription->businessId,
                            'errMessage' => $e->getMessage(),
                        ];
                        $message = "\n\n ======================== Error ========== \n\n" . print_r($data, true) ;
                        $errMessages .= "\n" . $message;
                    }
            }
            if ($stripeCustomerId){
                $subscription->providerCustomerId = $stripeCustomerId;
                $subscription->providerAccount = $stripeAccount;
                $subscription->save(false);

                $stripeUser = StripeUser::findByUserIdAndAccount($subscription->userId, $stripeAccount);
                if ($stripeUser === null) {
                    $stripeUser = new StripeUser();
                    $stripeUser->userId = $subscription->userId;
                    $stripeUser->stripeAccount = $stripeAccount;
                }
                $stripeUser->stripeCustomerId = $stripeCustomerId;
                $stripeUser->save();
            }
            $i++;
        }
        $response['totalCount'] = $i;
        $response['errorCount'] = $errorCount;
        $response['errorMessage'] = $errorCount;
        $errMessages = "Total Errors :: {$errorCount} \n\n" . $errMessages;
        logMail("ProcessMissingStripeCustomers process completed!", $response,$errMessages);
        return $this->_sendResponse($response, $message);
    }

}