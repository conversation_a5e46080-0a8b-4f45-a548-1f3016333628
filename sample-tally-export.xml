<?xml version="1.0" encoding="UTF-8"?>
<ENVELOPE>
  <HEADER>
    <TALLYREQUEST>Import Data</TALLYREQUEST>
  </HEADER>
  <BODY>
    <IMPORTDATA>
      <REQUESTDESC>
        <REPORTNAME>Vouchers</REPORTNAME>
        <STATICVARIABLES>
          <SVCURRENTCOMPANY>Your Company Name</SVCURRENTCOMPANY>
        </STATICVARIABLES>
      </REQUESTDESC>
      <REQUESTDATA>
        <TALLYMESSAGE>
          <VOUCHER REMOTEID="123" VCHTYPE="Sales" ACTION="Create">
            <DATE>20240501</DATE>
            <NARRATION>Invoice INV-001</NARRATION>
            <VOUCHERTYPENAME>Sales</VOUCHERTYPENAME>
            <REFERENCE>INV-001</REFERENCE>
            <VOUCHERNUMBER>INV-001</VOUCHERNUMBER>
            <PARTYLEDGERNAME>Customer Name</PARTYLEDGERNAME>
            <PARTYNAME>Customer Name</PARTYNAME>
            <PARTYGSTIN>GSTIN12345678</PARTYGSTIN>
            <ADDRESS.LIST>
              <ADDRESS>123 Main Street</ADDRESS>
              <ADDRESS>Apartment 4B</ADDRESS>
              <ADDRESS>City, State, ZIP</ADDRESS>
            </ADDRESS.LIST>
            
            <!-- Sales account entry -->
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>Sales Account</LEDGERNAME>
              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
              <AMOUNT>1000.00</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
            
            <!-- CGST entry -->
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>CGST</LEDGERNAME>
              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
              <AMOUNT>90.00</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
            
            <!-- SGST entry -->
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>SGST</LEDGERNAME>
              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
              <AMOUNT>90.00</AMOUNT>
            </ALLLEDGERENTRIES.LIST>
            
            <!-- Sundry Debtors entry -->
            <ALLLEDGERENTRIES.LIST>
              <LEDGERNAME>Sundry Debtors</LEDGERNAME>
              <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
              <AMOUNT>-1180.00</AMOUNT>
              <BILLALLOCATIONS.LIST>
                <NAME>INV-001</NAME>
                <BILLTYPE>New Ref</BILLTYPE>
                <AMOUNT>-1180.00</AMOUNT>
              </BILLALLOCATIONS.LIST>
            </ALLLEDGERENTRIES.LIST>
            
            <!-- Inventory entries for item details -->
            <INVENTORYENTRIES.LIST>
              <STOCKITEMNAME>Product 1</STOCKITEMNAME>
              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
              <RATE>500.00</RATE>
              <AMOUNT>-500.00</AMOUNT>
              <ACTUALQTY>1</ACTUALQTY>
              <BILLEDQTY>1</BILLEDQTY>
              <BATCHNAME>Primary Batch</BATCHNAME>
            </INVENTORYENTRIES.LIST>
            
            <INVENTORYENTRIES.LIST>
              <STOCKITEMNAME>Product 2</STOCKITEMNAME>
              <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
              <RATE>500.00</RATE>
              <AMOUNT>-500.00</AMOUNT>
              <ACTUALQTY>1</ACTUALQTY>
              <BILLEDQTY>1</BILLEDQTY>
              <BATCHNAME>Primary Batch</BATCHNAME>
            </INVENTORYENTRIES.LIST>
          </VOUCHER>
        </TALLYMESSAGE>
      </REQUESTDATA>
    </IMPORTDATA>
  </BODY>
</ENVELOPE>
