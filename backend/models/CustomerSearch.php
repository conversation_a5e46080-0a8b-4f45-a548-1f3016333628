<?php

namespace backend\models;

use common\models\Customer;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * CustomerSearch represents the model behind the search form of `common\models\Customer`.
 */
class CustomerSearch extends Customer
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'businessId' ,'otpCode', 'isDeleted'], 'integer'],
            [['email', 'name', 'phoneNumber', 'addressLine1', 'addressLine2', 'addressLine3', 'profilePic', 'companyName', 'taxNumber', 'panNumber', 'accessToken', 'otpExpiry', 'auth_key', 'password_hash', 'status', 'createdAt', 'updatedAt', 'deletedAt', 'addedBy'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        // print_r($this->addedBy);die();
        $query = Customer::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => Yii::$app->params['pagination']],
        ]);
        if (isset($_GET['Customer']['DateRange'])) {
            $date_range_arr = explode(' - ', $_GET['Customer']['DateRange']);
            $FromDate = Yii::$app->common->formatDate($date_range_arr[0], 2);
            $ToDate = Yii::$app->common->formatDate($date_range_arr[1], 2);
            $query->andFilterWhere(['>=', 'DATE_FORMAT(customer.createdAt, "%Y-%m-%d")', $FromDate])
                ->andFilterWhere(['<=', 'DATE_FORMAT(customer.createdAt, "%Y-%m-%d")', $ToDate]);
        }
        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }


        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'businessId' => $this->businessId,
            'otpCode' => $this->otpCode,
            'otpExpiry' => $this->otpExpiry,
            'addedBy' => $this->addedBy,
            'updatedAt' => $this->updatedAt,
            'deletedAt' => $this->deletedAt,
            'isDeleted' => $this->isDeleted,
        ]);

        if (!Yii::$app->session['isAdmin']) {
            $query->andFilterWhere(['isDeleted' => 0]); // show only active customers!
        }

        $query->andFilterWhere(['like', 'email', $this->email])
            ->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'phoneNumber', $this->phoneNumber])
            ->andFilterWhere(['like', 'addressLine1', $this->addressLine1])
            ->andFilterWhere(['like', 'addressLine2', $this->addressLine2])
            ->andFilterWhere(['like', 'addressLine3', $this->addressLine3])
            ->andFilterWhere(['like', 'profilePic', $this->profilePic])
            ->andFilterWhere(['like', 'companyName', $this->companyName])
            ->andFilterWhere(['like', 'taxNumber', $this->taxNumber])
            ->andFilterWhere(['like', 'panNumber', $this->panNumber])
            ->andFilterWhere(['like', 'accessToken', $this->accessToken])
            ->andFilterWhere(['like', 'auth_key', $this->auth_key])
            ->andFilterWhere(['>=', 'createdAt', $this->createdAt])
            ->andFilterWhere(['like', 'password_hash', $this->password_hash])
            ->andFilterWhere(['like', 'status', $this->status]);
        return $dataProvider;
    }
}
