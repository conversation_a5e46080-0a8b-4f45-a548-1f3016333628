<?php

namespace backend\models;

use common\models\Quotation;
use common\models\User;
use yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * QuotationSearch represents the model behind the search form of `common\models\Quotation`.
 */
class QuotationSearch extends Quotation
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'businessId', 'statusUpdatedById', 'isPurchaseOrder', 'isDeleted'], 'integer'],
            [['quotationNumber', 'termsIds', 'pdfFileName', 'status', 'statusText',
            'referenceName', 'salesPersonName',
                'statusAttachment', 'quotationDate', 'followupDate', 'createdAt', 'updatedAt', 'deletedAt', 'companyName', 'customerId', 'assignedToId'], 'safe'],
            [['totalTaxAmount', 'totalAmount', 'totalDiscountAmount'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    public function searchFollowUp($params)
    {
        $dataProvider = $this->search($params);

        $dataProvider->query->andWhere([
            'OR',
            ['IS', 'followupDate', null],
            ['=', 'followupDate', date('Y-m-d')]
        ]);
        $dataProvider->query->andFilterWhere([
            'NOT IN', 'quotation.status', ['approved', 'rejected']
        ]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Quotation::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => \Yii::$app->params['pagination']],
        ]);
        $dataProvider->sort->attributes['companyName'] = [
            'asc' => ['companyName' => SORT_ASC],
            'desc' => ['companyName' => SORT_DESC],
            'label' => 'companyName',
            'default' => SORT_ASC
        ];
        if (isset($_GET['Quotation']['DateRange'])) {
            $date_range_arr = explode(' - ', $_GET['Quotation']['DateRange']);
            $FromDate = \Yii::$app->common->formatDate($date_range_arr[0], 2);
            $ToDate = \Yii::$app->common->formatDate($date_range_arr[1], 2);
            $query->andFilterWhere(['>=', 'DATE_FORMAT(quotation.createdAt, "%Y-%m-%d")', $FromDate])
                ->andFilterWhere(['<=', 'DATE_FORMAT(quotation.createdAt, "%Y-%m-%d")', $ToDate]);
        }
        $session = Yii::$app->session;
        if (!isset($session['isAdmin'])) {
            Yii::$app->common->setLoginSessionData();
        }
        if ($session['isAdmin'] == 0) {
            $salesUser = User::findByPk($session['user_id']);
            $subUserIds = $salesUser->getSubIds();
            $query->andWhere(['in', 'assignedToId', $subUserIds]);
        }
        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'quotation.businessId' => $this->businessId,
            // 'customerId' => $this->customerId,
            // 'assignedTo' => $this->assignedTo,
            'totalTaxAmount' => $this->totalTaxAmount,
            'totalAmount' => $this->totalAmount,
            'totalDiscountAmount' => $this->totalDiscountAmount,
            'statusUpdatedById' => $this->statusUpdatedById,
            'quotationDate' => $this->quotationDate,
            'followupDate' => $this->followupDate,
            'isPurchaseOrder' => $this->isPurchaseOrder,
            'isQuotation' => $this->isQuotation,
            'isBudget' => $this->isBudget,
            'isDeleted' => $this->isDeleted,
            'quotation.createdAt' => $this->createdAt,
            'quotation.updatedAt' => $this->updatedAt,
            'quotation.deletedAt' => $this->deletedAt,
        ]);

        $query->andFilterWhere(['like', 'quotationNumber', $this->quotationNumber])
            ->andFilterWhere(['like', 'termsIds', $this->termsIds])
            ->andFilterWhere(['like', 'pdfFileName', $this->pdfFileName])
            ->andFilterWhere(['like', 'quotation.status', $this->status])
            ->andFilterWhere(['like', 'statusText', $this->statusText])
            ->andFilterWhere(['like', 'statusAttachment', $this->statusAttachment]);

        $query->andFilterWhere(['like', 'referenceName', $this->referenceName]);
        $query->andFilterWhere(['like', 'salesPersonName', $this->salesPersonName]);

        $query->joinWith(['customer' => function ($q) {
            $q->where('customer.companyName LIKE "%' . $this->companyName . '%" ');
            $q->andWhere('customer.name LIKE "%' . $this->customerId . '%"');
        }]);
        $query->joinWith(['assignedTo' => function ($q) {
            $q->Where('user.name LIKE "%' . $this->assignedToId . '%" ');
        }]);

        return $dataProvider;
    }
}
