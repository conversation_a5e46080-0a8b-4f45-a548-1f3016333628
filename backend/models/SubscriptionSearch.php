<?php

namespace backend\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Subscription;

/**
 * SubscriptionSearch represents the model behind the search form of `common\models\Subscription`.
 */
class SubscriptionSearch extends Subscription
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'userId', 'planId', 'startedFromDate', 'startDate', 'endDate', 'isActive'], 'integer'],
            [['providerPlanId', 'provider', 'providerSubscriptionId', 'lastTransactionId', 'status', 'createdAt', 'updatedAt'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Subscription::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'userId' => $this->userId,
            'planId' => $this->planId,
            'startedFromDate' => $this->startedFromDate,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'isActive' => $this->isActive,
            'updatedAt' => $this->updatedAt,
        ]);

        $query->andFilterWhere(['like', 'providerPlanId', $this->providerPlanId])
            ->andFilterWhere(['like', 'provider', $this->provider])
            ->andFilterWhere(['like', 'providerSubscriptionId', $this->providerSubscriptionId])
            ->andFilterWhere(['>=', 'startedFromDate', $this->startedFromDate])
            ->andFilterWhere(['like', 'lastTransactionId', $this->lastTransactionId])
            ->andFilterWhere(['like', 'status', $this->status]);

        return $dataProvider;
    }
}
