<?php

namespace backend\models;

use common\models\NotificationMessage;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * NotificationMessageSearch represents the model behind the search form of `common\models\NotificationMessage`.
 */
class NotificationMessageSearch extends NotificationMessage
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'fromUserId', 'toUserId'], 'integer'],
            [['fromUserType', 'toUserType', 'topic', 'message', 'type', 'jsonData', 'createdAt', 'updatedAt'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = NotificationMessage::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'fromUserId' => $this->fromUserId,
            'toUserId' => $this->toUserId,
            'createdAt' => $this->createdAt,
            'updatedAt' => $this->updatedAt,
        ]);

        $query->andFilterWhere(['like', 'fromUserType', $this->fromUserType])
            ->andFilterWhere(['like', 'toUserType', $this->toUserType])
            ->andFilterWhere(['like', 'topic', $this->topic])
            ->andFilterWhere(['like', 'message', $this->message])
            ->andFilterWhere(['like', 'type', $this->type])
            ->andFilterWhere(['like', 'jsonData', $this->jsonData]);

        return $dataProvider;
    }
}
