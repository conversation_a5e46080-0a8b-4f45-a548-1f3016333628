<?php

use common\models\Business;
use common\models\Customer;
use common\models\enum\ReceiptStatus;
use common\models\Receipt;
use common\models\TermsCondition;
use common\models\User;
use kartik\date\DatePicker;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $model Receipt */
/* @var $searchModel backend\models\ReceiptSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Receipts';
$this->params['breadcrumbs'][] = $this->title;
$userList = ArrayHelper::map(User::find()->select(["id", "name As fullName"])->where('1=0')->orderBy('fullName ASC')->asArray()->all(), 'id', 'fullName');
$customerList = ArrayHelper::map(Customer::find()->select(["id", "name As fullName"])->where('1=0')->orderBy('fullName ASC')->asArray()->all(), 'id', 'fullName');
$businessList = ArrayHelper::map(Business::find()->select(["id", "name"])->where('1=0')->orderBy('name ASC')->asArray()->all(), 'id', 'name');
$termList = ArrayHelper::map(TermsCondition::find()->select(["id", "text"])->where('1=0')->orderBy('text ASC')->asArray()->all(), 'id', 'name');
$date =  null;
$isAdmin = Yii::$app->session['isAdmin'];
?>
<div class="receipt-index">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <?php $form = ActiveForm::begin(['method' => 'get']); ?>
                            <div class="row md-form">
                                <div class="col-md-4 col-lg-4 col-sm-4">
                                    <?= $form->field($searchModel, 'DateRange')->textInput(['class' => 'form-control', 'tabindex' => '1', 'autocomplete' => "off", 'id' => 'daterange', 'value' => $date])->label('Date Range'); ?>
                                </div>
                            </div>
                            <?php ActiveForm::end(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary">
                            <h4 class="card-title text-center text-uppercase"
                                style="font-weight: 700"><?= Html::encode($this->title) ?></h4>
                            <p class="card-category"></p>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <?php Pjax::begin();
                                echo GridView::widget([
                                    'dataProvider' => $dataProvider,
                                    'filterModel' => $searchModel,
                                    'columns' => [
                                        // ['class' => 'yii\grid\SerialColumn'],

                                        [
                                            'class' => 'yii\grid\SerialColumn',
                                        ],
                                        'receiptNumber',
                                        [
                                            'attribute' => 'isItemizedReceipt',
                                            'label' => 'Type',
                                            //'filter' => Html::activeDropDownList($searchModel, 'assignedTo', $userList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return $model->isItemizedReceipt ? "Itemized" : "Simple";
                                            },
                                        ],
                                        /* [
                                             'attribute' => 'businessId',
                                             'label'=> 'Business Name',
                                             'filter' => Html::activeDropDownList($searchModel, 'businessId', $businessList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                             'value' => function ($model) {
                                                 return !empty(Yii::$app->common->getParentName('BUSINESS', $model->businessId)) ? Yii::$app->common->getParentName('BUSINESS', $model->businessId) : '-',
                                             },
                                         ],*/

                                        [
                                            'attribute' => 'createdById',
                                            'label' => 'User Name',
                                            //'filter' => Html::activeDropDownList($searchModel, 'assignedTo', $userList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('USER', $model->createdById)) ? Yii::$app->common->getParentName('USER', $model->createdById) : '-';
                                            },
                                        ],
                                        [
                                            'attribute' => 'customerId',
                                            'label' => 'Customer Name',
                                            //'filter' => Html::activeDropDownList($searchModel, 'customerId', $customerList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('CUSTOMER', $model->customerId)) ? Yii::$app->common->getParentName('CUSTOMER', $model->customerId) : '-';
                                            },
                                        ],
                                        [
                                            'attribute' => 'companyName',
                                            'label' => 'Company Name',
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('COMPANY', $model->customerId)) ? Yii::$app->common->getParentName('COMPANY', $model->customerId) : '-';
                                            },
                                        ],
                                        // 'termsIds',
                                        //'totalTaxAmount',
                                        [
                                            'attribute' => 'totalAmount',
                                            'label' => 'Due Amount',
                                        ],
                                        [
                                            'attribute' => 'paidAmount',
                                            'label' => 'Paid Amount',
                                        ],
                                        'paymentMethod',
                                        'paymentReferenceNumber',
                                        //'totalDiscountAmount',
                                        // 'statusAttachment',
                                        /*[
                                            'attribute' => 'statusUpdatedById',
                                            'filter' => Html::activeDropDownList($searchModel, 'statusUpdatedById', $userList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('USER', $model->statusUpdatedById)) ? Yii::$app->common->getParentName('USER', $model->statusUpdatedById) : '-';
                                            },
                                        ],*/
                                          'receiptDate',
                                        //'isDeleted',
                                        //'createdAt',
                                        //'updatedAt',
                                        //'deletedAt',
                                        [
                                            'attribute' => 'updatedAt',
                                            'value' => 'updatedAt',
                                            'filter' => DatePicker::widget([
                                                'model' => $searchModel,
                                                'attribute' => 'updatedAt',
                                                'pluginOptions' => [
                                                    'format' => 'yyyy-mm-dd',
                                                    'autoclose' => true,
                                                ]
                                            ]),
                                        ],
                                        [
                                            'header' => 'Action',
                                            'class' => 'yii\grid\ActionColumn',
                                            'headerOptions' => ['style' => 'width:5%'],
                                            'visible' => true,
                                            'template' => '{view} {html} {print}',
                                            'buttons' => [
                                                'view' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['/receipt/view-pdf?id=' . $model->id]);
                                                    return Html::a('<span class="material-icons">visibility</span>', $url, [
                                                        'title' => Yii::t('app', 'View'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },

                                                'html' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['/receipt/view-html?id=' . $model->id]);
                                                    return Html::a('<span class="material-icons">web</span>', $url, [
                                                        'title' => Yii::t('app', 'View'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },

                                                'edit' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['/receipt/edit?receipt_number=' . base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">edit</span>', $url, [
                                                        'title' => Yii::t('app', 'Edit Receipt'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                                'print' => function ($url, $model) {
                                                    $url = !empty($model->pdfFileUrl) ? $model->pdfFileUrl : '#';
                                                    return Html::a('<span class="material-icons">picture_as_pdf</span>', $url, [
                                                        'title' => Yii::t('app', 'Print PDF'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                            ],
                                        ],
                                    ],
                                    'pager' => [
                                        'prevPageLabel' => '<i class="material-icons">chevron_left</i>',
                                        'nextPageLabel' => '<i class="material-icons">chevron_right</i>',
                                        // Customzing options for pager container tag
                                        'options' => [
                                            'tag' => 'ul',
                                            'class' => 'pagination',
                                            'id' => 'pager-container',
                                        ],
                                        // Customzing CSS class for pager link
                                        'linkOptions' => ['class' => 'waves-effect'],
                                        'activePageCssClass' => 'active',
                                        'disabledPageCssClass' => 'disabled',
                                        // Customzing CSS class for navigating link
                                        'prevPageCssClass' => 'mypre',
                                        'nextPageCssClass' => 'mynext',
                                    ],
                                ]);
                                Pjax::end(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
<script type="text/javascript">
    $("#daterange").daterangepicker({
        maxDate: new Date(),
        locale: {
            format: 'Y-MM-DD'
        }
    });
</script>