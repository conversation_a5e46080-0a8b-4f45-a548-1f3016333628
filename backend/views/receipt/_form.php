<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Receipt */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="receipt-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'receiptNumber')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'receiptDate')->textInput() ?>

    <?= $form->field($model, 'isItemizedReceipt')->textInput() ?>

    <?= $form->field($model, 'businessId')->textInput() ?>

    <?= $form->field($model, 'customerId')->textInput() ?>

    <?= $form->field($model, 'templateId')->textInput() ?>

    <?= $form->field($model, 'createdById')->textInput() ?>

    <?= $form->field($model, 'subTotalAmount')->textInput() ?>

    <?= $form->field($model, 'totalAmount')->textInput() ?>

    <?= $form->field($model, 'paidAmount')->textInput() ?>

    <?= $form->field($model, 'paymentMethod')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'paymentReferenceNumber')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'paymentNote')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'taxPercentage')->textInput() ?>

    <?= $form->field($model, 'totalTaxAmount')->textInput() ?>

    <?= $form->field($model, 'discountType')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'discountPercentage')->textInput() ?>

    <?= $form->field($model, 'totalDiscountAmount')->textInput() ?>

    <?= $form->field($model, 'otherCharges')->textInput() ?>

    <?= $form->field($model, 'otherInfo')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'pdfFileName')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'pdfFileUrl')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'status')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'statusText')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'receiptSettings')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'additionalFields')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'isArchived')->textInput() ?>

    <?= $form->field($model, 'archivedInfo')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'invoiceReferenceNumber')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'isDeleted')->textInput() ?>

    <?= $form->field($model, 'createdAt')->textInput() ?>

    <?= $form->field($model, 'updatedAt')->textInput() ?>

    <?= $form->field($model, 'deletedAt')->textInput() ?>

    <div class="form-group text-right pr-3">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
