<?php

use common\models\Business;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Customer */
/* @var string[] $subUserList */
/* @var $form yii\widgets\ActiveForm */
$isAdmin = Yii::$app->session['isAdmin'];
$isDisabled = "";
if (!$isAdmin) {
    $isDisabled = 'true';
    $model->addedBy = array_key_first($subUserList);
}
$business = Business::getBusiness();

?>

<div class="customer-form">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-primary">
                        <?php if (!empty($model->id)) { ?>
                            <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Update Customer
                                : <?php echo $model->name ?></h3>
                            <p class="card-category"></p>
                        <?php } else { ?>
                            <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Create
                                Customer </h3>
                        <?php } ?>
                    </div>
                    <div class="card-body">
                        <?php $form = ActiveForm::begin(['options' => [
                            'class' => 'md-form',
                            'enctype' => 'multipart/form-data',
                            'autocomplete' => "off"
                        ]]); ?>

                        <div class="row md-form">
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'name')->textInput(['maxlength' => true, 'class' => 'form-control']) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'companyName')->textInput(['maxlength' => true]) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'phoneNumber')->textInput(['maxlength' => true, 'class' => 'form-control']) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'email', ['enableAjaxValidation' => true])->textInput(['maxlength' => true, 'class' => 'form-control']) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'addressLine1')->textInput(['maxlength' => true]) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'addressLine2')->textInput(['maxlength' => true]) ?>
                            </div>
                            <div class="col-md-6 col-lg-6 col-sm-6">
                                <?= $form->field($model, 'otherInfo')->textInput(['maxlength' => true]) ?>
                            </div>
                            <?php if ($business->regionCode === "IN"): ?>
                                <div class="col-md-6 col-lg-6 col-sm-6">
                                    <?= $form->field($model, 'taxNumber')->textInput(['maxlength' => true]) ?>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>

                    <div class="form-group text-right pr-3">
                        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
                    </div>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>

<script type="text/javascript">
    function readURL(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function (e) {
                $('#imgInp').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    $("#customer-profilepic").change(function () {
        readURL(this);
    });
</script>