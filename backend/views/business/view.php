<?php

use yii\helpers\Html;
use yii\web\YiiAsset;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Business */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Businesses', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
YiiAsset::register($this);
$session = Yii::$app->session;
?>
<div class="business-view">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary">
                            <?php if (!empty($model->id)) { ?>
                                <h3 class="card-title text-center text-uppercase" style="font-weight: 700">View Region
                                    : <?php echo $model->name ?></h3>
                                <p class="card-category"></p>
                            <?php } ?>
                        </div>
                        <div class="card-body">
                            <?php if ($session['isAdmin'] == 1) { ?>
                                <p class="table-add float-right mb-3 mr-2">
                                    <?= Html::a('Update', ['update', 'id' => base64_encode($model->id)], ['class' => 'btn btn-primary']) ?>
                                    <!-- <button type="button" class="btn btn-danger" data-toggle="modal"
                                             data-target="#deleteModel">
                                         Delete
                                     </button>-->
                                </p>
                            <?php } ?>
                            <div class="table-responsive">

                                <?= DetailView::widget([
                                    'model' => $model,
                                    'attributes' => [
                                        'id',
                                        'name',
                                        'tagLine',
                                        'email:email',
                                        'website',
                                        'phoneNumber',
                                        'addressLine1',
                                        'addressLine2',
                                        'addressLine3',
                                        'taxNumber',
                                        'panNumber',
                                        [
                                            'attribute' => 'image',
                                            'value' => $model->image,
                                            'format' => ['image', ['width' => '100', 'height' => '100']],
                                        ],
                                        [
                                            'attribute' => 'signatureImg',
                                            'value' => $model->signatureImg,
                                            'format' => ['image', ['width' => '100', 'height' => '100']],
                                        ],
                                        'bankInfo',
                                        'upiCode',
                                        [
                                            'attribute' => 'upiQrImage',
                                            'value' => $model->upiQrImage,
                                            'format' => ['image', ['width' => '100', 'height' => '100']],
                                        ],
                                    ],
                                ]) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript">
    $('#confirm').on('click', function () {
        var deleteid = <?php echo "'" . base64_encode($model->id) . "'"; ?>;
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/business/delete'])?>',
            type: 'POST',
            data: {id: deleteid},
            success: function (response) {
                if (response == 1) {
                    window.location.href = '/business';
                } else {

                }

            }
        });
    });
</script>

