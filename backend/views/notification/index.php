<?php

use backend\models\Notification;
use common\models\enum\NotificationType;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model backend\models\Notification */
/* @var $form yii\widgets\ActiveForm */

$this->title = 'Notification Messages';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="notification-message-index">

    <div class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header card-header-primary">
                    <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Notification</h3>
                </div>

                <div class="card-body">
                    <h4 class="text-center text-uppercase">Total Records
                        Processed
                        : <?= $model->totalRecordsCount ?></h4>

                    <div class="notification-form">
                        <?php $form = ActiveForm::begin(); ?>

                        <?= $form->field($model, 'notificationMode')->dropDownList(Notification::NotificationModes, ['prompt' => '--']) ?>
                        <?= $form->field($model, 'type')->dropDownList(NotificationType::getSelectionArray(), ['prompt' => '--']) ?>
                        <?= $form->field($model, 'topic')->textInput(['maxlength' => true]) ?>
                        <?= $form->field($model, 'actionUrl')->textInput() ?>
                        <?= $form->field($model, 'coverImage')->textInput() ?>
                        <?= $form->field($model, 'alertTitle')->textInput(['maxlength' => true]) ?>
                        <?= $form->field($model, 'message')->textInput(['maxlength' => true]) ?>
                        <?= $form->field($model, 'userIds')->textarea(['rows' => 3]) ?>
                        <?= $form->field($model, 'query')->textarea(['rows' => 3]) ?>
                        <?= $form->field($model, 'userIdColumn')->dropDownList(Notification::UserIdColumnOptions, ['prompt' => '']) ?>

                        <div class="form-group">
                            <?= Html::submitButton('Send Notification', ['class' => 'btn btn-success', 'data' => [
                                'confirm' => 'Are you sure you want send notification?',
                                'method' => 'post',
                            ],]) ?>
                        </div>
                        <?php ActiveForm::end(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
