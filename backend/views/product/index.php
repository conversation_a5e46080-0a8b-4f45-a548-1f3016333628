<?php

use common\models\Business;
use common\models\enum\Key;
use common\models\ProductCategory;
use kartik\date\DatePicker;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ProductSearch */
/* @var $model common\models\Product */
/* @var $dataProvider yii\data\ActiveDataProvider */
/* @var $businessId integer */

$this->title = 'Products';
$this->params['breadcrumbs'][] = $this->title;
$date = null;
$action_list = '';
$visible = true;
$session = Yii::$app->session;
if ($session['isAdmin']) {
    $action_list = '{update} {delete}';
} else {
    $action_list = '{view}';
    $visible = false;
}


$isProductWiseTaxEnabled = Business::getConfig(Key::GROUP_QUOTATION, Key::TAX_SETTINGS) == Key::PER_ITEM;
$isProductCodeEnable = Business::getConfig(Key::GROUP_APP, Key::isProductCodeEnable) ?? 0;

$isInventoryEnabled = Business::getConfig(Key::GROUP_APP, Key::IS_INVENTORY_ENABLED);
$isInventoryMode = $searchModel->isInventoryMode;

$useSeparateInventoryItems = $searchModel->useSeparateInventoryItems;

$productCategoryQuery = ProductCategory::find()->byBusiness($businessId);

if ($useSeparateInventoryItems){
        $productCategoryQuery->andFilterWhere(['isOnlyForInventory' => $isInventoryMode]);
}

$categoryArray = ArrayHelper::map($productCategoryQuery->asArray()->all(), 'id', 'name');
$allowProductImport = false;
$shouldDisplayQty = $isInventoryEnabled && (!$useSeparateInventoryItems || $isInventoryMode);


$allowCreateProduct = Business::getConfig(Key::GROUP_APP, Key::ALLOW_CREATE_PRODUCT_TO_SALES_USER);
?>
<div class="product-index">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary">
                            <h4 class="card-title text-center text-uppercase"
                                style="font-weight: 700"><?= Html::encode($this->title) ?></h4>
                            <p class="card-category"></p>
                        </div>
                        <div class="card-body">
                            <p class="table-add float-right mb-3 mr-2">
                                <?= Html::a('Download Report', ['download-excel'], [
                                    'class' => 'btn btn-primary',
                                    'data-pjax' => 0
                                ]) ?>
                            </p>

                            <?php if (!$isInventoryMode && ($session['isAdmin'] == 1 || $allowCreateProduct)) { ?>
                                <?php if ($allowProductImport): ?>
                                    <p class="table-add float-left mb-3 mr-2">
                                        <?= Html::a('Import Product', ['/product/import'], ['class' => 'btn btn-primary']) ?>
                                    </p>
                                <?php endif; ?>
                                <p class="table-add float-right mb-3 mr-2">
                                    <?= Html::a('Create Product', ['create'], ['class' => 'btn btn-success']) ?>
                                </p>
                            <?php } ?>
                            <div class="table-responsive">
                                <?php Pjax::begin();
                                echo GridView::widget([
                                    'dataProvider' => $dataProvider,
                                    'filterModel' => $searchModel,
                                    'columns' => [
                                        //    ['class' => 'yii\grid\SerialColumn'],
                                        [
                                            'class' => 'yii\grid\SerialColumn',
                                        ],
                                        [
                                            'attribute' => 'code',
                                            'visible' => $isProductCodeEnable,
                                            'headerOptions' => ['style' => 'width:8%'],
                                        ],

//                                        'code',
                                        'name',
                                        //'description',
                                        //'options',
                                        [
                                            'attribute' => 'taxPercentage',
                                            'visible' => $isProductWiseTaxEnabled,
                                            'headerOptions' => ['style' => 'width:5%'],
                                        ],
                                        [
                                            'label' => 'Available Qty',
                                            'value'=>'inventory.quantity',
                                            'visible' => $shouldDisplayQty,
                                            'headerOptions' => ['style' => 'width:5%'],
                                        ],
                                        [
                                            'attribute' => 'price',
                                            'visible' => !$isInventoryMode,
                                            'headerOptions' => ['style' => 'width:5%'],
                                        ],
                                        [
                                            'attribute' => 'categoryId',
                                            'label' => 'Category',
                                            'headerOptions' => ['style' => 'width:10%'],
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('CATEGORY', $model->categoryId)) ? Yii::$app->common->getParentName('CATEGORY', $model->categoryId) : '-';
                                            },
                                            'filter' => Html::activeDropDownList($searchModel, 'categoryId', $categoryArray, ['class' => 'form-control', 'prompt' => '- Select -']),

                                        ],
                                        [
                                            'attribute' => 'image',
                                            'format' => 'html',
                                            'visible' => $isProductWiseTaxEnabled,
                                            'value' => function ($data) {
                                                return Html::img($data['image'],
                                                    ['width' => '70px']);
                                            },
                                            'filter' => false,
                                        ],
                                        [
                                            'attribute' => 'isDeleted',
                                            'headerOptions' => ['style' => 'width:10%'],
                                            'filter' => Html::activeDropDownList($searchModel, 'isDeleted', ["0" => "Active", "1" => "Deactive"], ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return Yii::$app->common->displayLabel('isDeleted', $model->isDeleted);
                                            },
                                            'contentOptions' => function ($model, $key, $index, $column) {
                                                $id = base64_encode($model->id);
                                                if ($model->isDeleted == '1') {
                                                    return ['style' => 'width:90px', 'class' => 'btn btn-danger  btn-sm activebtn', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#activeModel", "onclick" => "activeDeactive('$id')"];
                                                } else {
                                                    return ['style' => 'width:90px', 'class' => 'btn btn-success  btn-sm activebtn', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#deactiveModel", "onclick" => "activeDeactive('$id')"];
                                                }

                                            },
                                            'visible' => !($visible == 0),
                                        ],
                                        [
                                            'attribute' => 'createdAt',
                                            'value' => 'createdAt',
                                            'filter' => DatePicker::widget([
                                                'model' => $searchModel,
                                                'attribute' => 'createdAt',
                                                'pluginOptions' => [
                                                    'format' => 'yyyy-mm-dd',
                                                    'autoclose' => true,
                                                ]
                                            ]),
                                        ],
                                        [
                                            'header' => 'Action',
                                            'class' => 'yii\grid\ActionColumn',
                                            'headerOptions' => ['style' => 'width:5%'],
                                            'template' => $action_list,
                                            'visible' => !Yii::$app->user->isGuest,
                                            'buttons' => [
                                                'view' => function ($url, $model) {
                                                    $session = Yii::$app->session;
                                                    $url = Yii::$app->urlManager->createUrl(['/product/view?id=' . base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">visibility</span>', $url, [
                                                        'title' => Yii::t('app', 'View'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                                'update' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['product/update', 'id' => base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">edit</span>', $url, [
                                                        'title' => Yii::t('app', 'Update'),
                                                        'data-pjax' => '0'
                                                    ]);
                                                },
                                                'delete' => function ($url, $model) {
                                                    $id = base64_encode($model->id);
                                                    return Html::a('<span class="material-icons">delete</span>', [''], ['class' => 'delete', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#deleteModel", "onclick" => "deleteProduct('$id')"]);
                                                },
                                            ],
                                        ],
                                    ],
                                    'pager' => [
                                        'prevPageLabel' => '<i class="material-icons">chevron_left</i>',
                                        'nextPageLabel' => '<i class="material-icons">chevron_right</i>',
                                        // Customzing options for pager container tag
                                        'options' => [
                                            'tag' => 'ul',
                                            'class' => 'pagination',
                                            'id' => 'pager-container',
                                        ],
                                        // Customzing CSS class for pager link
                                        'linkOptions' => ['class' => 'waves-effect'],
                                        'activePageCssClass' => 'active',
                                        'disabledPageCssClass' => 'disabled',
                                        // Customzing CSS class for navigating link
                                        'prevPageCssClass' => 'mypre',
                                        'nextPageCssClass' => 'mynext',
                                    ],
                                ]);
                                Pjax::end(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>

<script type="text/javascript">
    var deleteid;

    function deleteProduct($id) {
        deleteid = $id;
    }

    $('#confirm').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/product/delete'])?>',
            type: 'POST',
            data: {id: deleteid},
            success: function (response) {
                if (response == 1) {
                    location.reload(true);
                } else {

                }

            }
        });
    });
    $("#daterange").daterangepicker({
        maxDate: new Date(),
        locale: {
            format: 'Y-MM-DD',
        }

    });
    var productid;

    function activeDeactive($id) {
        productid = $id;
    }

    $('#confirmActive').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/product/active'])?>',
            type: 'post',
            data: {id: productid},
            success: function (data) {
                location.reload(true);
            }
        });
    });
    $('#confirmDeactive').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/product/active'])?>',
            type: 'post',
            data: {id: productid},
            success: function (data) {
                location.reload(true);
            }
        });
    });
</script>

