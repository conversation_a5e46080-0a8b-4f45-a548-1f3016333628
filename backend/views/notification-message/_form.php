<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\NotificationMessage */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="notification-message-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'fromUserId')->textInput() ?>

    <?= $form->field($model, 'fromUserType')->dropDownList(['user' => 'User', 'customer' => 'Customer',], ['prompt' => '']) ?>

    <?= $form->field($model, 'toUserId')->textInput() ?>

    <?= $form->field($model, 'toUserType')->dropDownList(['user' => 'User', 'customer' => 'Customer',], ['prompt' => '']) ?>

    <?= $form->field($model, 'topic')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'message')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'type')->dropDownList(['general' => 'General', 'status_update' => 'Status update', 'all-users' => 'All-users', 'all-sub-users' => 'All-sub-users', 'all-customers' => 'All-customers', 'assigned' => 'Assigned', 'new_inquiry' => 'New inquiry', 'topic' => 'Topic',], ['prompt' => '']) ?>

    <?= $form->field($model, 'jsonData')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'createdAt')->textInput() ?>

    <?= $form->field($model, 'updatedAt')->textInput() ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
