<?php

/* @var $this yii\web\View */
/* @var $form yii\bootstrap\ActiveForm */

/* @var $model LoginForm */

use common\models\LoginForm;
use rce\material\widgets\Noti;
use yii\bootstrap\ActiveForm;
use yii\helpers\Html;

$this->title = 'Request Password Reset';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-login">
    <!-- <h1><? //= Html::encode($this->title) ?></h1> -->
    <div class="col-md-4 col-sm-6 ml-auto mr-auto"><!-- col-lg-4  -->
        <div class="card card-login card-hidden">
            <div class="card-header card-header-rose text-center" data-background-color="orange">
                <h4 class="card-title"><?= Html::encode($this->title) ?></h4>
            </div>
            <?php $form = ActiveForm::begin(['id' => 'request-password-reset-form']); ?>
            <div class="card-body ">
                <p>Please fill out your email. A link to reset password will be sent there.</p>
                <span class="bmd-form-group">
                <?= $form->field($model, 'email', ['template' => '
                   <div class="help-block"></div>
                   <div class="input-group">
                       <div class="input-group-prepend">
                       <span class="input-group-text">
                       <i class="material-icons">email</i>
                       </span>
                       </div>
                   {input}
                  
                   </div>
                    {error}
                   '
                ])->textInput()->label(false) ?>
           </span>
            </div>
            <div class="card-footer justify-content-center">
                <?= Html::submitButton('Send', ['class' => 'btn btn-primary btn-block btn-border']) ?>
            </div>

            <div class="card-footer">
            </div>
            <?php ActiveForm::end(); ?>
        </div>
        <?= Noti::widget() ?>
    </div>
</div>
