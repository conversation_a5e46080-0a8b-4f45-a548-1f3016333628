<?php

use common\models\Region;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Region */
/* @var $form yii\widgets\ActiveForm */

$parentUserList = [];
if (!empty($model->type)) {
    $searchTearm = '';
    $parentUserList = [];
    if ($model->type == 'state') {
        $searchTearm = 'country';
    }
    if ($model->type == 'city') {
        $searchTearm = 'state';
    }
    if ($searchTearm != '') {
        $parentUserList = ArrayHelper::map(Region::find()->where(['isDeleted' => 0])->andFilterWhere(['like', 'type', $searchTearm])->asArray()->all(), 'id', 'name');
    }
}
?>

<div class="region-form">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-primary">
                        <?php if (!empty($model->id)) { ?>
                            <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Update Regions
                                : <?php echo $model->name ?></h3>
                            <p class="card-category"></p>
                        <?php } else { ?>
                            <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Create
                                Regions </h3>
                        <?php } ?>
                    </div>
                    <div class="card-body">
                        <?php $form = ActiveForm::begin(); ?>
                        <div class="row md-form">
                            <div class="col-md-12 col-lg-12 col-sm-12">
                                <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
                            </div>
                        </div>
                        <div class="row md-form">
                            <div class="col-md-12 col-lg-12 col-sm-12">
                                <?= $form->field($model, 'type')->dropDownList(['country' => 'Country', 'state' => 'State', 'city' => 'City',], ['prompt' => 'Select Type', 'class' => 'form-control',
                                    'onchange' => '
                                $.post( "' . urldecode(Yii::$app->urlManager->createUrl('region/lists?type=')) . '"+$(this).val(), function( data ) {
                                  $( "select#parent_id" ).html( data );
                                });'
                                ])->label() ?>
                            </div>
                        </div>
                        <div class="row md-form">
                            <div class="col-md-12 col-lg-12 col-sm-12">
                                <?=
                                $form->field($model, 'parentId')->dropDownList($parentUserList, [
                                        'id' => 'parent_id',
                                        'prompt' => '-Choose parent-',
                                        'value' => $model->parentId ?? 0,]
                                )->label('Parent');
                                ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <?= Html::submitButton('Save', ['class' => 'btn btn-success pull-right']) ?>
                        </div>

                        <?php ActiveForm::end(); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/0.97.3/js/materialize.min.js">
