<?php

use common\models\Business;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\Breadcrumbs;

/* @var $this View */
/* @var $content string */
$session = Yii::$app->session;
$selectedBusinessId = $session->get('businessId');
if (!empty($selectedBusinessId)){
    $business = Business::findByPk($selectedBusinessId);
    $selectedBusinessName = $business->name;
}

?>
<!--                <div class="dropdown-item">Support PIN : <?=Yii::$app->user->id?> </div>-->
<!--                <div class="dropdown-item">Business PIN : <?=Yii::$app->user->identity->business->id?> </div>-->

<nav class="navbar navbar-expand-lg navbar-transparent">

    <div class="container-fluid">
        <?php if (!empty($selectedBusinessId)) : ?>

        <nav aria-label="breadcrumb">
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="/business">Business</a></li>
                <li class="breadcrumb-item active"><?= $selectedBusinessName ?></li>
            </ul>
        </nav>
        <?php endif; ?>

        <div class="navbar-wrapper">
            <nav aria-label="breadcrumb">
                <?= Breadcrumbs::widget([
                    'itemTemplate' => "<li class=\"breadcrumb-item\">{link}</li>\n",
                    'activeItemTemplate' => "<li class=\"breadcrumb-item active\">{link}</li>\n",
                    'links' => $this->params['breadcrumbs'] ?? [],
                ]) ?>
            </nav>
        </div>
        <button class="navbar-toggler" type="button" data-toggle="collapse" aria-controls="navigation-index"
                aria-expanded="false" aria-label="Toggle navigation">
            <span class="sr-only">Toggle navigation</span>
            <span class="navbar-toggler-icon icon-bar"></span>
            <span class="navbar-toggler-icon icon-bar"></span>
            <span class="navbar-toggler-icon icon-bar"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end">
            <!-- <form class="navbar-form">
               <div class="input-group no-border">
                 <input type="text" value="" class="form-control" placeholder="Search...">
                 <button type="submit" class="btn btn-white btn-round btn-just-icon">
                   <i class="material-icons">search</i>
                   <div class="ripple-container"></div>
                 </button>
               </div>
             </form>-->
            <ul class="navbar-nav">
<!--                <div class="dropdown-divider"></div>-->

                <!--     <li class="nav-item">
                       <a class="nav-link" href="#">
                         <i class="material-icons">dashboard</i>
                         <p class="d-lg-none d-md-block">
                           Stats
                         </p>
                       </a>
                     </li>
                     <li class="nav-item dropdown">
                       <a class="nav-link" href="http://example.com" id="navbarDropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                         <i class="material-icons">notifications</i>
                         <span class="notification">5</span>
                         <p class="d-lg-none d-md-block">
                           Some Actions
                         </p>
                       </a>
                       <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
                         <a class="dropdown-item" href="#">Mike John responded to your email</a>
                         <a class="dropdown-item" href="#">You have 5 new tasks</a>
                         <a class="dropdown-item" href="#">You're now friend with Andrew</a>
                         <a class="dropdown-item" href="#">Another Notification</a>
                         <a class="dropdown-item" href="#">Another One</a>
                       </div>
                     </li>-->
                <li class="nav-item dropdown">
                    <a class="nav-link" href="#" id="navbarDropdownProfile" data-toggle="dropdown" aria-haspopup="true"
                       aria-expanded="false">
                        <i class="material-icons">person</i>
                        <p class="d-lg-none d-md-block">
                            Account
                        </p>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownProfile">
                        <?php $url = Yii::$app->urlManager->createUrl(['user/update', 'id' => base64_encode(Yii::$app->user->id)]); ?>
                        <?php $changePasswordUrl = Yii::$app->urlManager->createUrl(['user/change-password', 'id' => base64_encode(Yii::$app->user->id)]); ?>
                        <a class="dropdown-item" href=<?= $url ?>>Profile</a>
                        <a class="dropdown-item" href=<?= $changePasswordUrl ?>>Change Password</a>
                        <!-- <a class="dropdown-item" href="#">Settings</a>-->
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" style="padding: 0;">
                            <?php
                            echo Html::beginForm(['/site/logout'], 'post');
                            echo Html::submitButton(
                                'Logout (' . Yii::$app->user->identity->email . ')',
                                ['class' => 'btn btn-link logout']
                            );
                            echo Html::endForm();
                            ?>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#">Business PIN : <?=$selectedBusinessId?></a>

                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>
<!-- End Navbar -->
