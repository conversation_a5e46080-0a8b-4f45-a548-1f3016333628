<?php
/* @var $directoryAsset */
use common\models\Business;
use common\models\enum\Key;

$menu = $img = "";
$config = new rce\material\Config();
if (class_exists('common\models\Menu')) {
    // advance template
    $menu = common\models\Menu::getMenu();
}
if (empty($config::sidebarBackgroundImage())) {
    $img = $directoryAsset . '/img/sidebar-1.jpg';
} else {
    $img = $config::sidebarBackgroundImage();
}
$business = Business::getBusiness();
$logoClass = "sidebar-logo-img-small";
$isDisplayAppName = $business?->config(Key::GROUP_APP, Key::IS_DISPLAY_APP_NAME) ?? false;
if (!$isDisplayAppName) {
    $logoClass = "sidebar-logo-img-big";
}
?>
<div class="sidebar" data-color="<?= $config::sidebarColor() ?>"
     data-background-color="<?= $config::sidebarBackgroundColor() ?>">
    <div class="logo">
        <a href="#" class="simple-text logo-mini">
            <?php
            if (empty($config::logoMini())) { ?>
                <img src="<?php echo $business->image ?>" class="<?= $logoClass ?>">
            <?php } else {
                echo $config::logoMini();
            }
            ?>
        </a>
        <?php if ($isDisplayAppName): ?>
            <a href="#" class="simple-text logo-normal" style="white-space: normal">
                <?= $business->name ?>
            </a>
        <?php endif; ?>
    </div>
    <div class="sidebar-wrapper">
        <!--<ul class="nav">
          <li class="nav-item active  ">
            <a class="nav-link" href="/">
              <i class="material-icons">home</i>
              <p>Home</p>
            </a>
          </li>
        </ul>-->
        <?= $menu ?>
    </div>
    <div class="sidebar-background" style="background-image: url(<?= $img ?>) "></div>
</div>
