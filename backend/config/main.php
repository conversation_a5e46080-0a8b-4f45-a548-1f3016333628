<?php

use yii\bootstrap\BootstrapAsset;

$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-backend',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'backend\controllers',
    'bootstrap' => ['log'],
    'homeUrl' => '/backend/site/index',
    'modules' => [
        'gridview' => [
            'class' => '\kartik\grid\Module'
        ],
        'redactor' => [
            'class' => 'yii\redactor\RedactorModule',
            'widgetClientOptions' => [
                //['html', 'formatting', 'bold', 'italic', 'deleted', 'unorderedlist', 'orderedlist',
                //   'outdent', 'indent', 'image', 'file', 'link', 'alignment', 'horizontalrule'], // + 'underline'
                'buttonsHide' => ['html', 'formatting', 'deleted', 'unorderedlist', 'orderedlist', 'outdent', 'indent', 'image', 'file', 'link', 'alignment', 'horizontalrule'],
            ]
        ],
    ],
    'components' => [
        'request' => [
            'csrfParam' => '_csrf-backend',
            'cookieValidationKey' => 'backend-quotation-pro-secret-key',
            'baseUrl' => '/backend',
        ],
        'common' => [
            'class' => common\components\Common::class,
        ],
        'user' => [
            'identityClass' => 'common\models\User',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-backend', 'httpOnly' => true],
        ],
        'session' => [
            // this is the name of the session cookie used for login on the backend
            'name' => 'advanced-backend',
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],


        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'baseUrl' => '/backend',
            'rules' => [
                '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
                '<controller:\w+>/<action:\w+>/<function:\w+>' => '<controller>/<action>'
            ],
        ],
        'assetManager' => [
            'bundles' => [
                'yii\bootstrap4\BootstrapAsset' => [
                    'css' => [],

                ],
                BootstrapAsset::class => [
                    'css' => [],
                    'js' => [],
                ],
                'rce\material\Assets' => [
                    'siteTitle' => APP_NAME,
                    'sidebarColor' => 'azure', // "purple | azure | green | orange | danger | rose"
                    'sidebarBackgroundColor' => 'white', // "black | white"
                    // 'sidebarBackgroundImage' => ''
                ],

            ],
        ],

    ],
    'params' => $params,
];
