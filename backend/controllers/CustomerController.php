<?php

namespace backend\controllers;

use backend\models\CustomerSearch;
use Box\Spout\Common\Exception\IOException;
use Box\Spout\Common\Exception\SpoutException;
use Box\Spout\Common\Exception\UnsupportedTypeException;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Exception\WriterNotOpenedException;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use common\models\Business;
use common\models\Customer;
use common\models\User;
use kartik\form\ActiveForm;
use kartik\mpdf\Pdf;
use Mpdf\MpdfException;
use setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfParser\Type\PdfTypeException;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\Expression;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * CustomerController implements the CRUD actions for Customer model.
 */
class CustomerController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Customer models.
     * @return mixed
     */
    public function actionIndex()
    {
        $model = new Customer();
        $searchModel = new CustomerSearch();
        $searchModel->businessId = $this->businessId;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->sort->defaultOrder = ['id' => SORT_DESC];
        if ($model->load(Yii::$app->request->get())) {

            $date_range_arr = explode(' - ', $_GET['Customer']['DateRange']);
            $model->DateRange = $_GET['Customer']['DateRange'];
            $FromDate = Yii::$app->common->formatDate($date_range_arr[0], 2);
            $ToDate = Yii::$app->common->formatDate($date_range_arr[1], 2);
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
            $final_details_arr = $dataProvider->getModels();
            if (!empty($final_details_arr)) {

                if (isset($_GET['btnReport']) && $_GET['btnReport'] == 'excel') {
                    list($dataProvider, $final_details_arr) = $this->filterUsingDatesForExcel($searchModel, $FromDate, $ToDate);
                }
                if (isset($_GET['btnReport']) && $_GET['btnReport'] == 'pdf') {
                    return $this->filterUsingDatesForPDF($searchModel, $model, $FromDate, $ToDate);
                }
            }
        }
        return $this->render('index', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @param CustomerSearch $searchModel
     * @param $FromDate
     * @param $ToDate
     * @return array
     * @throws IOException
     * @throws SpoutException
     * @throws UnsupportedTypeException
     * @throws WriterNotOpenedException
     */
    public function filterUsingDatesForExcel(CustomerSearch $searchModel, $FromDate, $ToDate): array
    {
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination = false;
        $final_details_arr = $dataProvider->getModels();
        $style = array();
        $timpstamp = Yii::$app->user->id . time();
        $writer = WriterFactory::create(Type::XLSX); // for XLSX files

        $style = (new StyleBuilder())->setFontName('Arial')->setFontSize(11)->build();
        $defaultStyle = (new StyleBuilder())->setFontName('Arial')->setFontSize(11)->build();
        $FilePath = UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_customer_report_excel' . ".xlsx";

        $writer->setDefaultRowStyle($defaultStyle)->openToFile($FilePath); // write data to a file or to a PHP stream
        $writer->addRow(['Report Name', 'Customer Report'], $style);
        $writer->addRow(['From Date:', date('d/m/Y', strtotime($FromDate))], $style);
        $writer->addRow(['To Date:', date('d/m/Y', strtotime($ToDate))], $style);
        $writer->addRow([''], $style);
        $writer->addRow(['Serial No', 'ID', 'First Name', 'Last Name', 'Email', 'Phone Number', 'Address Line1', 'Address Line2', 'Address Line3', 'Company Name', 'Profile Pic', 'GST/Vat Number', 'PAN Number', 'Status', 'Added By', 'Is Deleted', 'Created At', 'Updated At', 'Deleted At'], $style);
        $i = 1;
        foreach ($final_details_arr as $key => $val) {
            $writer->addRow([$i
                , $val['id']
                , !empty($val['name']) ? $val['name'] : '-'
                , !empty($val['email']) ? $val['email'] : '-'
                , !empty($val['phoneNumber']) ? $val['phoneNumber'] : '-'
                , !empty($val['addressLine1']) ? $val['addressLine1'] : '-'
                , !empty($val['addressLine2']) ? $val['addressLine2'] : '-'
                , !empty($val['addressLine3']) ? $val['addressLine3'] : '-'
                , !empty($val['companyName']) ? $val['companyName'] : '-'
                , !empty($val['profilePic']) ? $val['profilePic'] : '-'
                , !empty($val['taxNumber']) ? $val['taxNumber'] : '-'
                , !empty($val['panNumber']) ? $val['panNumber'] : '-'
                , !empty($val['status']) ? $val['status'] : '-'
                , !empty(Yii::$app->common->getParentName('USER', $val['addedBy'])) ? Yii::$app->common->getParentName('USER', $val['addedBy']) : '-'
                , Yii::$app->common->displayLabel('isDeleted', $val['isDeleted'])
                , !empty($val['createdAt']) ? $val['createdAt'] : '-'
                , !empty($val['updatedAt']) ? $val['updatedAt'] : '-'
                , !empty($val['deletedAt']) ? $val['deletedAt'] : '-'
            ], $style);

            $i++;
        }
        $writer->close();
        @chmod(UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_customer_report_excel' . ".xlsx", 0777);
        Yii::$app->response->sendFile($FilePath);
        unlink(UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_customer_report_excel' . ".xlsx");
        return array($dataProvider, $final_details_arr);
    }

    /**
     * @param CustomerSearch $searchModel
     * @param Customer $model
     * @param $FromDate
     * @param $ToDate
     * @return string
     * @throws MpdfException
     * @throws CrossReferenceException
     * @throws PdfParserException
     * @throws PdfTypeException
     * @throws InvalidConfigException
     */
    public function filterUsingDatesForPDF(CustomerSearch $searchModel, Customer $model, $FromDate, $ToDate): string
    {
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination = false;
        $final_details_arr = $dataProvider->getModels();
        $content = Yii::$app->controller->renderPartial('_customer_report_pdf', ['report' => $final_details_arr, 'model' => $model, 'FromDate' => $FromDate, 'ToDate' => $ToDate, 'ReportName' => 'Customer Report'], false);
        Yii::$app->response->format = Response::FORMAT_RAW;
        $pdf = new Pdf([
            // set to use core fonts only
            'mode' => Pdf::MODE_CORE,
            // A4 paper format
            'format' => Pdf::FORMAT_A4,
            // portrait orientation
            'orientation' => Pdf::ORIENT_PORTRAIT,
            // stream to browser inline
            'destination' => Pdf::DEST_BROWSER,
            // your html content input
            'content' => $content,
            // format content from your own css file if needed or use the
            // enhanced bootstrap css built by Krajee for mPDF formatting
            'cssFile' => '@backend/web/css/report-style.css',
            // any css to be embedded if required
            // 'cssInline' => '.kv-heading-1{font-size:18px}',
            // set mPDF properties on the fly
            //  'options' => ['title' => 'Customer Report'],
            // call mPDF methods on the fly
            'methods' => [
                'SetHeader' => ['Customer Report'],
                'SetFooter' => ['Page No : {PAGENO}'],
            ]
        ]);
        return $pdf->render();
    }

    /**
     * Displays a single Customer model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $id = base64_decode($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the Customer model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Customer the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Customer::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Creates a new Customer model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Customer();
        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }
        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'profilePic')){
                $model->profileImage = UploadedFile::getInstance($model, 'profilePic');
            }else{
                unset($model->profilePic);
            }
            $model->addedBy = !empty($_POST['Customer']['addedBy']) ? $_POST['Customer']['addedBy'] : Yii::$app->user->id;
            $model->businessId = $this->businessId;
            //$model->generateAccessToken();
            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Customer added successfully!');
                return $this->redirect(['update', 'id' => base64_encode($model->id)]);
            }else{
                Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
            }
        }
        return $this->render('create', [
            'model' => $model,
            'subUserList' => $this->getSubUserList()
        ]);
    }

    public function getSubUserList()
    {
        /** @var User $currentUser */
        $currentUser = Yii::$app->user->identity;
        $userQuery = User::find()->select(["id", "name As fullName"])->where(['isDeleted' => 0])->andWhere('1=0');

        if (!$currentUser->isAdmin) {
            $userIds = $currentUser->getSubIds();
            array_push($userIds, Yii::$app->user->id);
            $userQuery->andWhere(['in', 'id', $userIds]);
        }
        $allSubUsers = $userQuery->orderBy('fullName ASC')->asArray()->all();
        return ArrayHelper::map($allSubUsers, 'id', 'fullName');
    }

    /**
     * Updates an existing Customer model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $id = base64_decode($id);
        $model = $this->findModel($id);
        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }
        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'profilePic')){
                $model->profileImage = UploadedFile::getInstance($model, 'profilePic');
            }else{
                unset($model->profilePic);
            }
            $model->addedBy = !empty($_POST['Customer']['addedBy']) ? $_POST['Customer']['addedBy'] : Yii::$app->user->id;
            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Customer updated successfully!');
            }else{
                Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
            }
        }

        return $this->render('update', [
            'model' => $model,
            'subUserList' => $this->getSubUserList()
        ]);
    }

    /**
     * Deletes an existing Customer model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');
        $id = base64_decode($id);
        Yii::$app->session->setFlash('success', 'Customer deletd successfully!');
        return $this->findModel($id)->softDelete();
    }

    public function actionActive()
    {
        $id = $_POST['id'];
        $id = base64_decode($id);
        $user = $this->findModel($id);
        if ($user->isDeleted == 1) {
            $user->isDeleted = 0;
            $user->deletedAt = NULL;
            if ($user->save(false)) {
                Yii::$app->session->setFlash('success', 'Successfully Activate Customer');
            }
        } else {
            $user->isDeleted = 1;
            $user->deletedAt = new Expression('NOW()'); // log the deletion date
            if ($user->save(false)) {
                Yii::$app->session->setFlash('success', 'Successfully Deactivate Customer');
            }
        }
    }

    public function actionList()
    {
        $search = Yii::$app->request->post('search');
        $data = Customer::findActive()->where(['businessId'=>Business::getBusiness()->id]);
        if (!empty($search)) {
            $data->where(['or', ['like', 'companyName', $search . '%', false], ['like', 'name', $search . '%', false]]);
        }
        $data = $data->asArray()->all();
        return json_encode(["data" => $data]);
    }

    public function actionGetCustomer()
    {
        $id = Yii::$app->request->post('id');
        $data = Customer::find()->where(['id' => $id])->one();
        return Json::encode(["data" => $data]);
    }
}
