<?php

namespace backend\controllers;

use backend\models\BusinessSearch;
use common\models\Business;
use Yii;
use yii\filters\VerbFilter;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

/**
 * BusinessController implements the CRUD actions for Business model.
 */
class BusinessController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Business models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BusinessSearch();
        $searchModel->id = $this->businessId;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->sort->defaultOrder = ['id' => SORT_DESC];

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'selectedBusinessId' => $this->businessId,
        ]);
    }

    /**
     * Displays a single Business model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView(int $id)
    {
        $id = base64_decode($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the Business model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Business the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Business::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Creates a new Business model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Business();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            Yii::$app->session->setFlash('success', 'Business added successfully!');
            return $this->redirect(['edit', 'id' => base64_encode($model->id)]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    public function actionChange($businessId, $fromAjax = false)
    {
        $session = Yii::$app->session;
        $session->set('businessId', $businessId);
        if ($fromAjax) {
            echo $businessId;
            exit();
        }
        $this->redirect('/site/index');
    }

    public function actionReset()
    {
        $session = Yii::$app->session;
        $session->remove('businessId');
        $this->redirect('/site/index');
    }

    /**
     * Updates an existing Business model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $id = base64_decode($id);
        $model = $this->findModel($id);
        if ($model->load(Yii::$app->request->post())) {
            $model->imageFile = UploadedFile::getInstance($model, 'imageFile');
            $model->signatureImgFile = UploadedFile::getInstance($model, 'signatureImgFile');
            $model->upiQrImageFile = UploadedFile::getInstance($model, 'upiQrImageFile');
            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Business updated successfully!');
                return $this->redirect(['update', 'id' => base64_encode($model->id)]);
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Business model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');
        $id = base64_decode($id);
        Yii::$app->session->setFlash('success', 'Business deleted successfully!');
        return $this->findModel($id)->softDelete();
    }
}
