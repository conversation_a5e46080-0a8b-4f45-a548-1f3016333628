<?php

namespace backend\controllers;

use backend\models\ProductCategorySearch;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use common\models\Business;
use common\models\enum\Key;
use common\models\ProductCategory;
use kartik\mpdf\Pdf;
use Yii;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * ProductCategoryController implements the CRUD actions for ProductCategory model.
 */
class ProductCategoryController extends BaseController
{

    public int $isInventoryMode;
    public int $useSeparateInventoryItems;
    public int $isInventoryEnabled;
    public function init()
    {
        parent::init();
        $this->isInventoryEnabled = Business::getConfig(Key::GROUP_APP, Key::IS_INVENTORY_ENABLED);
        $this->useSeparateInventoryItems = $this->isInventoryEnabled && Business::getConfig(Key::GROUP_CUSTOM, Key::useSeparateInventoryItems);
        $this->isInventoryMode = 0;
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    // allow authenticated users
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'actions' => ['index', 'view', 'create', 'update', 'active', 'delete'],
                    ],
                    // deny all guest users
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'actions' => ['index', 'view'],
                        'denyCallback' => function ($rule, $action) {
                            return $this->redirect(Yii::$app->urlManager->createUrl('site/index'));
                        },
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all ProductCategory models.
     * @return mixed
     */
    public function actionIndex()
    {
        $model = new ProductCategory();
        $searchModel = new ProductCategorySearch();
        $searchModel->businessId = $this->businessId;
        $searchModel->useSeparateInventoryItems = $this->useSeparateInventoryItems;
        $searchModel->isInventoryMode = $this->isInventoryMode;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ProductCategory model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $id = base64_decode($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the ProductCategory model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ProductCategory the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ProductCategory::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Creates a new ProductCategory model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ProductCategory();
        $model->businessId = $this->businessId;
        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'image')){
                $model->coverImage = UploadedFile::getInstance($model, 'image');
            }else{
                unset($model->image);
            }

            if ($model->save(false)) {
                Yii::$app->session->setFlash('success', 'Product Category added successfully!');
                return $this->redirect(['update', 'id' => base64_encode($model->id)]);
            }else{
                Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
            }

        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ProductCategory model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $id = base64_decode($id);
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'image')){
                $model->coverImage = UploadedFile::getInstance($model, 'image');
            }else{
                unset($model->image);
            }
            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Product Category updated successfully!');
            }else{
                Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
            }

        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ProductCategory model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');
        $id = base64_decode($id);
        Yii::$app->session->setFlash('success', 'Product Category deleted successfully!');
        return $this->findModel($id)->softDelete();
    }

    public function actionActive()
    {
        $id = $_POST['id'];
        $id = base64_decode($id);
        $user = $this->findModel($id);
        if ($user->isDeleted == 1) {
            $user->isDeleted = 0;
            $user->deletedAt = NULL;
            if ($user->save(false)) {
                Yii::$app->session->setFlash('success', 'Product Category Activated successfully!');
            }
        } else {
            $this->findModel($id)->softDelete();
            Yii::$app->session->setFlash('success', 'Product Category Deactivated successfully!');
        }
    }
}
