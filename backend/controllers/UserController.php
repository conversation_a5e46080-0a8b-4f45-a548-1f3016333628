<?php

namespace backend\controllers;

use backend\models\ResetPasswordForm;
use backend\models\UserSearch;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use common\models\Business;
use common\models\User;
use common\models\UserBusiness;
use kartik\form\ActiveForm;
use kartik\mpdf\Pdf;
use Yii;
use yii\base\InvalidArgumentException;
use yii\db\Expression;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * UserController implements the CRUD actions for User model.
 */
class UserController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    // allow authenticated users
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'actions' => ['index', 'view', 'create', 'update', 'lists', 'active', 'lists', 'delete', 'change-password'],
                    ],
                    // deny all guest users
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'actions' => ['index', 'view'],
                        'denyCallback' => function ($rule, $action) {
                            return $this->redirect(Yii::$app->urlManager->createUrl('site/index'));
                        },
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all User models.
     * @return mixed
     */
    public function actionIndex()
    {
        $model = new User();
        $searchModel = new UserSearch();
        $searchModel->businessId = $this->businessId;

        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->sort->defaultOrder = ['id' => SORT_DESC];

        if ($model->load(Yii::$app->request->get())) {

            $date_range_arr = explode(' - ', $_GET['User']['DateRange']);
            $model->DateRange = $_GET['User']['DateRange'];
            $FromDate = Yii::$app->common->formatDate($date_range_arr[0], 2);
            $ToDate = Yii::$app->common->formatDate($date_range_arr[1], 2);
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
            $final_details_arr = $dataProvider->getModels();
            if (!empty($final_details_arr)) {

                if (isset($_GET['btnReport']) && $_GET['btnReport'] == 'excel') {
                    $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
                    $dataProvider->pagination = false;
                    $final_details_arr = $dataProvider->getModels();
                    $style = array();
                    $timpstamp = Yii::$app->user->id . time();
                    $writer = WriterFactory::create(Type::XLSX); // for XLSX files

                    $style = (new StyleBuilder())->setFontName('Arial')->setFontSize(11)->build();
                    $defaultStyle = (new StyleBuilder())->setFontName('Arial')->setFontSize(11)->build();
                    $FilePath = UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_user_report_excel' . ".xlsx";

                    $writer->setDefaultRowStyle($defaultStyle)->openToFile($FilePath); // write data to a file or to a PHP stream
                    $writer->addRow(['Report Name', 'User Report'], $style);
                    $writer->addRow(['From Date:', date('d/m/Y', strtotime($FromDate))], $style);
                    $writer->addRow(['To Date:', date('d/m/Y', strtotime($ToDate))], $style);
                    $writer->addRow([''], $style);
                    $writer->addRow(['Serial No', 'ID', 'First Name', 'Last Name', 'Email', 'From Email', 'Phone Number', 'Address Line1', 'Profile Pic', 'Designation', 'Country', 'State', 'City', 'Pincode', 'Parent User', 'Status', 'IS Admin', 'Is Deleted', 'Created At', 'Updated At', 'Deleted At'], $style);
                    $i = 1;
                    foreach ($final_details_arr as $key => $val) {
                        $writer->addRow([$i
                            , $val['id']
                            , !empty($val['name']) ? $val['name'] : '-'
                            , !empty($val['email']) ? $val['email'] : '-'
                            , !empty($val['fromEmail']) ? $val['fromEmail'] : '-'
                            , !empty($val['phoneNumber']) ? $val['phoneNumber'] : '-'
                            , !empty($val['addressLine1']) ? $val['addressLine1'] : '-'
                            , !empty($val['profilePic']) ? $val['profilePic'] : '-'
                            , !empty($val['designation']) ? $val['designation'] : '-'
                            , !empty($val['country']) ? $val['country'] : '-'
                            , !empty($val['state']) ? $val['state'] : '-'
                            , !empty($val['city']) ? $val['city'] : '-'
                            , !empty($val['pincode']) ? $val['pincode'] : '-'
                            , !empty(Yii::$app->common->getParentName('USER', $val['parentId'])) ? Yii::$app->common->getParentName('USER', $val['parentId']) : '-'
                            , !empty($val['status']) ? $val['status'] : '-'
                            , Yii::$app->common->displayLabel('isAdmin', $val['isAdmin'])
                            , Yii::$app->common->displayLabel('isDeleted', $val['isDeleted'])
                            , !empty($val['createdAt']) ? $val['createdAt'] : '-'
                            , !empty($val['updatedAt']) ? $val['updatedAt'] : '-'
                            , !empty($val['deletedAt']) ? $val['deletedAt'] : '-'
                        ], $style);

                        $i++;
                    }
                    $writer->close();
                    @chmod(UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_user_report_excel' . ".xlsx", 0777);
                    Yii::$app->response->sendFile($FilePath);
                    unlink(UPLOAD_DIR_PATH . "temp/" . $timpstamp . '_user_report_excel' . ".xlsx");
                }
                if (isset($_GET['btnReport']) && $_GET['btnReport'] == 'pdf') {
                    $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
                    $dataProvider->pagination = false;
                    $final_details_arr = $dataProvider->getModels();

                    $content = Yii::$app->controller->renderPartial('_user_report_pdf', ['report' => $final_details_arr, 'model' => $model, 'FromDate' => $FromDate, 'ToDate' => $ToDate, 'ReportName' => 'User Report'], false);
                    Yii::$app->response->format = Response::FORMAT_RAW;
                    $pdf = new Pdf([
                        // set to use core fonts only
                        'mode' => Pdf::MODE_CORE,
                        // A4 paper format
                        'format' => Pdf::FORMAT_A4,
                        // portrait orientation
                        'orientation' => Pdf::ORIENT_PORTRAIT,
                        // stream to browser inline
                        'destination' => Pdf::DEST_BROWSER,
                        // your html content input
                        'content' => $content,
                        // format content from your own css file if needed or use the
                        // enhanced bootstrap css built by Krajee for mPDF formatting
                        'cssFile' => '@backend/web/css/report-style.css',
                        // any css to be embedded if required
                        // 'cssInline' => '.kv-heading-1{font-size:18px}',
                        // set mPDF properties on the fly
                        //  'options' => ['title' => 'Customer Report'],
                        // call mPDF methods on the fly
                        'methods' => [
                            'SetHeader' => ['User Report'],
                            'SetFooter' => ['Page No : {PAGENO}'],
                        ]
                    ]);
                    return $pdf->render();
                }
            }
        }
        return $this->render('index', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'selectedBusinessId' => $this->businessId,
        ]);

    }

    /**
     * Displays a single User model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $id = base64_decode($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the User model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return User the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected
    function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Creates a new User model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $business = Business::getBusiness();
        if (!$business?->canAddNewUser()) {
            Yii::$app->session->setFlash('error', "You can not add new User! Please contact developer to increase your user limit");
            return $this->goBack();
        }

        $model = new User();
        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }
        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'profilePic')) {
                $model->profileImage = UploadedFile::getInstance($model, 'profilePic');
            } else {
                unset($model->profilePic);
            }
            if (UploadedFile::getInstance($model, 'signatureImg')) {
                $model->signatureImgFile = UploadedFile::getInstance($model, 'signatureImg');
            } else {
                unset($model->signatureImg);
            }

            $randomPassword = $model->setRandomPassword();
            $model->status = User::STATUS_ACTIVE;
            if (empty($model->parentId) || $model->parentId == 0) {
                $model->parentId = $this->businessUserId;
            }
            $model->generateAccessToken();
            $model->regionCode = $business?->regionCode;
            if (!empty($model->phoneNumber)) {
                $model->isPhoneVerified = 1;
            }
            if ($model->save()) {
                if ($this->businessId) {
                    $userBusiness = new UserBusiness();
                    $userBusiness->userId = $model->id;
                    $userBusiness->businessId = $this->businessId;
                    $userBusiness->isOwner = 0;
                    $userBusiness->save();
                }
                if ($model->email) {
                    $model->sendEmailTempPassword($randomPassword);
                    Yii::$app->session->setFlash('success', 'User added successfully!');
                    return $this->redirect(['update', 'id' => base64_encode($model->id)]);
                }
            }
            if ($model->hasErrors()) {
                Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
            }
        }

        return $this->render('create', ['model' => $model,]);
    }

    /**
     * Updates an existing User model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $id = base64_decode($id);
        $model = $this->findModel($id);
        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }
        if ($model->load(Yii::$app->request->post())) {
            if (UploadedFile::getInstance($model, 'profilePic')) {
                $model->profileImage = UploadedFile::getInstance($model, 'profilePic');
            } else {
                unset($model->profilePic);
            }
            if (UploadedFile::getInstance($model, 'signatureImg')) {
                $model->signatureImgFile = UploadedFile::getInstance($model, 'signatureImg');
            } else {
                unset($model->signatureImg);
            }
            if ($model->parentId == 0) {
                $model->parentId = '';
            }

            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'User updated successfully!');
                return $this->redirect(['update', 'id' => base64_encode($model->id)]);
            }
        }
        if ($model->hasErrors()) {
            Yii::$app->session->setFlash('error', $model->getErrorSummary(true)[0]);
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing User model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');
        $id = base64_decode($id);
        if ($this->findModel($id)->softDelete()) {
            Yii::$app->session->setFlash('success', 'User deleted successfully!');
            return 1;
        }
        return 0;
    }

    public function actionLists($designation)
    {
        $searchTearm = '';
        $models = [];
        if ($designation == 'sales_manager') {
            $searchTearm = 'sales_director';
        }
        if ($designation == 'sales_person') {
            $searchTearm = 'sales_manager';
        }
        if ($searchTearm != '') {
            $models = User::find()->select(["id", "name As fullName"])->where(['isDeleted' => 0])->andWhere('1=0')->andFilterWhere(['like', 'designation', $searchTearm])->orderBy('fullName ASC')->asArray()->all();
        }
        echo "<option>-Choose parent-</option>";
        if (sizeof($models) > 0) {
            foreach ($models as $model) {
                echo "<option value='" . $model['id'] . "'>" . $model['fullName'] . "</option>";
            }
        }

    }

    public function actionActive()
    {
        $id = $_POST['id'];
        $id = base64_decode($id);
        $user = $this->findModel($id);
        if ($user->isDeleted == 1) {
            $user->isDeleted = 0;
            $user->deletedAt = NULL;
            if ($user->save(false)) {
                Yii::$app->session->setFlash('success', 'Successfully Activate User');
            }
        } else {
            $user->isDeleted = 1;
            $user->deletedAt = new Expression('NOW()'); // log the deletion date
            if ($user->save(false)) {
                Yii::$app->session->setFlash('success', 'Successfully Deactivate User');
            }
        }
    }

    /**
     * Resets password.
     *
     * @return mixed
     * @throws BadRequestHttpException
     */
    public function actionChangePassword($id)
    {
        $id = base64_decode($id);
        $user = $this->findModel($id);
        try {
            $model = new ResetPasswordForm($user);
        } catch (InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->resetPassword()) {
            Yii::$app->session->setFlash('success', 'New password saved.');
            return $this->goHome();
        }

        return $this->render('resetPassword', [
            'model' => $model,
        ]);
    }


}
