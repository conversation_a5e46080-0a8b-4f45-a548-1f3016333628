<?php

namespace api\modules\v1\controllers;

use Carbon\Carbon;
use common\helpers\GoogleContactHelper;
use common\models\AppSettings;
use common\models\User;
use common\services\EmailClient;
use Exception;
use yii;

/**
 * Api controller
 */
class  ApiController extends BaseApiController
{
    public function actionTest()
    {
        $request = Yii::$app->request;
        $id = $request->post('id');
        $testUser = User::findByPk($id);
        $response = [
            'appName' => env(appName),
            'APP_NAME' => APP_NAME,
            'testUser' => $testUser,
        ];
        $message = 'Test API response!';
        return $this->_sendResponse($response, $message);
    }

    public function actionTestEmail()
    {

        $request = Yii::$app->request;
        $email = $request->post('email');

        $now = new Carbon();
        /** @var yii\symfonymailer\Mailer $mailer */
        $mailer = EmailClient::getInstance()->getSmtpMailer();
        $errors = null;
        try {
            $mailSuccess = $mailer->compose(['html' => 'testMail-html'])
                ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']])
                ->setTo($email)
                ->setSubject(Yii::$app->name . " - test mail " . $now)
                ->send();
            $message = "email sent successfully";
            return $this->_sendResponse(['mailSuccess' => $mailSuccess, 'errors' => $errors], $message);
        } catch (Exception $e) {
            $errors = $e->getMessage();
            Yii::error($e);
            $mailSuccess = false;
            return $this->_sendErrorResponse($e->getCode(), $e->getMessage());
        }
    }

    public function actionTestNotification()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        try {
            $userId = $request->post('userId');
            $txtMessage = $request->post('message');
            $testUser = User::findByPk($userId);
            $testUser->sendNotification($txtMessage);
            $message = "Notification to user sent successfully";
            return $this->_sendResponse([], $message);
        } catch (Exception $e) {
            Yii::error($e);
            return $this->_sendErrorResponse($e->getCode(), $e->getMessage());
        }
    }

    public function actionAddGoogleContact()
    {
        $name = Yii::$app->request->post('name');
        $phone = Yii::$app->request->post('phone');

        $message = "Contact added successfully";
        // Call the helper function
        try {
            $message = GoogleContactHelper::addContactToGoogle($name, $phone);
        } catch (Exception $e) {
            $this->_sendErrorResponse($e->getCode(), $e->getMessage());
        }
        // Return a response to the client
        return $this->_sendResponse(Yii::$app->request->post(), $message);
    }


    public function actionSyncData($lastTimestamp = 0)
    {

        //  App Settings
        $appSettings = AppSettings::find()
            ->where('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->all();

        $response['appSettings'] = [];
        if (count($appSettings) > 0) {
            $response['appSettings'] = $appSettings;
        }

        $message = "Synchronize data sent successfully";
        return $this->_sendResponse($response, $message);
    }


}