<?php

namespace api\modules\v1\controllers;

use common\models\ProductCategory;
use common\models\User;
use Yii;
use yii\web\UploadedFile;

class ProductCategoryController extends BaseApiController
{
    public function actionUpdate()
    {
        $this->actionCreate(true);
    }

    public function actionCreate($isUpdate = false)
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;

        $categoryId = $request->post('id');
        $newCategory = ProductCategory::findByPk($categoryId);
        if ($newCategory == null) {
            $newCategory = new ProductCategory();
            $newCategory->businessId = $this->business->id;
        }

        $newCategory->setAttributes($_POST);  // load all attributes (in new User model)
        $newCategory->coverImage = UploadedFile::getInstanceByName('image');

        if (!$newCategory->validate()) {
            $errors = $newCategory->getErrorSummary(true)[0];
            return $this->_sendErrorResponse(200, $errors, 101);
        }

        $newCategory->save();  // save new product
        $newCategory->refresh();
        $response = ['category' => $newCategory];
        $message = "New category created successfully!";
        if ($isUpdate) {
            $message = "Category updated successfully!";
        }
        return $this->_sendResponse($response, $message);
    }

    public function actionRemove()
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;

        $categoryId = $request->post('id');

        if ($categoryId == null) {
            return $this->_sendErrorResponse(200, 'input param(s) missing!', 101);
        }

        $category = ProductCategory::findByPk($categoryId);
        if ($category == null) {
            return $this->_sendErrorResponse(200, 'Invalid category id!', 101);
        }

        $category->softDelete(); // remove product option

        $response = ['categoryId' => $category->id];
        $message = "Category removed successfully!";
        return $this->_sendResponse($response, $message);
    }


    public function actionList($offset = 0, $limit = null, $orderBy = "c.id", $order = "desc")
    {
        $salesUser = $this->_checkAuth();
        $offset = Yii::$app->request->get('offset', 0);
        $order = strcasecmp($order, 'desc') ? SORT_ASC : SORT_DESC;

        if ($limit == null) {
            $limit = Yii::$app->params['records_limit'];
        }
        $findQuery = ProductCategory::find();
        $findQuery->byBusiness($this->business->id);
        $findQuery->alias('c');

        $findQuery->orderBy([$orderBy => $order]);

        $findQuery->offset($offset);
        $findQuery->limit($limit);
        $sql = $findQuery->createCommand()->rawSql;
        $arrList = $findQuery->asArray()->all();

        // TODO Access control query - as per sub-level (pid)

        $p['offset'] = $offset;
        $p['limit'] = $limit;
        $p['record_sent'] = count($arrList);

        $response = ['categoryList' => $arrList, 'pagination' => $p, 'sql' => $sql];
        $message = "Category list sent successfully";
        return $this->_sendResponse($response, $message);
    }


}