<?php

namespace api\modules\v1\controllers;

use api\modules\v1\models\ReceiptListing;
use common\helpers\FileManager;
use common\models\enum\Key;
use common\models\Receipt;
use common\models\User;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class ReceiptController extends BaseApiController
{
    public function actionList()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $params = Yii::$app->request->get();

        $receiptListing = new ReceiptListing();
        $receiptListing->setAttributes($params);
        $receiptListing->currentUser = $user;
        $receiptListing->businessId = $this->business->id;

        if (!$receiptListing->validate()) {
            $this->_sendErrorResponse(200, $receiptListing->getErrorSummary(true)[0], 101);
        }

        $response = [
            'receiptList' => $receiptListing->search(),
            'pagination' => $receiptListing->pagination(),
        ];

        if (isset($params['debug'])) {
            $response['sql'] = $receiptListing->sql;
        }

        $message = "Receipt list sent successfully";
        return $this->_sendResponse($response, $message);
    }

    public function actionSync($lastTimestamp = 0)
    {
        $salesUser = $this->_checkAuth();

        $findQuery = Receipt::find();
        $findQuery->andWhere('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->andWhere(['isDeleted' => 0]);
        $findQuery->andWhere(['businessId' => $this->business->id]);
        if ($this->identity->isUser() && !$salesUser->isAdmin) {
            $subUserIds = $salesUser->getSubIds();
            $findQuery->andWhere(['in', 'createdById', $subUserIds]);
        }

        $arrList = $findQuery->all();
        $rAdd['receiptList'] = $arrList;
        $response['add'] = $rAdd;
        if ($lastTimestamp != 0) {
            //  Receipt
            $receiptList = Receipt::find()->select(['id'])
                ->where('deletedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->andWhere(['isDeleted' => 1])->andWhere(['businessId' => $this->business->id])
                ->all();

            $rRemove['receiptIds'] = ArrayHelper::getColumn($receiptList, 'id');
            $response['remove'] = $rRemove; // Removed data list
        }

        $message = "Receipt Sync list sent successfully";
        return $this->_sendResponse($response, $message);
    }

    public function actionUpdate()
    {
        $this->actionCreate(true);
    }

    public function actionRemove()
    {
        /** @var User $user */
        $user = $this->_checkAuth();

        $request = Yii::$app->request;
        $receiptId = $request->post('id');
        /** @var User $user */
        $receipt = Receipt::findByPk($receiptId);
        if ($receipt == null) {
            return $this->_sendErrorResponse(200, 'Invalid receipt id', 101);

        }
        if (!$user->isAdmin && $receipt->createdById != $user->id) {
            return $this->_sendErrorResponse(200, 'only owner has rights to perform this action', 101);
        }

        if ($receipt->remove()) {
            $response = ['receiptId' => $receipt->id];
            $message = 'Receipt removed successfully!';
            return $this->_sendResponse($response, $message);
        }
        return $this->_sendErrorResponse(200, "Server side issues!", 105); // user doesn't exits!
    }


    public function actionCreate($isUpdate = false)
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $isSkipSubscriptionVerification = \toBool($request->post(Key::isSkipSubscriptionVerification, false));

        if (!$isSkipSubscriptionVerification && !$salesUser->business->canGenerateReceipt()) {
            return $this->_sendErrorResponse(200, "Your free usage limit is over! Please subscribe to premium plan!", 402);
            // TODO : Your free usage limit is over! Please Update your app to latest version OR subscribe to premium plan!
        }

        try {
            $newReceipt = Receipt::CreateOrUpdate($request, $salesUser);
        } catch (Exception | \Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), $e->getCode());
        }
        $newReceipt->refresh();
        $salesUser->refresh();
        $businessStats = $salesUser->businessStats;
        $response = ['receipt' => $newReceipt, 'pdfFileBase64' => $newReceipt->pdfFileBase64,
            'totalUsageCount' => $businessStats->totalUsageCount,
            'totalFreeUsageCount' => $businessStats->totalFreeUsageCount,
            'receiptFreeUsageLimit' => $businessStats->receiptFreeUsageLimit,
            'receiptCount' => $businessStats->receiptCount,
            ];

        $message = $isUpdate ? "Receipt updated successfully!" : "New receipt created successfully!";

        return $this->_sendResponse($response, $message);
    }

    public function actionGeneratePdf()
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $debug = $request->get('debug', false);
        $quoteId = $request->get('id');
        $newReceipt = Receipt::findByPk($quoteId);
        if ($newReceipt == null) {
            return $this->_sendErrorResponse(200, 'Invalid receipt id', 101);
        }

        if ($newReceipt->pdfFileUrl) {
            $pdfFile = file_get_contents($newReceipt->getPdfFilePath());
            $newReceipt->pdfFileBase64 = base64_encode($pdfFile);
            $response = ['receipt' => $newReceipt, 'pdfFileBase64' => $newReceipt->pdfFileBase64];
            $message = "Receipt generated successfully!";
            return $this->_sendResponse($response, $message);
        }


        $newReceipt->generatePDF(false);

        $response = [];
        if ($debug) {
            $relPath = sprintf(RECEIPT_DIR, $newReceipt->businessId, date('Y'), date('m'));

            // Save PDF file
            $pdfFileUrl = FileManager::saveFileContent($newReceipt->pdfOutputData, $relPath, $newReceipt->pdfFileName);

            // Save HTML file
            $htmlFileName = basename($newReceipt->pdfFileName, ".pdf") . ".html";
            $htmlFileUrl = FileManager::saveFileContent($newReceipt->pdfHtmlContent, $relPath, $htmlFileName);

            $response = ['pdfFileUrl' => $pdfFileUrl, 'htmlFileUrl' => $htmlFileUrl];
        }

        $response = array_merge(
            $response,
            ['receipt' => $newReceipt, 'pdfFileBase64' => $newReceipt->pdfFileBase64]
        );
        $message = "Receipt generated successfully!";
        return $this->_sendResponse($response, $message);
    }

    public function actionSendPdfMail()
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;

        $quoteId = $request->get('id');
        $newReceipt = Receipt::findByPk($quoteId);
        if ($newReceipt == null) {
            return $this->_sendErrorResponse(200, 'Invalid receipt id', 101);
        }

        if (!$newReceipt->sendMailToCustomer()) {
            return $this->_sendErrorResponse(200, 'There is server side issue in sending email! Please try again later!', 105);
        }

        $response = ['receipt' => $newReceipt];
        $message = "Receipt mail has been sent successfully!";
        return $this->_sendResponse($response, $message);
    }


    public function actionDetail($id)
    {
        $salesUser = $this->_checkAuth();
        $findQuery = Receipt::find();
        $findQuery->alias('q');
        $findQuery->select(['q.*', 'c.name as customer_name',
            'u.name as salesUser_name',
        ]);

        $findQuery->andWhere(['q.id' => $id]);
        $findQuery->joinWith('customer c');
        $findQuery->joinWith('createdBy u');
        $findQuery->joinWith('receiptItems');

        $sql = $findQuery->createCommand()->rawSql;
        $receipt = $findQuery->asArray()->one();

        if ($receipt == null) {
            return $this->_sendErrorResponse(200, 'Invalid receipt id', 101);
        }

        $response = ['receipt' => $receipt];
        $message = "Receipt detail sent successfully";
        return $this->_sendResponse($response, $message);
    }


    public function actionUpdateStatus($userId = null)
    {
        if ($userId) {
            /** @var User $salesUser */
            $salesUser = User::findByPk($userId);
        } else {
            /** @var User $salesUser */
            $salesUser = $this->_checkAuth();
        }

        $request = Yii::$app->request;

        $quoteId = $request->post('id');
        $status = $request->post('status');
        $statusText = $request->post('statusText');

        if ($quoteId == null || $status == null) {
            return $this->_sendErrorResponse(200, 'Missing input params!', 101);
        }

        $receipt = Receipt::findByPk($quoteId);
        if ($receipt == null) {
            return $this->_sendErrorResponse(200, 'Invalid receipt id', 101);
        }

        $receipt->setAttributes($_POST);  // load all attributes (in new User model)

//        $receipt->status = Receipt::STATUS_NEW;

        if (!$receipt->validate()) {
            $errors = $receipt->getErrorSummary(true)[0];
            return $this->_sendErrorResponse(200, $errors, 101);
        }
        if (!$receipt->save()) {
            return $this->_sendErrorResponse(200, $receipt->getErrorSummary(true)[0], 101);
        }

        $response = ['receipt' => $receipt];
        $message = "Receipt status updated successfully!";
        return $this->_sendResponse($response, $message);
    }


}