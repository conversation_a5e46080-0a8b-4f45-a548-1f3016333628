<?php

namespace api\modules\v1\controllers;

use common\models\AppSettings;
use common\models\BusinessCategory;
use common\models\enum\Key;
use common\models\Region;
use common\models\States;
use common\models\User;
use Yii;

/**
 * App controller
 */
class  AppController extends BaseApiController
{
    public function actionSyncData($lastTimestamp = 0)
    {
        $this->_logUserActiveTime();
        //  App Settings
        $appSettings = AppSettings::find()
            ->where('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->all();

        $response['appSettings'] = [];
        if (count($appSettings) > 0) {
            $response['appSettings'] = $appSettings;
        }

        if ($lastTimestamp  == 0) {
            //  Region Table
            $regionList = Region::find()
                ->where('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->all();

            $response['regionList'] = [];
            if (count($regionList) > 0) {
                $response['regionList'] = $regionList;
            }

            //  States Table
            $stateList = States::find()
                ->where('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->all();

            $response['stateList'] = [];
            if (count($stateList) > 0) {
                $response['stateList'] = $stateList;
            }

            //  States Table
            $businessCategoryList = BusinessCategory::find()
                ->where('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->all();

            $response['businessCategoryList'] = [];
            if (count($businessCategoryList) > 0) {
                $businessCategoryList = BusinessCategory::find()->where(['isDeleted' => 0])->all();
                $response['businessCategoryList'] = $businessCategoryList;
            }
        }
        $message = "Synchronize data sent successfully";
        $this->disableNumericCheck = true;
        return $this->_sendResponse($response, $message);
    }

    public function actionDebugLog()
    {
        /** @var User $user */
        $user = $this->_checkAuthSafely();
        $request = Yii::$app->request;
        $message = $request->post('message');
        $data = $request->post('data');

        logMail("Debug Log -  {$this->user?->id}", [
            'message' => $message,
            'data' => $data,
            'userId' => $this->user?->id,
            'businessId' => $this->business?->id,
            'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'],
            'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'],
            'HTTP_UUID' => $_SERVER['HTTP_UUID'],
            'HTTP_AUTH_TOKEN' => $_SERVER['HTTP_AUTH_TOKEN'],
            'REQUEST_URI' => $_SERVER['REQUEST_URI'],
        ],
            $message,
            env(logUserIdsEmail) ?? null,
        );
        $this->response->data = [
            'success'=>true,
        ];
        $this->response->statusCode = 200;
        $this->response->send();
    }

}