<?php

namespace api\modules\v1\controllers;

use Carbon\Carbon;
use common\helpers\GoogleBillingHelper;
use common\models\enum\Key;
use common\models\enum\ProviderType;
use common\models\Subscription;
use common\models\SubscriptionPlan;
use common\models\User;
use JsonException;
use Yii;
use yii\base\Exception;

class GoogleSubscriptionController extends BaseApiController
{

    public function actionGeneratePaymentIntent()
    {
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $planId = $request->post('planId');
        $plan = SubscriptionPlan::findByPk($planId);

        if ($plan === null || $plan->googlePlanId === null) {
            $message = "Invalid plan id sent!";
            return $this->_sendErrorResponse(200, $message, 406);
        }

        $isSent = Subscription::SendPaymentIntentEmailNotification($user, $plan, ProviderType::GOOGLE);

        $response = [
            'isMailSent' => $isSent,
        ];
        $message = "Google Subscription Payment Intent Created!";
        return $this->_sendResponse($response, $message);
    }
    public function actionTestPurchaseToken()
    {
        /** @var User $salesUser */
        $this->_checkAuth();
        $request = Yii::$app->request;
        $productId = $request->post('productId');
        $purchaseToken = $request->post('purchaseToken');
        $isRecurring = $request->post('isRecurring');

        if (empty($productId) || empty($purchaseToken)) {
            return $this->_sendErrorResponse(200, "Purchase data missing!", 406);
        }
        $debugData = GoogleBillingHelper::sharedInstance()->debugPurchaseToken($productId, $purchaseToken, $isRecurring);

        $response = $debugData;
        $message = "actionTestPurchaseToken!";
        return $this->_sendResponse($response, $message);
    }


    public function actionManualValidate()
    {
        /** @var User $salesUser */
        $this->_checkAuth();
        $request = Yii::$app->request;
        $userId = $request->post('userId');
        $productId = $request->post('productId');
        $purchaseToken = $request->post('purchaseToken');
        if (empty($productId) ||empty($userId) || empty($purchaseToken)) {
            return $this->_sendErrorResponse(200, "Purchase data missing!", 406);
        }
        $user = User::findByPk($userId);
        if ($user === null) {
            $message = "Invalid User id sent!";
            return $this->_sendErrorResponse(200, $message, 406);
        }
        if (env(Key::ENV) === DEV_MODE && !str_contains($productId, "test")) {
            return $this->_sendResponse([], "LIVE Mode Product ID Found In DEV Server, productId : {$productId}");
        }

        if (env(Key::ENV) === PROD_MODE && str_contains($productId, "test")) {
            return $this->_sendResponse([], "DEV Mode Product ID Found In LIVE Server, productId : {$productId}");
        }
        $plan = SubscriptionPlan::find()->byGooglePlanId($productId)->one();
        if ($plan === null) {
            $message = "Plan doesn't exist in our system! GooglePlay Plan ID : {$productId}";
            $data = [$productId,$purchaseToken];
            logError($message, $data);
            return $this->_sendResponse([], $message);
        }

        try {
            $subscription = GoogleBillingHelper::sharedInstance()->processSubscription($productId, $purchaseToken);
        } catch (Exception|\Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), 406);
        }
        $user->refresh();
        $response = [
            'subscription' => $subscription,
            'user' => $this->_userData($user),
        ];
        $message = "Plan subscribed successfully!";
        return $this->_sendResponse($response, $message);
    }


    public function actionValidate()
    {
        /** @var User $salesUser */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $purchaseData = $request->post('purchaseData');
        if (empty($purchaseData)) {
            return $this->_sendErrorResponse(200, "Purchase data missing!", 406);
        }
        $purchase = json_decode($purchaseData); // convert to object
        if (empty($purchase->productId) || empty($purchase->purchaseToken)) {
            return $this->_sendErrorResponse(200, "Invalid purchase data found!", 406);
        }

        if (env(Key::ENV) === DEV_MODE && !str_contains($purchase->productId, "test")) {
            return $this->_sendResponse([], "LIVE Mode Product ID Found In DEV Server, productId : {$purchase->productId}");
        }

        if (env(Key::ENV) === PROD_MODE && str_contains($purchase->productId, "test")) {
            return $this->_sendResponse([], "DEV Mode Product ID Found In LIVE Server, productId : {$purchase->productId}");
        }

        $plan = SubscriptionPlan::find()->byGooglePlanId($purchase->productId)->one();
        if ($plan === null) {
            $message = "Plan doesn't exist in our system! GooglePlay Plan ID : {$purchase->productId}";
            $data = $purchaseData;
            logError($message, $data);
            return $this->_sendResponse([], $message);
        }

        try {
            $subscription = GoogleBillingHelper::sharedInstance()->processSubscription($purchase->productId, $purchase->purchaseToken, $user);
        } catch (Exception|\Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), 406);
        }
        $user->refresh();
        $response = [
            'subscription' => $subscription,
            'user' => $this->_userData($user),
        ];
        $message = "Plan subscribed successfully!";
        return $this->_sendResponse($response, $message);
    }


    // google-subscription/acknowledge-subscription
    public function actionAcknowledgeSubscription()
    {
        /** @var User $salesUser */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $subscriptionId = $request->post('subscriptionId');
        if (empty($subscriptionId)) {
            return $this->_sendErrorResponse(200, "Subscription ID data missing!", 406);
        }

        try {
            $subscription = GoogleBillingHelper::sharedInstance()->acknowledgeSubscription($subscriptionId);
        } catch (Exception|\Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), 406);
        }
        $user->refresh();
        $response = [
            'subscription' => $subscription,
        ];
        $message = "Subscription Acknowledgement Processed Successfully!";
        return $this->_sendResponse($response, $message);
    }

// google-subscription/acknowledge-subscription
    public function actionConsumeProduct()
    {
        /** @var User $salesUser */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $productId = $request->post('productId');
        $purchaseToken = $request->post('purchaseToken');
        if (empty($productId) || empty($purchaseToken)) {
            return $this->_sendErrorResponse(200, "productId OR purchaseToken data missing!", 406);
        }

        try {
            $purchase = GoogleBillingHelper::sharedInstance()->setConsumable($productId, $purchaseToken);
        } catch (Exception|\Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), 406);
        }
        $response = [
            'purchase' => $purchase,
        ];
        $message = "Purchase is marked as Consumed Successfully!";
        return $this->_sendResponse($response, $message);
    }


    // apple-subscription/webhook

    /**
     * @throws Exception
     */
    public function actionWebhook()
    {
        $rawBody = Yii::$app->request->getRawBody();
//        $this->logIntoFile($rawBody, "Google Subscription Webhook Data");
        $rawData = json_decode($rawBody, true);

        if (empty($rawData['message']) && empty($rawData['message']['data'])) {
            Yii::error("Invalid notification data \n" . print_r($rawData, true));
            throw new Exception('Invalid notification data');
        }
        try {
            $payloadData = json_decode(base64_decode($rawData['message']['data']), true, 512, JSON_THROW_ON_ERROR);
            if (empty($payloadData['subscriptionNotification'])) {
                if (!empty($payloadData['voidedPurchaseNotification'])) {
                    $purchaseNotification = $payloadData['voidedPurchaseNotification'];
                    $purchaseToken = $purchaseNotification["purchaseToken"];
                    try {
                        $subscription = Subscription::getExistingSubscription($purchaseToken);
                    } catch (Exception $e) {
                        Yii::error($e->getMessage() . " \n\n" . print_r($payloadData, true) . "\n\n" . $e->getTraceAsString());
                        return $this->_sendResponse([], $e->getMessage());
                    }
                    if (empty($subscription)) {
                        Yii::error("voidedPurchaseNotification Issue (Purchase not found in Subscription table) :: \n\n" . print_r($payloadData, true));
                    }
                    return $this->_sendResponse([], "voidedPurchaseNotification Processed!");
                }
                return $this->_sendResponse([], "Ignoring Notification other than subscription notification!");
            }
        } catch (JsonException $e) {
            throw new Exception('Invalid notification data');
        }
        $subscriptionNotification = $payloadData['subscriptionNotification'];
        $notificationType = $subscriptionNotification["notificationType"];
        $productId = $subscriptionNotification["subscriptionId"];
        $purchaseToken = $subscriptionNotification["purchaseToken"];

        if (env(Key::ENV) === DEV_MODE && !str_contains($productId, "test")) {
            return $this->_sendResponse([], "LIVE Mode Product ID Found In DEV Server, productId : {$productId}");
        }

        if (env(Key::ENV) === PROD_MODE && str_contains($productId, "test")) {
            return $this->_sendResponse([], "DEV Mode Product ID Found In LIVE Server, productId : {$productId}");
        }
        $plan = SubscriptionPlan::find()->byGooglePlanId($productId)->one();
        if ($plan === null) {
            $message = "Plan doesn't exists in our system! GooglePlay Plan ID : {$productId}";
            logError($message, $payloadData);
            return $this->_sendResponse([], $message);
        }
        try {
            $googleHelper = GoogleBillingHelper::sharedInstance();
            $googleHelper->event = $notificationType;
            $googleHelper->processSubscription($productId, $purchaseToken);
        } catch (Exception|\Exception $e) {
            $message = $e->getMessage();
            $message .= "\n\n";
            $message .= $e->getTraceAsString();

            logError($message, $payloadData);
            return $this->_sendErrorResponse(406, $e->getMessage());
        }
        // Return a success response to Google webhook
        return $this->_sendResponse([], "ok");
    }

    public function logIntoFile($data, $title = null)
    {
        if (is_array($data)) {
            $data = print_r($data, true);
        }
        if (is_object($data)) {
            $data = var_export($data, true);
        }
        $indianTimezone = 'Asia/Kolkata';
        $todayDate = Carbon::today($indianTimezone)->format('Y-m-d');
        $logfile = Yii::getAlias("@api/runtime/logs/google_webhook_{$todayDate}.log");
        file_put_contents($logfile, "\n", FILE_APPEND);
        if ($title) {
            file_put_contents($logfile, "\n $title \n", FILE_APPEND);
        }
        file_put_contents($logfile, $data, FILE_APPEND);
        //  return $this->_sendResponse([], "ok");
    }


}