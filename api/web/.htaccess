# API Security Configuration
RewriteEngine on

# Disable directory browsing
Options -Indexes

# Handle CORS preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Add CORS headers for all requests
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, AUTH_TOKEN, X-Requested-With, Accept, Origin"
Header always set Access-Control-Allow-Credentials "true"

# If a directory or a file exists, use the request directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
# Otherwise forward the request to index.php
RewriteRule ^(.*)\?*$ index.php?r=$1 [L,QSA]
