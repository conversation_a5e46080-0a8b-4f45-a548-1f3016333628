<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $this yii\web\View */
/* @var $template common\models\Template */
/* @var $business common\models\Business */
/* @var $quotation common\models\Quotation */
/* @var $quotationItems common\models\QuotationItems[] */
/* @var $terms common\models\TermsCondition[] */
/* @var $otherInfo common\models\enum\OtherInfo */
/* @var $currentUser common\models\User */
/* @var $shouldDisplayWatermark boolean */
/* @var $watermarkLink string */

/* @var $assetsUrl string */

use common\models\Business;
use common\models\enum\DiscountType;
use common\models\enum\Key;
use common\models\QuotationItems;
use common\models\TermsCondition;
use yii\i18n\Formatter;


$this->title = $quotation->id;
Business::setBusiness($quotation->businessId);
// Fetch settings
$appSettings = Business::getSettings(Key::GROUP_APP);
$customSettings = Business::getSettings(Key::GROUP_CUSTOM);

$quotationSettings = $quotation->getQuotationSettingsData();
// fetch and set vars for Quotation Settings

$discountSettings = $quotationSettings[Key::DISCOUNT_SETTINGS]; // "no_discount", "no_discount,per_item,on_total"
$taxRateOnTotal = $quotationSettings[Key::TAX_RATE_ON_TOTAL];
$taxSettings = $quotationSettings[Key::TAX_SETTINGS]; // "no_tax", "no_tax,per_item,on_total"
$topMessage = $quotationSettings[Key::TOP_MESSAGE];
$bottomMessage = $quotationSettings[Key::BOTTOM_MESSAGE];
$isDisplayBankDetails = $quotationSettings[Key::IS_DISPLAY_BANK_DETAILS] && !empty($business->bankInfo);
$isDisplayUpiDetails = $quotationSettings[Key::isDisplayUpiDetails] && !empty($business->upiCode);
$isDisplayHsnCode = $quotationSettings[Key::IS_DISPLAY_HSN_CODE];

$isDisplayTotal = true;

// Multi-user settings
$multiUserSettings = $business->settings(Key::GROUP_MULTI_USER);
$allowUserSignature = $multiUserSettings[Key::allowUserSignature] ?? 0;

// Determine which signature to use
$signatureImg = null;
if ($allowUserSignature && isset($currentUser) && !empty($currentUser->signatureImg)) {
    $signatureImg = $currentUser->signatureImg;
} elseif (!empty($business->signatureImg)) {
    $signatureImg = $business->signatureImg;
}

// settings end

/** @var Formatter $formatter */
$formatter = Yii::$app->formatter;
$formatter->locale = $appSettings[Key::LOCALE_CODE];
$formatter->dateFormat = $appSettings[Key::DATE_FORMAT];
$isProductImageEnabled = $appSettings[Key::IS_PRODUCT_IMAGE_ENABLED];
$business = $quotation->business;
$businessUser = $business->owner;
$customer = $quotation->customer;

// assets images
$mobileLogo = $assetsUrl . "\icons\phone.svg";
$emailLogo = $assetsUrl . "\icons\email.svg";

$additionalFieldsConfig = Business::getConfig(Key::GROUP_CUSTOM, Key::ADDITIONAL_PRODUCT_FIELDS);

?>
<?php
$isHeaderEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED] && $template->isHeaderFooterEnable;
$withHeaderFooter = "";
if ($isHeaderEnabled) {
    $headerImage = $business->headerImg;
    $footerImage = $business->footerImg;
    $withHeaderFooter = "withHeaderFooter";
}
?>
<?php if ($isHeaderEnabled): ?>
    <htmlpageheader name="pdf_header">
        <img src="<?= $headerImage ?>">
    </htmlpageheader>
    <htmlpagefooter name="pdf_footer">
        <img src="<?= $footerImage ?>">
    </htmlpagefooter>
<?php else: ?>
    <htmlpagefooter name="pdf_footer">
        <div class="absolute-div-content"
             style="position: absolute; top: 97%;  left: 87%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
            Page {PAGENO} of {nb}
        </div>
    </htmlpagefooter>
<?php endif; ?>

<body>
<?php if (!$isHeaderEnabled): ?>
    <div id="header">
        <table>
            <tr>
                <?php
                $alignClass = "leftAlign";
                if (!empty($business->image)):
                    $alignClass = "centerAlign";
                    ?>
                    <td id="businessLogo">
                        <img src="<?= $business->image ?>" alt="Business Logo" class="business-logo"/>
                    </td>
                <?php endif; ?>

                <td id="businessInfo" class="<?= $alignClass ?>">
                    <div class="business-name">
                        <?= $business->name ?>
                    </div>
                    <div class="business-address">
                        <?php if (!empty($business->addressLine1)): ?>
                            <div class="addressLine">
                                <?= $business->addressLine1 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine2)): ?>
                            <div class="addressLine">
                                <?= $business->addressLine2 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine3)): ?>
                            <div class="addressLine">
                                <?= $business->addressLine3 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->otherInfo)): ?>
                            <div class="addressLine">
                                <?= $business->otherInfo ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if (!empty($quotation->assignedTo->phoneNumber)): ?>
                            <span class="phone-number">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-phone" viewBox="0 0 16 16">
  <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
  <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
</svg>
                            <?= $quotation->assignedTo->phoneNumber ?>
                    </span>
                        <?php endif; ?>
                        <?php if (!empty($quotation->assignedTo->email)): ?>
                            <span class="email">
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                class="bi bi-envelope" viewBox="0 0 16 16">
  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
</svg>
                        <a class="email"
                           href="mailto: <?= $quotation->assignedTo->email ?>"><?= $quotation->assignedTo->email ?></a>
                    </span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($business->taxNumber)): ?>
                        <div class="tax-number">
                            <span class="taxLabel"><?= $business->taxLabel ?></span>
                            <span class="taxNumber"><?= $business->taxNumber ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="contact-name">
                        Contact Person : <?= $quotation->assignedTo->getName() ?>
                    </div>

                </td>
                <td id="topRight">
                    <h1 class="bold">Quotation</h1>
                </td>
            </tr>
        </table>
    </div>
    <hr/>
<?php else: ?>
<?php endif; ?>
<main class="<?= $withHeaderFooter ?>">
    <table id="topInfo">
        <tr>
            <td id="clientInfo">
                <div class="bold font-16">KIND ATTN :</div>
                <?php if (!empty($customer->companyName)): ?>
                    <div class="customer-business-name">
                        <?= $customer->companyName ?> - <?= $customer->name ?? "" ?>
                    </div>
                <?php endif; ?>
                <div>
                    <?php if (!empty($customer->phoneNumber)): ?>
                        <div class="phone-number">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-phone" viewBox="0 0 16 16">
                                <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                            </svg> <?= $customer->phoneNumber ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->email)): ?>
                        <div class="email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-envelope" viewBox="0 0 16 16">
                                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                            </svg>&nbsp; <?= $customer->email ?>
                        </div>
                    <?php endif; ?>
                </div>
            </td>
            <td class="quotationInfoParent">
                <table id="quotationInfo">
                    <tr>
                        <td class="bold">Quotation#</td>
                        <td id="quoteNumber"><?= $quotation->quotationNumber ?> </td>
                    </tr>
                    <tr>
                        <td class="bold">Date:</td>
                        <td><?= $formatter->asDate($quotation->quotationDate) ?></td>
                    </tr>
                    <?php if (!empty($otherInfo)): ?>
                        <?php /** @var \common\models\enum\OtherInfo $info */
                        foreach ($otherInfo as $info): ?>
                            <tr>
                                <td class="bold"><?= $info->label ?></td>
                                <td><?= $info->value ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </table>
            </td>
        </tr>
    </table>

    <?php if (!empty($quotation->subject)): ?>
        <div id="salutation" class="text-left">
            <div class="line2"><span class="terms-title"> Project : </span> <span><?= $quotation->subject ?></span>
            </div>
        </div>
    <?php endif; ?>

    <div id="salutation">
        <div class="line2"><span><?= nl2br($topMessage) ?></span></div>
    </div>

    <?php
    $colspan = 6;
    $rowspan = 2;
    $displayDiscountColumn = false;
    $isOldVersionUser = false;
    $displaySubTotal = false;
    $displayTaxColumn = false;
    $displayTax = false;
    $displayDiscount = false;

    if (($discountSettings !== Key::NO_DISCOUNT && $quotation->totalDiscountAmount > 0) || $quotation->otherCharges > 0 || $quotation->roundOffAmount > 0 || $quotation->totalTaxAmount > 0) {
        $displaySubTotal = true;
        $rowspan++;
    }

    if ($quotation->otherCharges > 0) {
        $rowspan++;
    }

    if ($quotation->roundOffAmount > 0) {
        $rowspan++;
    }
    if ($taxSettings !== Key::NO_TAX) {
        $displayTax = true;
        if ($taxSettings === Key::PER_ITEM) {
            $displayTaxColumn = true;
        }
        $rowspan++;
    }

    if ($quotation->totalDiscountAmount > 0) {
        $displayDiscount = true;
        $displayDiscountColumn = true;
        $rowspan++;
    }

    if (($taxSettings === Key::NO_TAX || $taxSettings === Key::ON_TOTAL) && $discountSettings === Key::ON_TOTAL) {
        $displayDiscountColumn = false;
    }

    if (empty($quotation->otherChargesLabel)) {
        $isOldVersionUser = true;

        $discountTotal = QuotationItems::find()->where(['quotationId' => $quotation->id])->sum('discountAmount');
        if ($discountTotal > 0) {
            $displayDiscountColumn = true;
        } else {
            $displayDiscountColumn = false;
        }

        $taxTotal = QuotationItems::find()->where(['quotationId' => $quotation->id])->sum('taxAmount');
        if ($taxTotal > 0) {
            $displayTaxColumn = true;
            $displayTax = true;
        } else {
            if ($quotation->totalTaxAmount > 0) {
                $displayTax = true;
            } else {
                $displayTaxColumn = false;
                $displayTax = false;
            }
        }
    }

    if ($quotation->otherCharges > 0) {
        $rowspan++;
    }

    if ($quotation->roundOffAmount > 0) {
        $rowspan++;
    }

    ?>

    <table id="quotationItems" class="topLine">
        <thead>
        <tr class="bottomLine">
            <th class="idx" width="10">#</th>

            <th class="center">Model No.</th>

            <?php if ($isProductImageEnabled): $colspan++ ?>
                <th class="left">PRODUCT IMAGE</th>
            <?php endif; ?>

            <?php if ($isDisplayHsnCode): $colspan++ ?>
                <th class="left"><?= $appSettings[Key::HSN_CODE_LABEL] ?></th>
            <?php endif; ?>

            <?php foreach ($additionalFieldsConfig as $field):
                $key = $field['key'];
                $label = $field['label'];
                $type = $field['type'];
                $colspan++;
                ?>
                <th class="center"><?= $label ?></th>
            <?php endforeach; ?>

            <th class="center">Remarks</th>

            <th class="right v-center">QTY</th>

            <th class="right v-center">PRICE</th>
            <?php if ($displayDiscountColumn): $colspan++ ?>
                <th class="right v-center">DISCOUNT</th>
            <?php endif; ?>
            <?php if ($displayTaxColumn): $colspan++ ?>
                <th class="right v-center"><?= $appSettings[Key::TAX_LABEL] ?></th>
            <?php endif; ?>

            <th class="right v-center">TOTAL</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 1;
        foreach ($quotationItems as $item):
            $product = $item->product;
            $additionalFields = $item->getAdditionalFieldsData();
            ?>
            <tr>
                <td class="idx center">
                    <?= $i++ ?>
                </td>

                <td class="center">
                    <div class=""><?= $product->name ?></div>
                </td>

                <?php if ($isProductImageEnabled): ?>
                    <td class="center"> <?= empty($product->image) ? "" : '<img src="' . $product->image . '"  style="max-width: 120px; max-height: 120px;" />' ?></td>
                <?php endif; ?>

                <?php if ($isDisplayHsnCode): ?>
                    <td class="center"><?= $product->HSNCode ?></td>
                <?php endif; ?>

                <?php foreach ($additionalFieldsConfig as $field):
                    $key = $field['key'];
                    $fieldValue = $additionalFields[$key]['value'];
                    ?>
                    <td class="center"><?= nl2br($fieldValue ?? "") ?></td>
                <?php endforeach; ?>

                <td class="center" width="150">
                    <div class="productDesc"><?= nl2br($item->description) ?></div>
                </td>

                <td class="qty v-center">
                    <div><?= $item->quantity ?></div>
                    <div class="unit"> <?= $product->unit ?? "" ?></div>
                </td>
                <td class="price v-center"><?= formatAsCurrency($item->price) ?></td>

                <?php if ($displayDiscountColumn): ?>
                    <td class="discount">
                        <div><?= formatAsCurrency($item->discountAmount) ?>
                        </div>
                        <?php if ($item->discountType === DiscountType::PERCENTAGE): ?>
                            <div class="percent"><?= $formatter->asDecimal($item->discountPercentage ?? 0, 1) . "%" ?></div>
                        <?php endif; ?>
                    </td>
                <?php endif; ?>

                <?php if ($displayTaxColumn): ?>
                    <td class="tax">
                        <div class="amt"><?= formatAsCurrency($item->taxAmount) ?>
                        </div>
                        <div class="percent"><?= $formatter->asDecimal($product->taxPercentage ?? 0, 1) . "%" ?></div>
                    </td>
                <?php endif; ?>

                <td class="total"><?= formatAsCurrency($item->totalAmount) ?></td>
            </tr>
        <?php endforeach; ?>

        <!--
             total column = <?= $colspan ?>
             total rows = <?= $rowspan ?>
        -->
        <?php if ($isDisplayTotal): ?>

            <tr>
                <td rowspan="<?= $rowspan ?>" colspan="<?= $colspan - 3 ?>" class="left v-center">
                </td>
            </tr>

            <?php if ($displaySubTotal): ?>
                <tr class="topLine">
                    <td colspan="2" class="fixedHeight leftAlign subtotal">SUB TOTAL</td>
                    <td class="total fixedHeight subtotal">
                        <?= formatAsCurrency($quotation->subTotalAmount) ?>
                    </td>
                </tr>
            <?php endif; ?>

            <?php if ($discountSettings !== Key::NO_DISCOUNT && $quotation->totalDiscountAmount > 0): ?>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign">DISCOUNT
                        <?php if ($discountSettings === Key::ON_TOTAL && $quotation->discountType === DiscountType::PERCENTAGE): ?>
                            (<?= $formatter->asDecimal($quotation->discountPercentage, 2) ?>%)
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= "- " . formatAsCurrency($quotation->totalDiscountAmount) ?>
                    </td>
                </tr>
            <?php endif; ?>
            <?php if ($displayTax): ?>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign"> <?= $appSettings[Key::TAX_LABEL] ?>
                        <?php if ($taxSettings !== Key::PER_ITEM): ?>
                            (<?= $formatter->asDecimal($quotation->taxPercentage, 2) ?>%)
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= formatAsCurrency($quotation->totalTaxAmount) ?>
                    </td>
                </tr>
            <?php endif; ?>

            <?php if ($quotation->otherCharges > 0): ?>
                <tr>
                    <td colspan="2"
                        class="fixedHeight leftAlign"> <?= $quotation->otherChargesLabel ?? "Other Charges" ?>
                        <?php if ($quotation->isOtherChargesTaxable && $quotation->otherChargesTaxPercentage > 0): ?>
                            <br/>
                            <span class="text-muted"> (<?= $formatter->asDecimal($quotation->otherChargesTaxPercentage, 2) ?>%) </span>
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= formatAsCurrency($quotation->otherCharges) ?>
                        <?php if ($quotation->isOtherChargesTaxable && $quotation->otherChargesTaxPercentage > 0): ?>
                            <br/>
                            <span class="text-muted"> <?= formatAsCurrency($quotation->otherChargesTaxAmount) ?> </span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endif; ?>

            <?php if ($quotation->roundOffAmount > 0): ?>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign"> Round-off
                    </td>
                    <td class="total fixedHeight">
                        <?= "- " . formatAsCurrency($quotation->roundOffAmount) ?>
                    </td>
                </tr>
            <?php endif; ?>
            <tr>
                <td colspan="2" class="grand total leftAlign doubleLine">GRAND TOTAL</td>
                <td class="grand total doubleLine">
                    <?= formatAsCurrency($quotation->totalAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        </tbody>
    </table>

    <div id="closingNote">
        <p><?= nl2br($bottomMessage) ?></p>
    </div>

    <div id="footer">
        <?php
        $leftClass = "";
        $rightClass = "";
        if ($isDisplayBankDetails || $isDisplayUpiDetails) {
            $leftClass = "leftPart";
            $rightClass = "rightPart";
        } ?>
        <?php if ($terms && count($terms) > 0) : ?>
        <div class="terms-details <?= $leftClass ?>">
            <p class="terms-heading">Terms of Business:</p>
            <ul id="terms">
                <?php /** @var TermsCondition $item */
                foreach ($terms as $item): ?>
                    <li>
                        <?php
                        $paddingClass = "";
                        if (!empty($item->title)): $paddingClass = "pl-10" ?>
                            <div class="terms-title"><?= $item->title ?></div>
                        <?php endif; ?>
                        <div class="terms-text <?= $paddingClass ?>"><?= nl2br($item->text) ?></div>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        <?php if ($isDisplayBankDetails || $isDisplayUpiDetails): ?>
            <div class="<?= $rightClass ?>">
                <div class="term-title">Payment Instructions</div>
                <div id="payment-info" class="term">
                    <?php if ($isDisplayBankDetails): ?>
                        <?= nl2br($business->bankInfo) ?>
                    <?php endif; ?>

                    <?php if ($isDisplayUpiDetails): ?>
                        <?php if ($isDisplayBankDetails): ?>
                            <br/>
                        <?php endif; ?>
                        <div>
                            <div><span style="font-weight: bold;">UPI ID: <?= $business->upiCode ?></span></div>
                            <?php if (!empty($business->upiQrImage)): ?>
                                <div style="padding-top: 10px;">
                                    <img src="<?= $business->upiQrImage ?>" style="max-width: 100px; max-height: 100px;" />
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <br clear="all">
    <div id="rightPart">
        <div id="signBlock">
            <p class="company-name">For, <?= strtoupper($business->name) ?></p>
            <p class="signatureSpace">
                <?php if (!empty($signatureImg)): ?>
                    <img src="<?= $signatureImg ?>" class="signImage"/>&nbsp;
                <?php endif; ?>
            </p>
            <p class="signature">AUTHORIZED SIGNATURE</p>
        </div>
    </div>

</main>

</body>