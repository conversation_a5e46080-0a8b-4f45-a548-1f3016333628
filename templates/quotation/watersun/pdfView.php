<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $this yii\web\View */
/* @var $quotation common\models\Quotation */
/* @var $template common\models\Template */
/* @var $business common\models\Business */
/* @var $customSettings [] */
/* @var $appSettings [] */
/* @var $quotationItems common\models\QuotationItems[] */
/* @var $terms common\models\TermsCondition[] */
/* @var $otherInfo common\models\enum\OtherInfo */
/* @var $currentUser common\models\User */

/* @var $assetsUrl string */

use common\models\Business;
use common\models\enum\Fields;
use common\models\enum\Key;
use yii\i18n\Formatter;


$this->title = $quotation->id;
Business::setBusiness($quotation->businessId);
// Fetch settings
$appSettings = Business::getSettings(Key::GROUP_APP);
$customSettings = Business::getSettings(Key::GROUP_CUSTOM);

$isPageBreakForTerms = $customSettings[Key::isPageBreakForTerms];

$quotationSettings = $quotation->getQuotationSettingsData();
// fetch and set vars for Quotation Settings

$discountSettings = $quotationSettings[Key::DISCOUNT_SETTINGS]; // "no_discount", "no_discount,per_item,on_total"
$taxRateOnTotal = $quotationSettings[Key::TAX_RATE_ON_TOTAL];
$taxSettings = $quotationSettings[Key::TAX_SETTINGS]; // "no_tax", "no_tax,per_item,on_total"
$topMessage = $quotationSettings[Key::TOP_MESSAGE];
$bottomMessage = $quotationSettings[Key::BOTTOM_MESSAGE];
$isDisplayBankDetails = $quotationSettings[Key::IS_DISPLAY_BANK_DETAILS] && !empty($business->bankInfo);
$isDisplayHsnCode = $quotationSettings[Key::IS_DISPLAY_HSN_CODE];

$displayNetRateColumn = $customSettings[Key::IS_DISPLAY_NET_RATE];
$isDisplayTotal = $customSettings[Key::IS_DISPLAY_TOTAL] ?? $quotationSettings[Key::IS_DISPLAY_TOTAL] ?? 1;

// settings end

/** @var Formatter $formatter */
$formatter = Yii::$app->formatter;

$formatter->locale = $quotationSettings[Key::LOCALE_CODE] ?? $appSettings[Key::LOCALE_CODE];
$languageCode = $quotationSettings[Key::LANGUAGE_CODE];
if (!empty($languageCode)) {
    \Yii::$app->language = $languageCode;
}

$shouldDisplayPrice = $quotationSettings[Key::shouldDisplayPrice] ?? 1;


$formatter->dateFormat = $appSettings[Key::DATE_FORMAT];
if ($formatter->locale === "en-ZX") {
    $formatter->locale = "en-ZW";
    $formatter->currencyCode = "ZWL";
}
$isProductImageEnabled = $appSettings[Key::IS_PRODUCT_IMAGE_ENABLED];
$business = $quotation->business;
$businessUser = $business->owner;
$customer = $quotation->customer;

// assets images
$mobileLogo = $assetsUrl . "\icons\phone.svg";
$emailLogo = $assetsUrl . "\icons\email.svg";

$isIndian = $business->regionCode === "IN";
$isDisplayAmountInWords = $isIndian && $quotationSettings[Key::IS_DISPlAY_AMOUNT_WORDS];
$quotationLabel = Yii::t('template-default', 'Quotation');
$taxLabel = $isIndian ? "GSTIN" : $appSettings[Key::TAX_LABEL];

$additionalFields = $quotation->getAdditionalFieldsData() ?? [];
$additionFields = $additionalFields[Key::groupAddition] ?? [];
$subtractionFields = $additionalFields[Key::groupSubtraction] ?? [];
$infoFields = $additionalFields[Key::groupInfo] ?? [];
$settingsFields = $additionalFields[Key::groupSettings] ?? [];

$salesUser = $quotation->assignedTo;
?>

<?php
$isHeaderFooterEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED];
$shouldRepeatHeaderFooter = $appSettings[Key::shouldRepeatHeaderFooter];
$isHeaderEnabled = !empty($business->headerImg) && $isHeaderFooterEnabled;
$isFooterEnabled = !empty($business->footerImg) && $isHeaderFooterEnabled;
$withHeaderFooter = "";
if ($isHeaderEnabled) {
    $headerImage = $business->headerImg;
    $footerImage = $business->footerImg;
    $withHeaderFooter = "withHeaderFooter";
}
$headerImage = $assetsUrl . "\img\header.jpg";
$footerImage = $assetsUrl . "\img\\footer.jpg";
$imgPath = $assetsUrl . "\\img";

$additionalDiscount = $infoFields[Key::additionalDiscount] ?? null;
$additionalDiscountAmount = $additionalDiscount['amount'] ?? 0;
$inverter = $infoFields[Fields::inverter]['value'] ?? null;
$gedaCharge = $infoFields[Fields::gedaCharge]['value'] ?? null;
$project = $infoFields[Fields::project]['value'] ?? null;

// Multi-user settings
$multiUserSettings = $business->settings(Key::GROUP_MULTI_USER);
$allowUserSignature = $multiUserSettings[Key::allowUserSignature] ?? 0;

// Determine which signature to use
$signatureImg = null;
if ($allowUserSignature && isset($currentUser) && !empty($currentUser->signatureImg)) {
    $signatureImg = $currentUser->signatureImg;
} elseif (!empty($business->signatureImg)) {
    $signatureImg = $business->signatureImg;
}


?>
<?php if ($isHeaderEnabled && $shouldRepeatHeaderFooter): ?>
    <htmlpageheader name="pdf_header">
        <img src="<?= $headerImage ?>">
    </htmlpageheader>
<?php endif; ?>
<?php if ($isFooterEnabled && $shouldRepeatHeaderFooter): ?>
    <htmlpagefooter name="pdf_footer">
        <img src="<?= $footerImage ?>">
    </htmlpagefooter>
<?php else: ?>
    <htmlpagefooter name="pdf_footer">
        <div class="absolute-div-content"
             style="position: absolute; top: 97%;  left: 87%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
            Page {PAGENO} of {nb}
        </div>
    </htmlpagefooter>
<?php endif; ?>
<body>

<div class="page">

    <!-- Content Section -->
    <div class="content">
        <!-- Quotation Info Section 1 -->
        <div class="container">

            <table id="topInfo">
                <tr>
                    <td id="clientInfo">
                        <div class="bold font-16"><?= Yii::t('template-default', 'To,') ?></div>
                        <?php if (!empty($customer->companyName)): ?>
                            <div class="customer-business-name">
                                <?= $customer->companyName ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($customer->name)): ?>
                            <div class="contact-name">
                                <?= $customer->name ?>
                            </div>
                        <?php endif; ?>
                        <div class="business-address">
                            <?php if (!empty($customer->addressLine1)): ?>
                                <div class="addressLine">
                                    <?= $customer->addressLine1 ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer->addressLine2)): ?>

                                <div class="addressLine">
                                    <?= $customer->addressLine2 ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer->addressLine3)): ?>
                                <div class="addressLine">
                                    <?= $customer->addressLine3 ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <?php if (!empty($customer->phoneNumber)): ?>
                                <div class="phone-number">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-phone" viewBox="0 0 16 16">
                                        <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                        <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                                    </svg> <?= $customer->phoneNumber ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer->email)): ?>
                                <div class="email">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-envelope" viewBox="0 0 16 16">
                                        <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                                    </svg>&nbsp; <?= $customer->email ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer->taxNumber)): ?>
                                <div class="tax-number">
                                    <span class="taxLabel"> <?= $taxLabel ?> </span>
                                    <span class="taxNumber"><?= $customer->taxNumber ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer->otherInfo)): ?>
                                <div class="addressLine">
                                    <?= nl2br($customer->otherInfo) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($quotation->subject)): ?>
                            <br/>
                            <div>
                                <span class="bold"> Subject: </span> <span><?= $quotation->subject ?></span>
                            </div>
                        <?php endif; ?>
                    </td>
                    <td class="quotationInfoParent">
                        <table id="quotationInfo">
                            <tr>
                                <td class="bold">Quotation NO :</td>
                                <td id="quoteNumber"><?= $quotation->quotationNumber ?> </td>
                            </tr>
                            <tr>
                                <td class="bold"><?= Yii::t('template-default', 'Date :') ?></td>
                                <td><?= $formatter->asDate($quotation->quotationDate) ?></td>
                            </tr>
                            <?php if (!empty($otherInfo)): ?>
                                <?php /** @var \common\models\enum\OtherInfo $info */
                                foreach ($otherInfo as $info): ?>
                                    <tr>
                                        <td class="bold"><?= $info->label ?></td>
                                        <td><?= $info->value ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </table>
                    </td>
                </tr>
            </table>


            <div class="subheader" style="margin-top: 10px;">
                Authorized And Empanelment Vendor of Pradhan Mantri Suryoday Yojna & GEDA
            </div>
            <div class="content" style="text-align: justify">
                <div class="text-center"><img src="<?= $imgPath ?>/subject-img.jpg" alt="Pradhan Mantri Suryoday Yojna"
                                              style="height: 250px;" class="subject-img"></div>

                <p style="margin-top: 10px">We, Watersun Electrical Solutions Pvt Ltd (WESPL), are a reputed supplier of
                    Solar PV modules,
                    solar water Pumping Systems, Solar Inverter and have vast experience of over 10 years in the
                    field of Solar PV. WESPL provides turnkey solutions and single window support for industrial &
                    commercial Solar Rooftop Power plants for captive consumption and ground-mounted MW scale
                    projects including Design, Engineering, Manufacturing, Procurement, Installation, Testing,
                    Commissioning, O&M, Monitoring, and Training.</p>

                <p>WESPL has installed more than 20 MW solar power plants with plant capacity from 3 KW to 500 KW.
                    Each single project has been running successfully for many years with generation performance
                    more than 17% PLF.</p>

                <p>We hope this is in line with your requirements. Please feel free to contact us for any further
                    details and information as required.</p>

                <p>We look forward to your acknowledgment and favorable consideration for the offer submitted.</p>
            </div>

            <div class="signature-section">
                <div class="signature float-right m-2">
                    <p><strong>Yours Truly</strong><br>
                        <strong>Mr.</strong> <?= $salesUser->getName() ?><br>
                        <strong>Phone No</strong>: <?= $salesUser->phoneNumber ?><br>
                    </p>
                </div>
            </div>
        </div>
        <pagebreak/>
        <!-- Quotation Info Section 2 -->
        <div class="container">
            <div class="system-header">
                <h1>Techno Commercial Proposal For</h1>
                <h2><?= $quotation->subject ?></h2>
                <h3>Solar power system</h3>
            </div>

            <table class="table-container">
                <tr>
                    <th>Description</th>
                    <th>Make</th>
                    <th>Quantity</th>
                </tr>
                <?php
                $i = 1;
                foreach ($quotationItems as $item):
                    $product = $item->product;
                    ?>
                    <tr>
                        <th>Solar Panel</th>
                        <td><?= $product->name ?></td>
                        <td><?= $item->quantity ?> <?= $product->unit ?? "" ?></td>
                    </tr>
                <?php endforeach; ?>
                <tr>
                    <th>Inverter</th>
                    <td colspan="2" class="text-align"><?= $inverter ?></td>
                    <!-- <td></td> -->
                </tr>
                <tr>
                    <th>Geb / Geda Charge</th>
                    <td colspan="2" class="text-align"><?= $gedaCharge ?></td>
                    <!-- <td></td> -->
                </tr>
                <tr>
                    <th>Project</th>
                    <td colspan="2" class="text-align"><?= $project ?></td>
                    <!-- <td></td> -->
                </tr>
            </table>

            <table class="description-table">
                <tbody>
                <tr>
                    <th>Sr</th>
                    <th>Description</th>
                    <th style="text-align: center; white-space: nowrap">Value (INR)</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td width="80%">
                        Design, supply, erection and commissioning of a rooftop grid-tied solar PV power generation
                        plant with standard lengths of wires. The solar power plant will consist of required no. of
                        SPV panels, inverter, and all the electrical items suitable to the designed installed
                        capacity.
                    </td>
                    <td class="total"><?= formatAsCurrency($quotation->subTotalAmount) ?></td>
                </tr>
                <tr>
                    <td colspan="2" class="right">Discount</td>
                    <td class="total"><?= formatAsCurrency($quotation->totalDiscountAmount) ?></td>
                </tr>
                <tr>
                    <td colspan="2" class="right">Net Payable Amount</td>
                    <td class="total"><?= formatAsCurrency($quotation->subTotalAmount - $quotation->totalDiscountAmount) ?></td>
                </tr>
                <tr>
                    <td colspan="2" class="right">Subsidy (Subsidy Will be credited To The Customer Account)</td>
                    <td class="total"><?= formatAsCurrency($additionalDiscountAmount) ?></td>
                </tr>
                <tr>
                    <td colspan="2" class="important right bold">Net Price After Receiving Subsidies</td>
                    <td class="total bold"><?= formatAsCurrency($quotation->totalAmount) ?></td>
                </tr>
                </tbody>
            </table>


            <!-- <p class="important"></p> -->

            <div class="notes">
                <p><strong>Note :</strong></p>
                <ul>
                    <li>All GST is inclusive</li>
                    <li>70% Solar Power System-(HSN CODE-8541)-12% @GST</li>
                    <li>30% Solar Power System-(HSN CODE-9954)-18% @GST</li>
                </ul>
            </div>

            <?php if (!empty($terms))  : ?>
                <div class="payment-terms">
                    <p><strong>Payment Terms And Condition :</strong></p>
                    <ul id="terms">
                        <?php foreach ($terms as $item): ?>
                            <li class="term">
                                <?= nl2br($item->text) ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

        </div>

        <pagebreak/>
        <!-- Quotation Info Section 3 -->
        <div class="container">
            <table class="terms-table">
                <thead>
                <tr>
                    <th colspan="3" class="section-title">
                        Terms & Conditions
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th>Sr.</th>
                    <th>Parameters</th>
                    <th>Remarks</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Rooftop area @10 Sq.Mtr./KWp to be provided</td>
                    <td class="remarks">Customer Scope</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Civil works</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Mounting, Erection, and Commissioning</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>Power evacuations (solar plant to mains)</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>Lightening arrester systems</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>Earthing systems</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>Free Operation & Maintenance (Cleaning has to be done by the customer)</td>
                    <td class="remarks">Included for 5 Years</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>Transport charges</td>
                    <td>Included</td>
                </tr>
                <tr>
                    <td>9</td>
                    <td>Supply, Erection & Commissioning Period</td>
                    <td>Generally, it is 1 month.</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>Validity period for this quote</td>
                    <td>15 days from the date of this offer</td>
                </tr>
                <tr>
                    <td>11</td>
                    <td>All extra and additional material/work</td>
                    <td>As per the bill raised on actual expenditure</td>
                </tr>
                </tbody>
                <thead>
                <tr>
                    <th colspan="3" class="section-title">
                        Warranty
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>1</td>
                    <td>SPV modules (for Manufacturing Defects)</td>
                    <td>15 Years</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>SPV modules (for performance)</td>
                    <td>90% Power output for the 1st 10 years <br> 80% power output for the 2nd 30 years</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Inverter (for Manufacturing Defects) Back to Back</td>
                    <td>10 Years</td>
                </tr>
                </tbody>
            </table>

            <h2 class="section-title"></h2>
            <table class="bom-table">
                <thead>
                <tr>
                    <th colspan="6" class="section-title">
                        BOM
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th>Sr.</th>
                    <th>Description</th>
                    <th>Unit</th>
                    <th>Qty.</th>
                    <th>Size</th>
                    <th>Make</th>
                </tr>

                <tr>
                    <td>1</td>
                    <td>Solar modules</td>
                    <td>Nos</td>
                    <td>As Per the Above First Page</td>
                    <td>As Per the Project</td>
                    <td>As Per the Above First Page</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Module mounting structure (GI)</td>
                    <td>Set</td>
                    <td>As per design</td>
                    <td>(max up to 8 FT from Ground)</td>
                    <td>Watersun Standard</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>String type Grid Tied Inverter</td>
                    <td>Nos</td>
                    <td>As per design</td>
                    <td>As Per the Project</td>
                    <td>Solaryaan / Solex</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>AJB with accessories</td>
                    <td>Nos</td>
                    <td>As per design</td>
                    <td>****</td>
                    <td>Polycab / C&S / Standard</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>ACDB</td>
                    <td>Nos</td>
                    <td>At actual</td>
                    <td>As per design</td>
                    <td>Polycab / C&S / Standard</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>DC cable with UV protected</td>
                    <td>Mtr.</td>
                    <td>At actual</td>
                    <td>As per design</td>
                    <td>Polycab / Equivalent</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>AC Cable</td>
                    <td>Mtr.</td>
                    <td>At actual</td>
                    <td>As per design</td>
                    <td>Polycab / Johnson / Equivalent</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>Earthing systems</td>
                    <td>Nos</td>
                    <td>At actual</td>
                    <td>As per design</td>
                    <td>Standard</td>
                </tr>
                <tr>
                    <td>9</td>
                    <td>Lightening arrester systems</td>
                    <td>Nos</td>
                    <td>At actual</td>
                    <td>****</td>
                    <td>Standard</td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>LA Cable</td>
                    <td>MTR</td>
                    <td>At actual</td>
                    <td>16 Sqmm Aluminium</td>
                    <td>Flexguard / Johnson / Polycab</td>
                </tr>
                <tr>
                    <th colspan="6" class="section-title">
                        Estimated Other Charges
                    </th>
                </tr>
                <tr>
                    <th colspan="2" class="section-title">
                        Account Number
                    </th>
                    <td colspan="4">
                        * 50% Advance includes discom registration charges of Rs.2000/- per KW. If any client refused to
                        install solar plant after discom registration payment, then we shall refund the amount after
                        deducting discom charges and receipt of same will be provided with A/C statement.
                    </td>
                </tr>
                <tr>
                    <th colspan="6" class="section-title">Company Bank Details</th>
                </tr>
                <tr>
                    <th colspan="2">Account Name</th>
                    <th colspan="4">WATERSUN ELECTRICAL SOLUTIONS PRIVATE LIMITED</th>
                </tr>
                <tr>
                    <td colspan="2">Account Number</td>
                    <td colspan="4">**************</td>
                </tr>
                <tr>
                    <td colspan="2">Bank Name</td>
                    <td colspan="4">HDFC BANK</td>
                </tr>
                <tr>
                    <td colspan="2">Branch Name</td>
                    <td colspan="4">RADHANPUR</td>
                </tr>
                <tr>
                    <td colspan="2">IFSC Code</td>
                    <td colspan="4">HDFC0002794</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>

</body>