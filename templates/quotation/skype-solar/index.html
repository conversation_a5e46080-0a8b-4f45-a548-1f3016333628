<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Skype Solar Residential Quotation</title>
<!--  include css file from assets folder-->
  <link rel="stylesheet" href="./assets/css/pdf-style.css" />
  <style>

  </style>
</head>
<body>
  <div class="page">
    <div style="width: 595px; height: 842px; position: relative; background: white">
      <img style="width: 595px; height: 114px; left: 0px; top: 0px; position: absolute" src="https://via.placeholder.com/595x114" />
      <img style="width: 595px; height: 88px; left: 0px; top: 754px; position: absolute" src="https://via.placeholder.com/595x88" />
      <div style="left: 20px; top: 123px; position: absolute; color: #549F06; font-size: 16px; font-family: Inter; font-weight: 600; letter-spacing: 0.48px; word-wrap: break-word">Residential, Commercial & Industrial Solar Power Solutions</div>
      <div style="width: 555px; left: 20px; top: 151px; position: absolute; justify-content: flex-start; align-items: center; gap: 11px; display: inline-flex">
        <img style="width: 178px; height: 141.16px" src="https://via.placeholder.com/178x141" />
        <img style="width: 178px; height: 141.16px" src="https://via.placeholder.com/178x141" />
        <img style="width: 178px; height: 141.16px" src="https://via.placeholder.com/178x141" />
      </div>
      <div style="left: 20px; top: 309px; position: absolute; color: #023C90; font-size: 20px; font-family: Poppins; font-weight: 600; word-wrap: break-word">HOW NET METERING WORKS</div>
      <img style="width: 481px; height: 243px; left: 20px; top: 348px; position: absolute" src="https://via.placeholder.com/481x243" />
      <div style="left: 20px; top: 595px; position: absolute; color: #023C90; font-size: 16px; font-family: Poppins; font-weight: 600; word-wrap: break-word">Proposal for Solar Power Plant:</div>
      <div style="width: 517px; height: 125px; left: 24px; top: 625px; position: absolute">
        <div style="width: 517px; height: 125px; left: 0px; top: 0px; position: absolute">
          <div style="width: 517px; height: 25px; left: 0px; top: 0px; position: absolute">
            <div style="width: 182px; height: 25px; left: 0px; top: 0px; position: absolute; border: 0.50px black solid"></div>
            <div style="width: 335px; height: 25px; left: 182px; top: 0px; position: absolute; border-top: 0.50px black solid; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
          </div>
          <div style="width: 517px; height: 25px; left: 0px; top: 25px; position: absolute">
            <div style="width: 182px; height: 25px; left: 0px; top: 0px; position: absolute; border-left: 0.50px black solid; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
            <div style="width: 335px; height: 25px; left: 182px; top: 0px; position: absolute; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
          </div>
          <div style="width: 517px; height: 25px; left: 0px; top: 50px; position: absolute">
            <div style="width: 182px; height: 25px; left: 0px; top: 0px; position: absolute; border-left: 0.50px black solid; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
            <div style="width: 335px; height: 25px; left: 182px; top: 0px; position: absolute; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
          </div>
          <div style="width: 517px; height: 25px; left: 0px; top: 75px; position: absolute">
            <div style="width: 182px; height: 25px; left: 0px; top: 0px; position: absolute; border-left: 0.50px black solid; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
            <div style="width: 335px; height: 25px; left: 182px; top: 0px; position: absolute; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
          </div>
          <div style="width: 517px; height: 25px; left: 0px; top: 100px; position: absolute">
            <div style="width: 182px; height: 25px; left: 0px; top: 0px; position: absolute; border-left: 0.50px black solid; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
            <div style="width: 335px; height: 25px; left: 182px; top: 0px; position: absolute; border-right: 0.50px black solid; border-bottom: 0.50px black solid"></div>
          </div>
        </div>
        <div style="left: 12px; top: 4px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Date of Offer</div>
        <div style="left: 12px; top: 29px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Project</div>
        <div style="left: 12px; top: 54px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Power Plant Size</div>
        <div style="left: 12px; top: 79px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Customer Name</div>
        <div style="left: 12px; top: 104px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Site Address</div>
        <div style="left: 197px; top: 4px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">18/05/2024</div>
        <div style="left: 197px; top: 29px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">Residential</div>
        <div style="left: 197px; top: 54px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">30KW (ON-Grid/Grid Tied Solar Power Plant)</div>
        <div style="left: 197px; top: 80px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">DR. Sandeep Jain ji</div>
        <div style="left: 197px; top: 104px; position: absolute; color: black; font-size: 12px; font-family: Poppins; font-weight: 500; word-wrap: break-word">NEAR Mahima Elanza, JAIPUR, RAJASTHAN</div>
      </div>
    </div>
  </div>
</body>
</html>
