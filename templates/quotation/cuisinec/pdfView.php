<?php

/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $this yii\web\View */
/* @var $quotation common\models\Quotation */
/* @var $template common\models\Template */
/* @var $business common\models\Business */
/* @var $customSettings [] */
/* @var $appSettings [] */
/* @var $quotationItems common\models\QuotationItems[] */
/* @var $terms common\models\TermsCondition[] */
/* @var $otherInfo common\models\enum\OtherInfo */
/* @var $currentUser common\models\User */
/* @var $shouldDisplayWatermark boolean */
/* @var $watermarkLink string */

/* @var $assetsUrl string */

use common\models\Business;
use common\models\enum\AttachmentType;
use common\models\enum\DiscountType;
use common\models\enum\Key;
use common\models\QuotationItems;
use yii\i18n\Formatter;

$this->title = $quotation->id;
Business::setBusiness($quotation->businessId);
// Fetch settings

$isPageBreakForTerms = $customSettings[Key::isPageBreakForTerms];

$quotationSettings = $quotation->getQuotationSettingsData();
// fetch and set vars for Quotation Settings

$discountSettings = $quotationSettings[Key::DISCOUNT_SETTINGS]; // "no_discount", "no_discount,per_item,on_total"
$taxRateOnTotal = $quotationSettings[Key::TAX_RATE_ON_TOTAL];
$taxSettings = $quotationSettings[Key::TAX_SETTINGS]; // "no_tax", "no_tax,per_item,on_total"
$topMessage = $quotationSettings[Key::TOP_MESSAGE];
$bottomMessage = $quotationSettings[Key::BOTTOM_MESSAGE];
$isDisplayBankDetails = $quotationSettings[Key::IS_DISPLAY_BANK_DETAILS];
$isDisplayProductCode = $quotationSettings[Key::IS_DISPLAY_PRODUCT_CODE];
$isDisplayHsnCode = $quotationSettings[Key::IS_DISPLAY_HSN_CODE];
$isPagebreakBeforeTerms = $quotationSettings[Key::IS_PAGE_BREAK_BEFORE_TERMS] ?? 0;
$isDisplaySalesPerson = $quotationSettings[Key::isDisplaySalesPerson] ?? 0;
$discountLabel = $quotationSettings[Key::discountLabel] ?? "DISCOUNT";

$displayNetRateColumn = $customSettings[Key::IS_DISPLAY_NET_RATE];
$isDisplayTotal = $customSettings[Key::IS_DISPLAY_TOTAL] && $quotationSettings[Key::IS_DISPLAY_TOTAL];

// Multi-user settings
$multiUserSettings = $business->settings(Key::GROUP_MULTI_USER);
$allowUserSignature = $multiUserSettings[Key::allowUserSignature] ?? 0;

// Determine which signature to use
$signatureImg = null;
if ($allowUserSignature && isset($currentUser) && !empty($currentUser->signatureImg)) {
    $signatureImg = $currentUser->signatureImg;
} elseif (!empty($business->signatureImg)) {
    $signatureImg = $business->signatureImg;
}

// settings end

/** @var Formatter $formatter */
$formatter = Yii::$app->formatter;

$formatter->locale = $quotationSettings[Key::LOCALE_CODE] ?? $appSettings[Key::LOCALE_CODE];
$languageCode = $quotationSettings[Key::LANGUAGE_CODE];
if (!empty($languageCode)) {
    \Yii::$app->language = $languageCode;
}

$shouldDisplayPrice = $quotationSettings[Key::shouldDisplayPrice] ?? 1;
$shouldDisplayPrice = $shouldDisplayPrice && !$quotation->isLumpsumAmount;
$isOnlyPriceDisplay = $quotationSettings[Key::isOnlyPriceDisplay] ?? 0;

$displayQtyColumn = true;
$displayDiscountColumn = true;
$displayTaxColumn = true;
$displayPriceColumn = true;
$displayTotalColumn = true;

$displaySubTotal = true;
$displayGrandTotal = true;
$displayTax = true;
$displayDiscount = true;
$descFieldWidth = 320;
//$isOnlyPriceDisplay = true;
if (!$shouldDisplayPrice) { // Hide Price & total columns
    $displayPriceColumn = false;
    $displayTotalColumn = false;
}

if ($isOnlyPriceDisplay) { // Display on price column
    $displayQtyColumn = false;
    $displayTaxColumn = false;
    $displayDiscountColumn = false;
    $displayTotalColumn = false;

    $displaySubTotal = false;
    $displayDiscount = false;
    $displayTax = false;
    $displayGrandTotal = false;

    $descFieldWidth = "550";
}

if (!$isOnlyPriceDisplay && !$isDisplayTotal) { // Hide/Show - TOTAL Block - After Price table
    $displaySubTotal = false;
    $displayDiscount = false;
    $displayTax = false;
    $displayGrandTotal = false;
}


$formatter->dateFormat = $appSettings[Key::DATE_FORMAT];
if ($formatter->locale === "en-ZX") {
    $formatter->locale = "en-ZW";
    $formatter->currencyCode = "ZWL";
}

if ($formatter->locale === "en-ZG") {
    $formatter->locale = "en-ZW";
    $formatter->currencyCode = "ZiG";
}


$isProductImageEnabled = $appSettings[Key::IS_PRODUCT_IMAGE_ENABLED];
$business = $quotation->business;
$businessUser = $business->owner;
$customer = $quotation->customer;

// assets images
$mobileLogo = $assetsUrl . "\icons\phone.svg";
$emailLogo = $assetsUrl . "\icons\email.svg";

// Resolve the image path using Yii2 alias
$watermarkPath = Yii::getAlias('@backend/web/img/logo/watermark.png');

$isIndian = $business->regionCode === "IN";
$isDisplayAmountInWords = $isIndian && $quotationSettings[Key::IS_DISPlAY_AMOUNT_WORDS];
$quotationLabel = Yii::t('template-default', 'Quotation');
$taxLabel = $isIndian ? "GSTIN" : $appSettings[Key::TAX_LABEL];

$additionalFields = $quotation->getAdditionalFieldsData() ?? [];
$additionFields = $additionalFields[Key::groupAddition] ?? [];
$subtractionFields = $additionalFields[Key::groupSubtraction] ?? [];
$infoFields = $additionalFields[Key::groupInfo] ?? [];
$settingsFields = $additionalFields[Key::groupSettings] ?? [];

$additionalDiscount = $infoFields[Key::additionalDiscount] ?? null;
$additionalDiscountAmount = $additionalDiscount[Key::amount] ?? 0;

$additionalFieldsConfig = Business::getConfig(Key::GROUP_CUSTOM, Key::ADDITIONAL_PRODUCT_FIELDS) ?? [];

?>

<?php
$isHeaderFooterEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED] && $template->isHeaderFooterEnable;
$shouldRepeatHeaderFooter = $appSettings[Key::shouldRepeatHeaderFooter];
$isHeaderEnabled = !empty($business->headerImg) && $isHeaderFooterEnabled;
$isFooterEnabled = !empty($business->footerImg) && $isHeaderFooterEnabled;
$withHeaderFooter = "";
if ($isHeaderEnabled) {
    $headerImage = $business->headerImg;
    $footerImage = $business->footerImg;
    $withHeaderFooter = "withHeaderFooter";
}
?>
<?php if ($isHeaderEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpageheader name="pdf_header">
        <img src="<?= $headerImage ?>">
    </htmlpageheader>
<?php endif; ?>
<?php if ($isFooterEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpagefooter name="pdf_footer">
        <img src="<?= $footerImage ?>">
    </htmlpagefooter>
<?php else : ?>
    <htmlpagefooter name="pdf_footer">
        <div class="absolute-div-content"
             style="position: absolute; top: 97%;  left: 87%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
            Page {PAGENO} of {nb}
        </div>
    </htmlpagefooter>
<?php endif; ?>

<body>
<?php if ($isHeaderEnabled && !$shouldRepeatHeaderFooter) :
    $margin = $template->getMarginSettings();
    $mainMarginTop = $margin->topWithHeaderImage + (6 * $margin->top);
    $absMarginTop = -1 * $margin->top;
    ?>
    <div class="<?= $withHeaderFooter ?>" style="position: absolute; top: <?= $absMarginTop ?>px;  left: 0;">
        <img src="<?= $headerImage ?>" width="100%" height="auto">
    </div>
    <div style="height: <?= $mainMarginTop ?>">&nbsp;</div>
<?php elseif (!$isHeaderEnabled) : ?>
    <div id="header">
        <table>
            <tr>
                <?php
                $alignClass = "leftAlign";
                if (!empty($business->image)) :
                    $alignClass = "centerAlign";
                    ?>
                    <td id="businessLogo">
                        <img src="<?= $business->image ?>" alt="Business Logo" class="business-logo"/>
                    </td>
                <?php endif; ?>

                <td id="businessInfo" class="<?= $alignClass ?>">
                    <div class="business-name">
                        <?= $business->name ?>
                    </div>
                    <div class="contact-name">
                        <?= $business->contactName ?>
                    </div>
                    <div class="business-address">
                        <?php if (!empty($business->addressLine1)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine1 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine2)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine2 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine3)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine3 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->otherInfo)) : ?>
                            <div class="addressLine">
                                <?= $business->otherInfo ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if (!empty($business->phoneNumber)) : ?>
                            <span class="phone-number">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-phone" viewBox="0 0 16 16">
                                        <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                        <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                                    </svg>
                                    <?= $business->phoneNumber ?>
                                </span>
                        <?php endif; ?>
                        <?php if (!empty($business->email)) : ?>
                            <span class="email">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-envelope" viewBox="0 0 16 16">
                                        <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                                    </svg>
                                    <a class="email" href="mailto: <?= $business->email ?>"><?= $business->email ?></a>
                                </span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($business->taxNumber)) : ?>
                        <div class="tax-number">
                            <span class="taxLabel"><?= $business->taxLabel ?></span>
                            <span class="taxNumber"><?= $business->taxNumber ?></span>
                        </div>
                    <?php endif; ?>
                </td>
                <td id="topRight">
                    <h1 class="bold"><?= $quotationLabel ?></h1>
                </td>
            </tr>
        </table>
    </div>
    <hr/>
<?php endif; ?>
<main class="<?= $withHeaderFooter ?>" style="">
    <table id="topInfo">
        <tr>
            <td id="clientInfo">
                <div class="bold font-16"><?= Yii::t('template-default', 'To,') ?></div>
                <?php if (!empty($customer->companyName)) : ?>
                    <div class="customer-business-name">
                        <?= $customer->companyName ?>
                    </div>
                <?php endif; ?>
                <?php if (!empty($customer->name)) : ?>
                    <div class="contact-name">
                        <?= $customer->name ?>
                    </div>
                <?php endif; ?>
                <div class="business-address">
                    <?php if (!empty($customer->addressLine1)) : ?>
                        <div class="addressLine">
                            <?= $customer->addressLine1 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine2)) : ?>

                        <div class="addressLine">
                            <?= $customer->addressLine2 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine3)) : ?>
                        <div class="addressLine">
                            <?= $customer->addressLine3 ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div>
                    <?php if (!empty($customer->phoneNumber)) : ?>
                        <div class="phone-number">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-phone" viewBox="0 0 16 16">
                                <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                            </svg> <?= $customer->phoneNumber ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->email)) : ?>
                        <div class="email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-envelope" viewBox="0 0 16 16">
                                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                            </svg>&nbsp; <?= $customer->email ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->taxNumber)) : ?>
                        <div class="tax-number">
                            <span class="taxLabel"> <?= $taxLabel ?> </span>
                            <span class="taxNumber"><?= $customer->taxNumber ?></span>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->otherInfo)) : ?>
                        <div class="addressLine">
                            <?= nl2br($customer->otherInfo) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </td>
            <td class="quotationInfoParent">
                <table id="quotationInfo">
                    <?php if ($isHeaderEnabled) : ?>
                        <tr>
                            <td id="topRight" colspan="2">
                                <h1 class="bold"><?= $quotationLabel ?></h1>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" height="5px">&nbsp;</td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td class="bold"><?= Yii::t('template-default', 'Quotation#') ?></td>
                        <td id="quoteNumber"><?= $quotation->quotationNumber ?> </td>
                    </tr>
                    <tr>
                        <td class="bold"><?= Yii::t('template-default', 'Date:') ?></td>
                        <td><?= $formatter->asDate($quotation->quotationDate) ?></td>
                    </tr>
                    <?php if (!empty($otherInfo)) : ?>
                        <?php /** @var \common\models\enum\OtherInfo $info */
                        foreach ($otherInfo as $info) : ?>
                            <tr>
                                <td class="bold"><?= $info->label ?></td>
                                <td><?= $info->value ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </table>
            </td>
        </tr>
    </table>

    <?php if (!empty($quotation->subject)) : ?>
        <div id="salutation" class="text-left">
            <div class="line2 bold"><span class="terms-title"> Subject: </span> <span><?= $quotation->subject ?></span>
            </div>
        </div>
    <?php endif; ?>

    <div id="salutation">
        <div class="line2"><span><?= nl2br($topMessage) ?></span></div>
    </div>

    <?php
    $colspan = 2;
    $rowspan = 2;
    $isOldVersionUser = false;


    if ($displaySubTotal && (($discountSettings !== Key::NO_DISCOUNT && $quotation->totalDiscountAmount > 0)
            || $quotation->otherCharges > 0 || $quotation->roundOffAmount != 0 || $quotation->totalTaxAmount > 0)) {
        $displaySubTotal = true;
        $rowspan++;
    } else {
        $displaySubTotal = false;
    }

    if ($quotation->otherCharges > 0) {
        $rowspan++;
    }

    if ($quotation->roundOffAmount != 0) {
        $rowspan++;
    }


    if ($quotation->totalTaxAmount > 0 && $taxSettings !== Key::NO_TAX) {
        if ($displayTaxColumn && $taxSettings === Key::PER_ITEM) {
            $displayTaxColumn = true;
        } else {
            $displayTaxColumn = false;
        }
    } else {
        $displayTax = false;
        $displayTaxColumn = false;
    }

    if ($displayTax) {
        $rowspan++;
    }

    if ($displayDiscount && $quotation->totalDiscountAmount > 0) {
        $displayDiscount = true;
        $rowspan++;
    } else {
        $displayDiscount = false;
    }

    if ($displayDiscountColumn && $quotation->totalDiscountAmount > 0) {
        $displayDiscountColumn = true;
    } else {
        $displayDiscountColumn = false;
    }

    if (($taxSettings === Key::NO_TAX || $taxSettings === Key::ON_TOTAL) && $discountSettings === Key::ON_TOTAL) {
        $displayDiscountColumn = false;
    }

    if (empty($quotation->otherChargesLabel)) {
        $isOldVersionUser = true;

        $discountTotal = QuotationItems::find()->where(['quotationId' => $quotation->id])->sum('discountAmount');
        if ($discountTotal > 0) {
            $displayDiscountColumn = true;
        } else {
            $displayDiscountColumn = false;
        }

        $taxTotal = QuotationItems::find()->where(['quotationId' => $quotation->id])->sum('taxAmount');
        if ($taxTotal > 0) {
            $displayTaxColumn = true;
            $displayTax = true;
        } else {
            if ($quotation->totalTaxAmount > 0) {
                $displayTax = true;
            } else {
                $displayTaxColumn = false;
                $displayTax = false;
            }
        }
    }

    if ($quotation->otherCharges > 0) {
        $rowspan++;
    }

    if ($quotation->roundOffAmount > 0) {
        $rowspan++;
    }

    if (!$displayPriceColumn) {
        $displayTaxColumn = false;
        $displayDiscountColumn = false;
    }
    if (!empty($additionFields) || !empty($subtractionFields)) {
        $additionFieldsCount = count($additionFields);
        $subtractionFieldsCount = count($subtractionFields);
        $rowspan += $additionFieldsCount + $subtractionFieldsCount;
    }

    if (!empty($additionalDiscount) && $additionalDiscountAmount >0)
        $rowspan += 2;
    ?>

    <table id="quotationItems" class="topLine">
        <thead>
        <tr class="bottomLine">
            <th class="idx" width="10">#</th>

            <th class="desc" width="<?= $descFieldWidth ?>"><?= Yii::t('template-default', 'DESCRIPTION') ?></th>
            <?php if ($isDisplayProductCode) : $colspan++ ?>
                <th class="left">Part No.</th>
            <?php endif; ?>

            <?php if ($isDisplayHsnCode) : $colspan++ ?>
                <th class="left"><?= $appSettings[Key::HSN_CODE_LABEL] ?></th>
            <?php endif; ?>

            <?php if ($isProductImageEnabled) : $colspan++ ?>
                <th class="center"><?= Yii::t('template-default', 'PRODUCT IMAGE') ?></th>
            <?php endif; ?>

            <?php foreach ($additionalFieldsConfig as $field):
                $key = $field['key'];
                $label = $field['label'];
                $type = $field['type'];
                $colspan++;
                ?>
                <th class="center"><?= $label ?></th>
            <?php endforeach; ?>

            <?php if ($displayQtyColumn) : $colspan++ ?>
                <th class="right"><?= Yii::t('template-default', 'QTY') ?></th>
            <?php endif; ?>

            <?php if ($displayPriceColumn) : $colspan++ ?>
                <th class="right"><?= Yii::t('template-default', 'PRICE') ?></th>
            <?php endif; ?>

            <?php if ($displayDiscountColumn) : $colspan++ ?>
                <th class="right"><?= Yii::t('template-default', 'DISCOUNT') ?></th>
            <?php endif; ?>

            <?php if ($displayDiscountColumn && $displayNetRateColumn) : $colspan++ ?>
                <th class="right"><?= Yii::t('template-default', 'Net Rate') ?></th>
            <?php endif; ?>

            <?php if ($displayTaxColumn) : $colspan += 2 ?>
                <th class="right text-wrap"><?= Yii::t('template-default', 'TAXABLE AMOUNT') ?></th>
                <th class="right"><?= $appSettings[Key::TAX_LABEL] ?></th>
            <?php endif; ?>
            <?php if ($displayTotalColumn) : $colspan++ ?>
                <th class="right"><?= Yii::t('template-default', 'TOTAL') ?></th>
            <?php endif; ?>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 1;
        foreach ($quotationItems as $item) :
            $product = $item->product;
            $additionalFields = $item->getAdditionalFieldsData();
            ?>
            <tr>
                <td class="idx">
                    <?= $i++ ?>
                </td>

                <td class="desc">
                    <div class="productName"><?= $product->name ?></div>
                    <?php if ($item->description) : ?>
                        <div class="productDesc"><?= nl2br($item->description) ?></div>
                    <?php endif; ?>
                </td>

                <?php if ($isDisplayProductCode) : ?>
                    <td class="hsn"><?= $product->code ?></td>
                <?php endif; ?>

                <?php if ($isDisplayHsnCode) : ?>
                    <td class="hsn"><?= $product->HSNCode ?></td>
                <?php endif; ?>

                <?php if ($isProductImageEnabled) : ?>
                    <td class="center"> <?= empty($product->image) ? "" : '<img src="' . $product->image . '"  style="max-width: 120px; max-height: 120px;" />' ?></td>
                <?php endif; ?>

                <?php foreach ($additionalFieldsConfig as $field):
                    $key = $field['key'];
                    $fieldValue = $additionalFields[$key]['value'];
                    ?>
                    <td class="center"><?= nl2br($fieldValue ?? "") ?></td>
                <?php endforeach; ?>

                <?php if ($displayQtyColumn) : ?>
                    <td class="qty">
                        <div><?= $item->quantity ?></div>
                        <div class="unit"> <?= $product->unit ?? "" ?></div>
                    </td>
                <?php endif; ?>

                <?php if ($displayPriceColumn) : ?>
                    <td class="price"><?= formatAsCurrency($item->price) ?></td>
                <?php endif; ?>

                <?php if ($displayDiscountColumn) : ?>
                    <td class="discount">
                        <div><?= formatAsCurrency($item->discountAmount) ?>
                        </div>
                        <?php if ($item->discountType === DiscountType::PERCENTAGE) : ?>
                            <div class="percent"><?= $formatter->asDecimal($item->discountPercentage ?? 0, 1) . "%" ?></div>
                        <?php endif; ?>
                    </td>
                <?php endif; ?>


                <?php if ($displayDiscountColumn && $displayNetRateColumn) : ?>
                       <td class="price"><?= formatAsCurrency($item->price - ($item->discountAmount / $item->quantity)) ?></td>
                <?php endif; ?>

                <?php if ($displayTaxColumn) : ?>
                    <td class="total"><?= formatAsCurrency($item->totalAmount - $item->taxAmount) ?></td>
                    <td class="tax">
                        <div class="amt"><?= formatAsCurrency($item->taxAmount) ?>
                        </div>
                        <div class="percent"><?= $formatter->asDecimal($product->taxPercentage ?? 0, 1) . "%" ?></div>
                    </td>
                <?php endif; ?>

                <?php if ($displayTotalColumn) : ?>
                    <td class="total"><?= formatAsCurrency($item->totalAmount) ?></td>
                <?php endif; ?>
            </tr>
        <?php endforeach; ?>

        <!--
             total column = <?= $colspan ?>
             total rows = <?= $rowspan ?>
        -->
        <?php if (!$displayTotalColumn) : ?>
        </tbody>
    </table>
<?php if ($displayGrandTotal) : ?>
    <table class="topLine">
        <tbody>
        <tr>
            <td rowspan="<?= $rowspan ?>" width="60%">
                &nbsp;
            </td>
        </tr>
        <?php endif; ?>
        <?php else : ?>
            <tr>
                <td colspan="<?= $colspan ?>" class="noBorder" style="height: 1px;margin: 0;padding: 0"></td>
            </tr>
            <tr>
                <td rowspan="<?= $rowspan ?>" colspan="<?= $colspan - 3 ?>" class="noColor"
                    style="vertical-align: bottom; text-align: left">
                    <?php if ($isDisplayAmountInWords) : ?>
                        <div class="bold text-left"> AMOUNT IN WORDS:</div>
                        <span class="text-left"> <?= amountToWordsINR($quotation->totalAmount) ?></span>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($displaySubTotal) : ?>
            <tr class="topLine">
                <td colspan="2"
                    class="fixedHeight leftAlign subtotal"><?= Yii::t('template-default', 'SUB TOTAL') ?></td>
                <td class="total fixedHeight subtotal">
                    <?= formatAsCurrency($quotation->subTotalAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($displayDiscount && $discountSettings !== Key::NO_DISCOUNT && $quotation->totalDiscountAmount > 0) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"><?= $discountLabel ?>
                    <?php if ($discountSettings === Key::ON_TOTAL && $quotation->discountType === DiscountType::PERCENTAGE) : ?>
                        (<?= $formatter->asDecimal($quotation->discountPercentage, 2) ?>%)
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= "- " . formatAsCurrency($quotation->totalDiscountAmount) ?>
                </td>
            </tr>
        <?php endif; ?>
        <?php if ($displayTax) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"> <?= $appSettings[Key::TAX_LABEL] ?>
                    <?php if ($taxSettings !== Key::PER_ITEM) : ?>
                        (<?= $formatter->asDecimal($quotation->taxPercentage, 2) ?>%)
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= formatAsCurrency($quotation->totalTaxAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($quotation->otherCharges > 0) : ?>
            <tr>
                <td colspan="2"
                    class="fixedHeight leftAlign"> <?= $quotation->otherChargesLabel ?? Yii::t('template-default', 'Other Charges') ?>
                    <?php if ($quotation->isOtherChargesTaxable && $quotation->otherChargesTaxPercentage > 0) : ?>
                        <br/>
                        <span class="text-muted"> (<?= $formatter->asDecimal($quotation->otherChargesTaxPercentage, 2) ?>%) </span>
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= formatAsCurrency($quotation->otherCharges) ?>
                    <?php if ($quotation->isOtherChargesTaxable && $quotation->otherChargesTaxPercentage > 0) : ?>
                        <br/>
                        <span class="text-muted"> <?= formatAsCurrency($quotation->otherChargesTaxAmount) ?> </span>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php
        foreach ($additionFields as $field) :
            $key = $field['key'];
            $label = $field['label'];
            $amount = $field['amount'] ?? null;
            $taxAmount = $field['taxAmount'] ?? null;
            $taxPercentage = $field['taxPercentage'] ?? null;
            if (empty($amount)) {
                continue;
            }
            ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"> <?= $label ?>
                    <?php if ($taxPercentage > 0) : ?>
                        <br/>
                        <span class="text-muted"> (<?= $formatter->asDecimal($taxPercentage, 2) ?>%) </span>
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= formatAsCurrency($amount) ?>
                    <?php if ($taxPercentage > 0) : ?>
                        <br/>
                        <span class="text-muted"> <?= formatAsCurrency($taxAmount) ?> </span>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endforeach; ?>

        <?php
        foreach ($subtractionFields as $field) :
            $key = $field['key'];
            $label = $field['label'];
            $amount = $field['amount'] ?? null;
            if (empty($amount)) {
                continue;
            }
            ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign" style="vertical-align: top"><?= $label ?>
                </td>
                <td class="total fixedHeight">
                    <?= "- " . formatAsCurrency($amount) ?>
                </td>
            </tr>
        <?php endforeach; ?>

        <?php if ($quotation->roundOffAmount != 0 && !($additionalDiscount && $additionalDiscountAmount > 0)) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"><?= Yii::t('template-default', 'Round-off') ?>
                </td>
                <td class="total fixedHeight">
                    <?= formatAsCurrency($quotation->roundOffAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($displayGrandTotal) : ?>
            <tr>
                <td colspan="2"
                    class="grand total leftAlign doubleLine"><?= Yii::t('template-default', 'GRAND TOTAL') ?></td>
                <td class="grand total doubleLine">
                    <?= formatAsCurrency($quotation->totalAmount + $additionalDiscountAmount) ?>
                </td>
            </tr>
            <?php if (!empty($additionalDiscount) && $additionalDiscountAmount > 0) : ?>
                <tr>

                    <td colspan="2"
                        class="total leftAlign"><?= strtoupper($additionalDiscount['label']) ?></td>
                    <td class="total">
                        <?= "- " . formatAsCurrency($additionalDiscount['amount']) ?>
                    </td>
                </tr>
                <?php if ($quotation->roundOffAmount != 0) : ?>
                    <tr>
                        <td colspan="2" class="fixedHeight leftAlign"><?= Yii::t('template-default', 'Round-off') ?>
                        </td>
                        <td class="total fixedHeight">
                            <?= formatAsCurrency($quotation->roundOffAmount) ?>
                        </td>
                    </tr>
                <?php endif; ?>
                <tr>
                    <td colspan="2"
                        class="grand total leftAlign doubleLine">FINAL AMOUNT</td>
                    <td class="grand total doubleLine">
                        <?= formatAsCurrency(($quotation->totalAmount)) ?>
                    </td>
                </tr>
            <?php endif; ?>
        <?php endif; ?>

        <?php if ($displayTotalColumn || $displayGrandTotal) : ?>
        </tbody>
    </table>
<?php endif; ?>

    <br clear="all"/>
    <div id="closingNote">
        <p><?= nl2br($bottomMessage) ?></p>
    </div>

    <?php if ($isPagebreakBeforeTerms) : ?>
        <pagebreak/>
    <?php endif; ?>

    <table style="margin-top: 20px;page-break-inside: avoid;margin-bottom: 5px;">
        <tr>
            <?php if ($terms && count($terms) > 0) : ?>
                <td style="text-align: left;">
                    <div class="term-title">Terms & Conditions:</div>
                    <ul id="terms">
                        <?php foreach ($terms as $item) : ?>
                            <li class="term">
                                <?= nl2br($item->text) ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </td>
            <?php endif; ?>
            <?php if ($isDisplayBankDetails && !empty($business->bankInfo)) : ?>
                <td width="35%" style="text-align:left; vertical-align:top;">
                    <div class="">
                        <div class="term-title"><?= Yii::t('template-default', 'Payment Instructions') ?></div>
                        <div id="payment-info" class="term">
                            <?= nl2br($business->bankInfo) ?>
                        </div>
                    </div>
                </td>
            <?php endif; ?>

            <?php
            $colsWithPayment = 1;
            if ($terms && count($terms) > 0) {
                echo "</tr> <tr>";
                if ($isDisplayBankDetails && !empty($business->bankInfo)) {
                    $colsWithPayment = 2;
                }
            } ?>
            <td width="" colspan="<?= $colsWithPayment ?>" style="text-align: right;">
                <div id="signBlock">
                    <p class="company-name">For, <?= strtoupper($business->name) ?></p>
                    <div class="signatureSpace" style="height: 60px; text-align:right;">
                        <?php if (!empty($signatureImg)) : ?>
                            <img src="<?= $signatureImg ?>" class="signImage"
                                 style="height: 60px; text-align: right;padding:20px"/>&nbsp;
                        <?php else : ?>
                            <br/>
                            <br/>
                            <br/>
                            <br/>
                            <br/>
                        <?php endif; ?>
                    </div>
                    <div class="signature"
                         style=" text-align: right;margin-bottom: 10px;"><?= Yii::t('template-default', 'AUTHORIZED SIGNATURE') ?>
                        <?php if ($isDisplaySalesPerson): ?>
                            <br/>
                            Generated by : <?= $quotation->assignedTo->getName() ?>
                        <?php endif; ?>
                    </div>
                </div>
            </td>
        </tr>
    </table>

    <?php
    $linkAttachments = $quotation->getAttachments(AttachmentType::LINK);
    if (!empty($linkAttachments))  : ?>
        <div class="terms-details">
            <p class="term-title">Attachments:</p>
            <ul id="terms">
                <?php /** @var \common\models\Attachment $attachment */
                foreach ($linkAttachments as $attachment): ?>
                    <li>
                        <?php
                        $paddingClass = "";
                        $attachmentTitle = $attachment->title;
                        if (empty($attachmentTitle)) {
                            $attachmentTitle = $attachment->linkUrl;
                        }
                        ?>
                        <div class="terms-title <?= $paddingClass ?>"><a href="<?=$attachment->linkUrl?>"><?= $attachmentTitle ?></a></div>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

</main>

</body>