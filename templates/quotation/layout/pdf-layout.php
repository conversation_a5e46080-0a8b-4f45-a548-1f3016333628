<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $content string */

$assetsDirUrl = $this->params['assetsDirUrl'];
$margin =  $this->params['margins'];

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="<?= $assetsDirUrl?>/bootstrap.min.css" media="all" />
    <link rel="stylesheet" href="<?= $assetsDirUrl?>/pdf-style.css" media="all" />

    <style type="text/css">
        body{
            padding:<?= $margin->top . "px " .$margin->right . "px " .$margin->bottom . "px " .$margin->left . "px" ?>;
        }
    </style>
    <script>
        // hide element with tag : htmlpageheader & htmlpagefooter
        function hideElement(tag) {
            var elements = document.getElementsByTagName(tag);
            for (var i = 0; i < elements.length; i++) {
                elements[i].style.display = 'none';
            }
        }

        window.onload = function () {
            hideElement('htmlpageheader');
            hideElement('htmlpagefooter');
        }



    </script>

</head>

<?= $content ?>
</html>