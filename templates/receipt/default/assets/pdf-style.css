.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

a {
    color: #001028;
    text-decoration: underline;
}

body {
    position: relative;
    width: 21cm;
    min-height: 29.7cm;
    color: #001028;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    border: 1px solid red;
    font-size: 12px;
    /*font-family: Cambria, Arial, sans-serif;*/
}

#header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
}


#quoteNumber {
    margin-left: 3px;
}

table td#businessLogo {
    text-align: center;
    height: 120px;
    width: 120px;
    /*border: 1px dotted grey;*/
}

.business-logo {
    max-height: 120px;
    max-width: 120px
}

#header {
    width: 100%;
    height: auto;
}

#businessInfo,
#topRight {
    vertical-align: middle;
}

table td#businessInfo {

}

table td#topRight {
    width: 120px;
    text-align: right;
    /*border: 1px dotted grey;*/
}

.taxLabel {
    font-weight: bold;
    margin-right: 2px;
}

table td#clientInfo {
    text-align: left;
}

#businessInfo > div, #clientInfo > div {
    color: black;
    line-height: 1.4em;
    text-align: left;
}

.business-name {
    font-size: 1.4em;
    line-height: 1.4em;
    font-weight: bold;
}

.customer-business-name {
    font-size: 1.0em;
    line-height: 1.4em;
    font-weight: bold;
}

.contact-name {
    font-size: 1.0em;
    line-height: 1.4em;
}

.bold {
    font-weight: bold;
}

.line {
    border-top: 1px solid #C1CED9;
    /*margin-bottom: 10px;*/
}

.iconImg{
    width: 16px;
    height: 16px;
}

.unicodeIcon {
    color: black;
    font-size: 0.3em;
}

.email {
    color: black;
    text-decoration: none;
}

#contact-info {
    font-size: 1.0em;
    line-height: 1.4em;
    font-weight: normal;
    text-align: center;
    margin: 0 0 20px 0;
    border: 1px solid #5D6975;
}

#salutation {
    margin-top: 5px;
}

h1 {
    color: #001028;
    font-size: 1.4em;
    line-height: 1.4em;
    font-weight: normal;
    text-align: left;
    margin: 0 0 0 0;
    max-height: 120px;
    max-width: 120px;

}

hr {
    padding: 0 0 0 0;
    margin: 0 0 0 0;
}

table#topInfo {
    margin-bottom: 10px;
    padding: 0;
}


table#topInfo tr {

}
#topInfo  td#shippingInfo{
    text-align: left;

}
#shippingInfo{
    margin-top: 5px;
    margin-left: 5px;
    text-align:left;
    vertical-align: top;
    width: 30%;
    padding: 10px;
}


table td.receiptInfoParent {
    vertical-align: top;
    max-width: 20%;
}

table .receiptInfo {
    text-align: right;
    border-collapse: separate;
    border-spacing: 10px;
    width: auto;
    vertical-align: top;

}

.noMargin{
    margin: 0;
}
.noPadding {
    padding:0;
}
table#receiptInfo td {
    padding: 5px 0 0;
    width: auto;
    white-space: nowrap;
    vertical-align: top;
}


main {
    flex: 1 1 auto;
    margin-top: 10px;
}

main.withHeaderFooter{
    margin-top: 0;
    margin-left: 25px;
    margin-right: 25px;
}

#clientInfo {
    float: left;
}

#clientInfo span {
    width: 52px;
    margin-right: 10px;
    display: inline-block;
}


#clientInfo {
    vertical-align: top;
    margin-top: 5px;
    margin-left: 5px;
}

#quoteInfo div {
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
}

#clientInfo div {
    white-space: nowrap;
    text-align: left;
}

#closingNote, #salutation .line1 {
    font-size: 1em;
    margin-bottom: 10px;
}

.line1 {
    line-height: 1.3em;
}

#salutation .line2 {
    font-size: 1em;
    line-height: 2.3em;
    margin-bottom: 10px;
}

table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    /* margin-bottom: 20px; */
}

table#receiptItems{
    width: 100%;
}


table#receiptItems tr th {
    white-space: normal;
}

table#receiptItems tr td {
    border-bottom: 1px solid grey;
    font-size: 1.2em;
}

table th,
table td {
    text-align: center;
    font-size: 1.0em;
}

table th {
    padding: 5px 10px;
    border-bottom: 1px solid #5D6975;
    background-color: #f2f4f7;
    white-space: nowrap;
    font-weight: bold;
}

table .category,
table .desc {
    text-align: left;
}

.productName{
    font-weight: bold;
}

.productDesc{
    font-size: 1em;
}


table td {
    padding: 5px;
    text-align: right;
}

table td.right, table th.right {
    text-align: right;
}

table td.left, table th.left {
    text-align: left;
}

table td.center, table th.center {
    text-align: center;
}


table td.category,
table td.desc, td.idx {
    vertical-align: top;
}

table td.desc {
    min-width: 40%;
}

table td.price,
table td.qty,
table td.tax,
table td.discount,
table td.hsn,
table td.total {
    vertical-align: top;
    white-space:nowrap;
}
.vertical-top,table td.vertical-top{
    vertical-align: top;
}
table td.subtotal {
    padding-top: 5px !important;
}

table td.grand {
    padding: 10px !important;
    background-color: #f2f4f7;
    vertical-align: middle;
}

table td.tax .amt {
}

table td.hsn {
    text-align: left;
}


.percent,.unit {
    font-size: 1em;
    color: #001028;
}

.noBorder {
    border: none;
}
.topLine {
    border-top: 1px solid #5D6975;
}

table tr.topLine td {
    border-top: 1px solid #5D6975;
}

.leftLine {
    border-left: 1px solid #5D6975;
}

.rightLine,
.rightLine :first-child {
    border-right: 1px solid #5D6975;
}

.bottomLine {
    border-bottom: 1px solid #5D6975;
}

.term {
    color: #001028;
    font-size: 1em;
}

footer {
    flex: 0 1 55px;
    text-align: center;
    background-color: #ffa52c;
    width: 100%;
    padding: 8px 0;
    position: relative;
}

#websiteDiv {
    position: absolute;

}

.website {
    text-align: center;
    vertical-align: middle;
    letter-spacing: 2px;
    font-weight: bold;
    background: transparent !important;
}

#brandLogo {
    text-align: right;
    vertical-align: middle;
    display: table-cell;
    position: absolute;
    right: 10px;
}

#brandLogo img {
    max-height: 60px;
}

table td.terms-details {
    clear: both;
    text-align: left;
    padding-top: 20px;
    padding-right: 30px;
}

table td.fixedHeight, table td.doubleLine {
    height: 25px;
    padding-top: 5px;
    padding-bottom: 5px;
    vertical-align: middle !important;
    border: none;
}

table td.doubleLine {
    border-top: 1px solid #5D6975;
    border-bottom: 2px double #5D6975;
}

.term-title {
    font-size: 1em;
    font-weight: bold;
}

ul#terms {
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 30px;
    margin-top: 5px;
}

.company-name {
    font-size: 1.2em;
    text-align: right;
    font-weight: bold;
    margin: 0;
}

table .noColor {
    background-color: white !important;
    border-color: white;
    border-bottom: none !important;
}

.signature,  td .signature {
    text-align: right;
    margin-bottom: 10px;
}

.signImage, td .signImage {
    height: 60px;
    text-align: right;
}

.signatureSpace, td .signatureSpace {
    height: 60px;
    text-align: right;
}

.v-center {
    vertical-align: middle;
}

.h-center {
    text-align: center;
}

.leftPart {
    float: left;
    width: 53%;
    /* background: #F5F5F5; */
}

.rightPart {
    float: right;
    width: 45%;
}

table td.leftAlign {
    text-align: left;
}

table td.centerAlign {
    text-align: center;
}

#payment-info td {
    /*font-size: 1.2em;*/
}

td #signBlock {
    padding-top: 15px;
    page-break-inside: avoid;
}

#footer.withHeaderFooter{
    page-break-inside: avoid;
}

.font-16 {
    font-size: 0.8em;
}

/* New styles for mPDF compatibility */
table th.payment-details-header {
    text-align: left;
    font-size: 1.2em;
}

table td.payment-details-row-label {
    width: 200px;
    text-align: left;
    font-weight: bold;
}

table td.payment-details-row-value {
    text-align: left;
}

.paid-amount-grand-total {
    text-align: left;
    font-size: 1.2em;
    font-weight: bold;
    background-color: #f2f4f7;
}

.paid-amount-grand-total-label {
    background-color: #f2f4f7;
}

/* Add these styles to your pdf-style.css file */

.leftPart {
    width: 70%;
}

.paid-amount-label {
    font-size: 1.3em;
    font-weight: bold;
    background-color: #f2f4f7;
    padding: 5px;
}

.paid-amount-value{
    font-size: 1.3em;
    font-weight: bold;
    padding: 5px;
}
.payment-words-label {
    font-size: 1.3em;
    font-weight: bold;
    background-color: #f2f4f7;
    padding: 5px;
}

.payment-words-value{
    font-size: 1.3em;
    font-weight: bold;
    padding: 5px;
}
