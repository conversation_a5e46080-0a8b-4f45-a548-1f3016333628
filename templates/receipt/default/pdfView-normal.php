<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $this yii\web\View */
/* @var $template common\models\Template */
/* @var $business common\models\Business */
/* @var $customSettings [] */
/* @var $appSettings [] */
/* @var $receipt common\models\Receipt */
/* @var $receiptItems common\models\ReceiptItems[] */
/* @var $terms common\models\TermsCondition[] */
/* @var $otherInfo common\models\enum\OtherInfo */
/* @var $shouldDisplayWatermark boolean */
/* @var $watermarkLink string */
/* @var $currentUser common\models\User */
/* @var $assetsUrl string */

use common\models\Business;
use common\models\enum\DiscountType;
use common\models\enum\Key;
use yii\i18n\Formatter;


$this->title = $receipt->id;
Business::setBusiness($receipt->businessId);
// Fetch settings
$receiptSettings = $receipt->getSettingsData();

// fetch and set vars for Receipt Settings

$discountSettings = $receiptSettings[Key::DISCOUNT_SETTINGS]; // "no_discount", "no_discount,per_item,on_total"
$taxRateOnTotal = $receiptSettings[Key::TAX_RATE_ON_TOTAL];
$taxSettings = $receiptSettings[Key::TAX_SETTINGS]; // "no_tax", "no_tax,per_item,on_total"
$topMessage = $receiptSettings[Key::TOP_MESSAGE];
$bottomMessage = $receiptSettings[Key::BOTTOM_MESSAGE];
$isDisplaySignatureBlock = $receiptSettings[Key::isDisplaySignatureBlock] ?? 1;

// Multi-user settings
$multiUserSettings = $business->settings(Key::GROUP_MULTI_USER);
$allowUserSignature = $multiUserSettings[Key::allowUserSignature] ?? 0;

// Determine which signature to use
$signatureImg = null;
if ($allowUserSignature && isset($currentUser) && !empty($currentUser->signatureImg)) {
    $signatureImg = $currentUser->signatureImg;
} elseif (!empty($business->signatureImg)) {
    $signatureImg = $business->signatureImg;
}

// settings end

/** @var Formatter $formatter */
$formatter = Yii::$app->formatter;

$formatter->locale = $receiptSettings[Key::LOCALE_CODE] ?? $appSettings[Key::LOCALE_CODE];

$formatter->dateFormat = $appSettings[Key::DATE_FORMAT];
$business = $receipt->business;
$businessUser = $business->owner;
$customer = $receipt->customer;

// Define constants
const MOBILE_ICON = "\icons\phone.svg";
const EMAIL_ICON = "\icons\email.svg";
const WATERMARK_URL = "www.quotationmaker.app";

// assets images
$mobileLogo = $assetsUrl . MOBILE_ICON;
$emailLogo = $assetsUrl . EMAIL_ICON;

$isIndian = $business->regionCode === "IN";
$isDisplayAmountInWords = true;
$taxLabel = $isIndian ? "GSTIN" : $appSettings[Key::TAX_LABEL];
if ($formatter->locale === "en-ZX") {
    $formatter->locale = "en-ZW";
    $formatter->currencyCode = "ZWL";
}

if ($formatter->locale === "en-ZG") {
    $formatter->locale = "en-ZW";
    $formatter->currencyCode = "ZiG";
}

?>
<?php
$isHeaderFooterEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED] && $template->isHeaderFooterEnable;
$shouldRepeatHeaderFooter = $appSettings[Key::shouldRepeatHeaderFooter];
$isHeaderEnabled = !empty($business->headerImg) && $isHeaderFooterEnabled;
$isFooterEnabled = !empty($business->footerImg) && $isHeaderFooterEnabled;
$withHeaderFooter = "";
if ($isHeaderEnabled) {
    $headerImage = $business->headerImg;
    $footerImage = $business->footerImg;
    $withHeaderFooter = "withHeaderFooter";
}
?>
<?php if ($isHeaderEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpageheader name="pdf_header">
        <img src="<?= $headerImage ?>">
    </htmlpageheader>
<?php endif; ?>
<?php if ($isFooterEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpagefooter name="pdf_footer">
        <img src="<?= $footerImage ?>">
    </htmlpagefooter>
<?php else : ?>
    <htmlpagefooter name="pdf_footer">
        <?php if ($shouldDisplayWatermark):
            // Resolve the image path using Yii2 alias
            $watermarkPath = Yii::getAlias('@backend/web/img/logo/watermark.png');
            ?>
            <div class="absolute-div-content"
                 style="position: absolute; top: 96%;  left: 5%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
                <a href="<?= $watermarkLink ?>"><img src="<?= $watermarkPath ?>" alt="Logo" width="auto" height="50"
                                                     style="padding-bottom: 5px"/></a>
            </div>
            <div class="absolute-div-content"
                 style="position: absolute; top: 97%;  left: 43%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
                <a href="<?= $watermarkLink ?>" style="color: darkgray;"><?= WATERMARK_URL ?></a>
            </div>    <?php endif; ?>
        <div class="absolute-div-content"
             style="position: absolute; top: 97%;  left: 87%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
            Page {PAGENO} of {nb}
        </div>
    </htmlpagefooter>
<?php endif; ?>

<body>
<?php if ($isHeaderEnabled && !$shouldRepeatHeaderFooter) :
    $margin = $template->getMarginSettings();
    $mainMarginTop = $margin->topWithHeaderImage + (6 * $margin->top);
    $absMarginTop = -1 * $margin->top;
    ?>
    <div class="<?= $withHeaderFooter ?>" style="position: absolute; top: <?= $absMarginTop ?>px;  left: 0;">
        <img src="<?= $headerImage ?>" width="100%" height="auto">
    </div>
    <div style="height: <?= $mainMarginTop ?>">&nbsp;</div>
<?php elseif (!$isHeaderEnabled) : ?>
    <div id="header">
        <table>
            <tr>
                <?php
                $alignClass = "leftAlign";
                if (!empty($business->image)) :
                    $alignClass = "centerAlign";
                    ?>
                    <td id="businessLogo">
                        <img src="<?= $business->image ?>" alt="Business Logo" class="business-logo"/>
                    </td>
                <?php endif; ?>

                <td id="businessInfo" class="<?= $alignClass ?>">
                    <div class="business-name">
                        <?= $business->name ?>
                    </div>
                    <div class="contact-name">
                        <?= $business->contactName ?>
                    </div>
                    <div class="business-address">
                        <?php if (!empty($business->addressLine1)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine1 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine2)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine2 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine3)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine3 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->otherInfo)) : ?>
                            <div class="addressLine">
                                <?= $business->otherInfo ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if (!empty($business->phoneNumber)) : ?>
                            <span class="phone-number">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-phone" viewBox="0 0 16 16">
                                        <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                        <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                                    </svg>
                                    <?= $business->phoneNumber ?>
                                </span>
                        <?php endif; ?>
                        <?php if (!empty($business->email)) : ?>
                            <span class="email">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-envelope" viewBox="0 0 16 16">
                                        <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                                    </svg>
                                    <a class="email" href="mailto: <?= $business->email ?>"><?= $business->email ?></a>
                                </span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($business->taxNumber)) : ?>
                        <div class="tax-number">
                            <span class="taxLabel"><?= $business->taxLabel ?></span>
                            <span class="taxNumber"><?= $business->taxNumber ?></span>
                        </div>
                    <?php endif; ?>
                </td>
                <td id="topRight" class="text-center">
                    <h1 class="bold">Receipt</h1>
                </td>
            </tr>
        </table>
    </div>
    <hr/>
<?php endif; ?>
<main class="<?= $withHeaderFooter ?>" style="">
    <table id="topInfo">
        <tr>
            <td id="clientInfo">
                <div class="bold font-16">TO,</div>
                <?php if (!empty($customer->companyName)): ?>
                    <div class="customer-business-name">
                        <?= $customer->companyName ?>
                    </div>
                <?php endif; ?>
                <?php if (!empty($customer->name)): ?>
                    <div class="contact-name">
                        <?= $customer->name ?>
                    </div>
                <?php endif; ?>
                <div class="business-address">
                    <?php if (!empty($customer->addressLine1)): ?>
                        <div class="addressLine">
                            <?= $customer->addressLine1 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine2)): ?>

                        <div class="addressLine">
                            <?= $customer->addressLine2 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine3)): ?>
                        <div class="addressLine">
                            <?= $customer->addressLine3 ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div>
                    <?php if (!empty($customer->phoneNumber)): ?>
                        <div class="phone-number">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-phone" viewBox="0 0 16 16">
                                <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z"/>
                                <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>
                            </svg> <?= $customer->phoneNumber ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->email)): ?>
                        <div class="email mt-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-envelope" viewBox="0 0 16 16">
                                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                            </svg>&nbsp; <?= $customer->email ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if (!empty($customer->taxNumber)): ?>
                    <div class="tax-number">
                        <span class="taxLabel"> <?= $taxLabel ?> </span>
                        <span class="taxNumber"><?= $customer->taxNumber ?></span>
                    </div>
                <?php endif; ?>

                <?php if (!empty($customer->otherInfo)): ?>
                    <div class="addressLine">
                        <?= nl2br($customer->otherInfo) ?>
                    </div>
                <?php endif; ?>
            </td>

            <td class="receiptInfoParent" width="275">
                <table id="receiptInfo">
                    <?php if ($isHeaderEnabled): ?>
                        <tr>
                            <td id="topRight" colspan="2">
                                <h1 class="bold">Receipt</h1>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" height="5px">&nbsp;</td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td class="bold noMargin noPadding"><?= Yii::t('template-default', 'Receipt#') ?></td>
                        <td id="quoteNumber  noMargin noPadding"><?= $receipt->receiptNumber ?> </td>
                    </tr>

                    <tr>
                        <td class="bold"><?= Yii::t('template-default', 'Receipt Date:') ?></td>
                        <td><?= $formatter->asDate($receipt->receiptDate) ?></td>
                    </tr>

                    <?php if (!empty($receipt->invoiceReferenceNumber)): ?>
                        <tr>
                            <td class="bold">INVOICE#</td>
                            <td id="quoteNumber"><?= $receipt->invoiceReferenceNumber ?> </td>
                        </tr>
                    <?php endif; ?>

                    <?php if (!empty($receipt->paymentMethod)): ?>
                        <tr>
                            <td class="bold">Payment Method</td>
                            <td id="quoteNumber"><?= $receipt->paymentMethod ?> </td>
                        </tr>
                    <?php endif; ?>

                    <?php if (!empty($receipt->paymentReferenceNumber)): ?>
                        <tr>
                            <td class="bold">Reference #</td>
                            <td id="quoteNumber"><?= $receipt->paymentReferenceNumber ?> </td>
                        </tr>
                    <?php endif; ?>

                    <?php if (!empty($otherInfo)): ?>
                        <?php /** @var \common\models\enum\OtherInfo $info */
                        foreach ($otherInfo as $info): ?>
                            <tr>
                                <td class="bold"><?= $info->label ?></td>
                                <td><?= $info->value ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </table>
            </td>
        </tr>
    </table>
    <?php if (!empty($topMessage)): ?>
        <div id="salutation">
            <div class="line2"><span><?= nl2br($topMessage) ?></span></div>
        </div>
    <?php endif; ?>


    <br clear="all"/>
    <?php if (!empty($bottomMessage)): ?>
        <div id="closingNote">
            <p><br/><?= nl2br($bottomMessage) ?></p>
        </div>
    <?php endif; ?>

    <table style="margin-top: 20px;page-break-inside: avoid;margin-bottom: 5px;">
        <tr>
            <td colspan="" style="text-align: left;">
                <?php if (!empty($receipt->paymentMethod)): ?>
                    <strong>Payment Method:</strong> <?= $receipt->paymentMethod ?><br/>
                <?php endif; ?>
                <?php if (!empty($receipt->paymentReferenceNumber)): ?>
                    <strong>Reference Number:</strong> <?= $receipt->paymentReferenceNumber ?><br/>
                <?php endif; ?>
                <?php if (!empty($receipt->paymentNote)): ?>
                    <strong>Payment For: </strong> <?= $receipt->paymentNote ?><br/>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <td width="" colspan="" style="text-align: right;">
                <div id="signBlock">
                    <p class="company-name">For, <?= strtoupper($business->name) ?></p>
                    <div class="signatureSpace" style="height: 60px; text-align:right;">
                        <?php if (!empty($signatureImg)) : ?>
                            <img src="<?= $signatureImg ?>" class="signImage"
                                 style="height: 60px; text-align: right;padding:20px"/>&nbsp;
                        <?php else : ?>
                            <br/>
                            <br/>
                            <br/>
                            <br/>
                            <br/>
                        <?php endif; ?>
                    </div>
                    <div class="signature"
                         style=" text-align: right;margin-bottom: 10px;"><?= Yii::t('template-default', 'AUTHORIZED SIGNATURE') ?>
                        <br/>
                        <span class="bold"> Received by : <?= $receipt->createdBy->getName() ?></span>
                    </div>
                </div>
            </td>
        </tr>
    </table>

</main>

</body>