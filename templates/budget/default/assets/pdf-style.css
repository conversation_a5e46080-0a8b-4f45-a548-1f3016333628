:root {
    --blue: #1e90ff;
    --white: #ffffff;
}
.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

a {
    color: #001028;
    text-decoration: underline;
}

body {
    position: relative;
    width: 21cm;
    min-height: 29.7cm;
    color: #001028;
    background: #FFFFFF;
    display: flex;
    flex-flow: column;
    border: 1px solid red;
    /*font-family: Cambria, Arial, sans-serif;*/
}

#header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
}


#quoteNumber {
    margin-left: 3px;
}

table td#businessLogo {
    text-align: center;
    height: 120px;
    width: 120px;
    /*border: 1px dotted grey;*/
}

.business-logo {
    max-height: 120px;
    max-width: 120px
}

#header {
    width: 100%;
    height: auto;
}

#businessInfo,
#topRight {
    vertical-align: middle;
}

table td#businessInfo {
    padding-left: 0;
}

table td#topRight {
    width: 120px;
    text-align: right;
    /*border: 1px dotted grey;*/
}

.taxLabel {
    font-weight: bold;
    margin-right: 2px;
}

table td#clientInfo {
    text-align: left;
}

#businessInfo > div, #clientInfo > div {
    color: black;
    line-height: 1.4em;
    text-align: left;
}



.business-name {
    font-size: 1.4em;
    line-height: 1.4em;
    font-weight: bold;
}

.customer-business-name {
    font-size: 1.0em;
    line-height: 1.4em;
    font-weight: bold;
}

.contact-name {
    font-size: 1.0em;
    line-height: 1.4em;
}

.bold {
    font-weight: bold;
}

.line {
    border-top: 1px solid #C1CED9;
    /*margin-bottom: 10px;*/
}

.iconImg{
    width: 16px;
    height: 16px;
}

.unicodeIcon {
    color: black;
    font-size: 0.3em;
}

.email {
    color: black;
    text-decoration: none;
}

#contact-info {
    font-size: 1.0em;
    line-height: 1.4em;
    font-weight: normal;
    text-align: center;
    margin: 0 0 20px 0;
    border: 1px solid #5D6975;
}

#salutation {
    margin-top: 5px;
}

h1 {
    color: #001028;
    font-size: 1.4em;
    line-height: 1.4em;
    font-weight: normal;
    text-align: left;
    margin: 0 0 0 0;
    max-height: 120px;
    max-width: 120px;

}

hr {
    padding: 0 0 0 0;
    margin: 0 0 0 0;
}

table#topInfo {
    margin-bottom: 20px;
    padding: 0;
}


table#topInfo tr {

}

table#topInfo td {
    padding: 0;
}

#topInfo  td#shippingInfo{
    text-align: left;

}
#shippingInfo{
    margin-top: 5px;
    margin-left: 5px;
    text-align:left;
    vertical-align: top;
    width: 30%;
    padding: 10px;
}


table td#quotationInfoParent {
    vertical-align: top;
    max-width: 20%;
}
table td.quotationInfoParent{
    vertical-align: top;
    text-align: right;
}
table#quotationInfo {
    text-align: right;
    border-collapse: separate;
    border-spacing: 10px 0;
    width: auto;
    vertical-align: top;
    display: inline-block;

}
.noMargin{
    margin: 0;
}
.noPadding {
    padding:0;
}
table#quotationInfo td {
    padding: 5px 0 0;
    width: auto;
    white-space: nowrap;
    vertical-align: top;
}


main {
    flex: 1 1 auto;
    margin-top: 10px;
}
main.withHeaderFooter{
    margin-top: 0;
    margin-left: 25px;
    margin-right: 25px;
}

#clientInfo {
    float: left;
}

#clientInfo span {
    width: 52px;
    margin-right: 10px;
    display: inline-block;
}


#clientInfo {
    vertical-align: top;
    margin-top: 5px;
    margin-left: 5px;
}

#quoteInfo div {
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
}

#clientInfo div {
    white-space: nowrap;
    text-align: left;
}

#closingNote, #salutation .line1 {
    font-size: 1em;
    margin-bottom: 10px;
}

.line1 {
    line-height: 1.3em;
}

#salutation .line2 {
    font-size: 1em;
    line-height: 2.3em;
    margin-bottom: 10px;
}
/* reset table style */
table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;

    /* margin-bottom: 20px; */
}
table th,
table td {
    text-align: left;
    font-size: 1.0em;
    padding: 5px;
}

table th {
    padding: 5px 10px;
    border-bottom: 1px solid #5D6975;
    background-color: #bdd7ee;
    white-space: nowrap;
    font-weight: bold;
}

table.withBorder td, table.withBorder th{
    border: 1px solid #0b3251;
}

table#quotationItems{
    width: 100%;
}
table#quotationItems tr th {
    white-space: normal;
}

table#quotationItems tr td {
    /*border-bottom: 1px dotted grey;*/
}

table .category,
table .desc {
    text-align: left;
}

.productName{
    font-weight: bold;
}

.productDesc{
    font-size: 12px;
}


table td.right, table th.right {
    text-align: right;
}

table td.left, table th.left {
    text-align: left;
}

table td.center, table th.center {
    text-align: center;
}


table td.category,
table td.desc, td.idx {
    vertical-align: top;
}

table td.desc {
    min-width: 40%;
}

table td.price,
table td.qty,
table td.tax,
table td.discount,
table td.hsn,
table td.total {
    vertical-align: top;
    white-space:nowrap;
    text-align: right;
}

table td.subtotal {
    padding-top: 5px !important;
    text-align: right;

}

table td.grand {
    padding-top:  10px !important;
    padding-bottom:  10px !important;
    background-color: #f2f4f7;
    vertical-align: middle;
    text-align: right;

}

table td.tax .amt {
    text-align: right;
}

table td.hsn {
    text-align: left;
}


.percent,.unit {
    font-size: 0.8em;
    color: #001028;
}

.topLine {
    border-top: 1px solid #5D6975;
}

table tr.topLine td {
    border-top: 1px solid #5D6975;
}

.leftLine {
    border-left: 1px solid #5D6975;
}

.rightLine,
.rightLine :first-child {
    border-right: 1px solid #5D6975;
}

.bottomLine {
    border-bottom: 1px solid #5D6975;
}

.term {
    color: #001028;
    font-size: 1em;
}

footer, #footer {
    width: 100%;
    padding: 8px 0;
    position: relative;
    page-break-inside: auto;
}

#websiteDiv {
    position: absolute;

}

.website {
    text-align: center;
    vertical-align: middle;
    letter-spacing: 2px;
    font-weight: bold;
    background: transparent !important;
}

#brandLogo {
    text-align: right;
    vertical-align: middle;
    display: table-cell;
    position: absolute;
    right: 10px;
}

#brandLogo img {
    max-height: 60px;
}

table td.terms-details {
    clear: both;
    text-align: left;
    padding-top: 20px;
    padding-right: 30px;
}

table td.fixedHeight, table td.doubleLine {
    min-height: 25px;
    padding-top: 5px;
    padding-bottom: 5px;
    vertical-align: middle !important;
}

table td.doubleLine {
    border-top: 1px solid #5D6975;
    border-bottom: 2px double #5D6975;
}

.term-title {
    font-size: 1em;
    font-weight: bold;
}

ul#terms {
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 30px;
    margin-top: 5px;
}

.company-name {
    font-size: 1.2em;
    text-align: right;
    font-weight: bold;
    margin: 0;
}

table td.noColor {
    background-color: white !important;
    border-bottom: none;
}

table td.noBorder, .noBorder {
    border: none;
}

.signature {
    text-align: right;
    margin-bottom: 10px;
}

.signImage {
    height: 60px;
    text-align: right;
}

.signatureSpace {
    height: 60px;
    text-align: right;
}

.v-center {
    vertical-align: middle;
}

.h-center {
    text-align: center;
}

.leftPart {
    float: left;
    width: 53%;
    /* background: #F5F5F5; */
}

.rightPart {
    float: right;
    width: 45%;
}

table td.leftAlign {
    text-align: left;
}

table td.centerAlign {
    text-align: center;
}

#payment-info {
    padding-right: 30px;
    margin-top: 5px;
}

#signBlock {
    padding-top: 15px;
    page-break-inside: avoid;
}

.font-16 {
    font-size: 14px;
}
