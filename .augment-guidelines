# Augment Guidelines for Quotation Pro Project

This document provides comprehensive guidelines for AI agents working with the Quotation Pro project. It outlines the project structure, key components, security considerations, and best practices for making changes to the codebase.

## Project Overview

Quotation Pro is a **multi-tenant business management API** built on the Yii2 PHP framework for creating quotations, invoices, budgets, and purchase orders with **complete data isolation between businesses**. The application serves as a comprehensive business document management system with advanced PDF generation, email notifications, and subscription management.

### Core Architecture

The application follows a **modular architecture** with these primary components:

- **API Module** (Primary Focus): RESTful API for mobile and web clients
- **Common Module** (Primary Focus): Shared models, components, and services
- **Console Module** (Primary Focus): Command-line utilities and scheduled tasks
- **Templates** (Primary Focus): PDF templates for various document types
- **Backend Module** (Secondary Focus): Administrative interface

### Multi-Tenant Design

- **Business Entity**: Central data model with all business-specific data linked to Business entity
- **User-Business Relationship**: Many-to-many relationship via user_business junction table
- **Data Isolation**: Complete separation through businessId foreign key in all business-related tables
- **Role-Based Access**: Business owners have full access, staff users have limited permissions

## Key Functionalities

### Document Management
- **Quotations**: Create, manage, and send quotations to clients with PDF generation
- **Invoices**: Generate invoices from quotations with GST support for Indian businesses
- **Budgets**: Plan and manage budgets for projects
- **Purchase Orders**: Manage purchase orders for suppliers
- **Proforma Invoices**: Handle proforma invoices with proper workflow
- **Delivery Notes**: Generate and manage delivery notes
- **Receipts**: Create and manage payment receipts

### Business Management
- **Customer Management**: Customer database with contact information and history
- **Product Catalog**: Product and service listings with pricing and inventory
- **User Management**: Role-based access control with business owners and staff
- **Business Settings**: Configurable business profiles, tax settings, and document numbering

### Technical Features
- **PDF Generation**: Template-based PDF generation with mPDF library and multiple design variants
- **Email System**: SMTP load balancing with 300 emails per account limit and template-based composition
- **Subscription Management**: Stripe and Razorpay integration with usage tracking and plan limitations
- **Multi-Tenant Architecture**: Complete data isolation between businesses
- **API Authentication**: Token-based authentication with AUTH_TOKEN header validation
- **Configuration System**: Layered configuration with environment-specific settings

## Directory Structure

```
quotation-pro/
├── api/                    # API implementation
│   ├── config/             # API-specific configuration
│   ├── modules/            # API modules (versioned)
│   │   └── v1/             # Version 1 of the API
│   │       ├── controllers/# API controllers
│   │       └── models/     # API-specific models
│   └── web/                # Web entry point for API
├── common/                 # Shared code
│   ├── app-config/         # Application-specific configurations
│   ├── components/         # Reusable components
│   ├── config/             # Common configuration
│   │   └── apps-settings/  # Environment-specific settings
│   ├── helpers/            # Utility functions
│   ├── mail/               # Email templates
│   ├── models/             # Data models
│   │   ├── base/           # Base models (Gii-generated)
│   │   ├── enum/           # Enumeration classes
│   │   ├── query/          # Query classes
│   │   └── traits/         # Shared traits
│   └── services/           # Service classes
├── console/                # Command-line utilities
│   ├── controllers/        # Console command controllers
│   ├── migrations/         # Database migrations
│   └── models/             # Console-specific models
├── templates/              # PDF templates
│   ├── budget/             # Budget templates
│   ├── delivery_note/      # Delivery note templates
│   ├── invoice/            # Invoice templates
│   ├── proforma_invoice/   # Proforma invoice templates
│   ├── purchase_order/     # Purchase order templates
│   ├── quotation/          # Quotation templates
│   └── receipt/            # Receipt templates
└── docs/                   # Documentation files
```

## Documentation Structure

The documentation is organized into several key files in the `docs/` directory:

1. **README.md**: Main entry point for comprehensive API documentation
2. **PROJECT_DOCUMENTATION.md**: Overview of project architecture and components
3. **project_overview.md**: Detailed system architecture, data flow, and entity relationships
4. **project_structure.md**: Directory structure and file organization
5. **configuration_system.md**: Layered configuration system and environment settings
6. **pdf_generation_system.md**: PDF template system and generation process
7. **email_system_documentation.md**: Email system architecture and components
8. **API_DOCUMENTATION.md**: Comprehensive API endpoints and usage
9. **API_CONFIGURATION_GUIDE.md**: Configuration system guide
10. **API_DEVELOPMENT_PROMPT.md**: API development guidelines
11. **MODEL_DEVELOPMENT_PROMPT.md**: Model development guidelines
12. **AI_AGENT_GUIDE.md**: Guidelines for AI agents working on the project
13. **security/README.md**: Security documentation and guidelines

## Critical Security Considerations

⚠️ **IMPORTANT**: The following critical security issues have been identified and must be addressed:

### Immediate Security Concerns
1. **Hardcoded Master Password**: `masterPassword => 'qpro.umn.nik@titanium#608'` in `common/config/params.php` (Line 202)
2. **Firebase Keys in Version Control**: Firebase server key exposed in params.php (Line 69)
3. **Master Password Authentication Bypass**: Allows login without proper password validation in UserController
4. **SQL Injection Risks**: Raw SQL execution in `execSql()` function and dynamic query building
5. **Weak Password Requirements**: Only 6 characters minimum requirement

### Security Best Practices
- **Never commit sensitive data** to version control
- **Use environment variables** for all credentials and API keys
- **Implement proper input validation** for all user inputs
- **Use parameterized queries** to prevent SQL injection
- **Add rate limiting** on authentication endpoints
- **Implement CSRF protection** for web forms

## Key Subsystems

### Email System

The email system has been refactored to follow a modular architecture with clear separation of concerns:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level email operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient for the underlying infrastructure

#### EmailClient Features
- Singleton pattern implementation for consistent configuration
- SMTP management with multiple providers and load balancing
- Mailer factory methods for different types of mailers
- Email tracking with counters for sent emails
- Rate limiting for SMTP providers
- Urgent admin notifications

#### EmailService Features
- Email composition with template support
- Specialized methods for different types of emails
- Error handling with centralized logging
- User and admin email sending capabilities

#### Email System Usage
- Create a new `EmailService` instance for email operations (not a singleton)
- Use `EmailClient` only for low-level mailer operations
- Follow the existing template structure for new email templates
- Always wrap email sending in try-catch blocks and log errors

### PDF Generation System

- **Template-based using mPDF library**: Uses mPDF for high-quality PDF generation
- **Multiple Template Variants**: Each document type has multiple design variants (default, modern, etc.)
- **GST Template Support**: For invoice and proforma_invoice, there are two template files:
  - `pdfView.php`: Standard template
  - `pdfView-gst.php`: GST-specific template for Indian businesses
  - Template selection based on `isGstApplicable` condition on the business
- **Dynamic Content Generation**: Templates use PHP variables for dynamic content
- **Business Settings Integration**: Template behavior controlled by business-specific settings
- **Conditional Display Elements**: Features like signature blocks, bank details, UPI details controlled by settings
- **Support for Custom Headers, Footers, and Watermarks**: Configurable branding elements

### Configuration System

**Layered Configuration Approach**:
1. **Base Configuration**: `common/config/main.php` and `common/config/params.php`
2. **Environment Configuration**: `common/config/apps-settings/{APP_ID}/main.php` and `params.php`
3. **Application Settings**: Dynamic settings stored in database with defaults in `app-settings-params.php`
4. **Local Overrides**: Developer-specific settings in `*-local.php` files (not in version control)

**Environment Support**:
- `quotation-dev`: Development environment
- `quotation-prod`: Production environment
- `quotation-local`: Local development environment
- `quotation-premium`: Premium version
- `estimate-prod`: Estimate Maker variant

**Business Settings**: Stored in database with group/key/value pattern (app, quotation, invoice, purchase_order, custom groups)

### Multi-Tenant Architecture

- **Business Entity**: Central data model with all business-specific data linked to Business entity
- **Data Isolation**: Complete separation through businessId foreign key in all business-related tables
- **User-Business Relationship**: Many-to-many via user_business junction table
- **Access Control**: Business owners have full access, staff users have role-based permissions
- **Business Settings**: Configurable settings stored in database accessible via `Business::config()` method

### API Architecture

- **RESTful Design**: API versioning (currently v1) with consistent endpoint structure
- **Authentication**: Token-based authentication via AUTH_TOKEN header
- **Base Controller**: `BaseApiController` provides consistent response formatting and error handling
- **Identity System**: `Identity` class implements Yii2's IdentityInterface for user authentication
- **Response Format**: Standardized JSON responses with success/error status, data, timestamp, and execution time
- **Multi-Tenant Aware**: All API requests filtered by business context

### Document Generation Flow

**Standard Flow**: Client Request → Authentication → Business Context → Data Validation → Document Creation → PDF Generation → Response with document data and PDF URL

**Components**:
- **Document Models**: Quotation, Invoice, Receipt, etc. with business logic
- **PdfService**: Singleton service for PDF generation using mPDF library
- **Template System**: Multiple variants per document type with customizable styling
- **File Management**: Organized storage with business-specific directories

## Development Guidelines

### General Approach

1. **Understand Before Changing**: Always thoroughly understand the existing code before making changes
2. **Detailed Planning**: Create a detailed plan before implementing changes
3. **Conservative Changes**: Be conservative with changes, especially to core components
4. **Documentation Updates**: Keep documentation updated with any code changes
5. **Testing**: Test changes thoroughly before considering them complete
6. **Follow DRY Principal**: Avoid repeating yourself in code
7. **SOLID Principal**: Adhere to SOLID principles for code design
8. **Recheck**: After making changes, recheck the implementation thoroughly to ensure it works as expected also check if you can apply DRY in implemented code and refactor.

## Review
- Take more time to thoroughly understand the codebase structure before implementing changes
- Pay closer attention to inheritance patterns and framework-specific conventions (like Yii2's base/extended model
pattern)
- Think more carefully about database operations to ensure they're efficient and necessary
- Double-check my work for consistency and correctness before presenting solutions
- Consider the long-term maintainability of any changes I propose

### Code Style and Conventions

1. **PSR Standards**: Follow PSR-1, PSR-2, and PSR-4 coding standards
2. **Yii2 Conventions**: Follow Yii2 framework conventions for controllers, models, and views
3. **Naming Conventions**:
   - **Models**: Singular, CamelCase (e.g., `User.php`, `Quotation.php`)
   - **Controllers**: Plural, CamelCase with Controller suffix (e.g., `UsersController.php`)
   - **Services**: CamelCase with Service suffix (e.g., `EmailService.php`, `PdfService.php`)
4. **Documentation**: Use PHPDoc comments for classes, methods, and properties
5. **Type Hints**: Use PHP type for parameters and return types

### Multi-Tenant Development

1. **Business Isolation**: **ALWAYS** scope queries by businessId to maintain data isolation
2. **Business Context**: Use `$this->business` in controllers to access current business context
3. **Business-Scoped Queries**: Implement `findByBusiness()` methods in models
4. **Foreign Keys**: Add businessId foreign key to all business-specific tables
5. **Query Validation**: Ensure all database queries include business ID conditions
6. **Access Control**: Verify user has access to the business before processing requests
7. **Settings Access**: Use `Business::config()` method for business-specific settings

### Email System Development

1. **EmailClient vs EmailService**: Understand the separation of concerns between these classes
2. **Instance Creation**: Create a new `EmailService` instance for email operations instead of using a singleton
3. **Template Structure**: Follow the existing template structure for new email templates
4. **Error Handling**: Always wrap email sending in try-catch blocks and log errors
5. **Specialized Methods**: For new types of emails, add specialized methods to EmailService

### PDF Template Development

1. **Template Structure**: Follow the existing template structure for new templates
2. **Dynamic Content**: Use PHP variables for dynamic content
3. **Responsive Design**: Ensure templates work with different paper sizes and orientations
4. **Conditional Sections**: Implement conditional sections based on document data
5. **Testing**: Test templates with various data scenarios
6. **GST Template Variants**: For invoice and proforma_invoice, implement changes in both `pdfView.php` and `pdfView-gst.php` files
7. **Template Selection Logic**: Understand that template selection is based on `isGstApplicable` condition on the business
8. **Signature Block Implementation**: Use `Key::isDisplaySignatureBlock` condition consistently across all document types
9. **Settings Integration**: Ensure all display settings (signature block, bank details, UPI details) are properly integrated

### API Development

1. **Base Controller**: **ALWAYS** extend `BaseApiController` for consistent response formatting
2. **Authentication**: Use `_checkAuth()` method for token validation and business context
3. **RESTful Design**: Follow RESTful principles for endpoint design
4. **Response Format**: Use `_sendResponse()` and `_sendErrorResponse()` methods
5. **Error Handling**: Implement proper error handling with consistent error codes
6. **Input Validation**: Validate all user inputs using model validation rules
7. **Business Scoping**: Ensure all data operations are scoped to the authenticated user's business
8. **Rate Limiting**: Implement rate limiting for sensitive endpoints
9. **CORS Headers**: Configure appropriate CORS headers for cross-origin requests

### Performance and Quality Guidelines

1. **Database Optimization**:
   - Use eager loading to avoid N+1 query problems
   - Implement proper database indexes on businessId, isDeleted, and frequently queried columns
   - Use query caching for complex queries
   - Implement pagination for large result sets

2. **Code Quality**:
   - Follow DRY (Don't Repeat Yourself) principles
   - Implement proper error handling with try-catch blocks
   - Use type hints for parameters and return types
   - Add comprehensive PHPDoc comments
   - Avoid long methods and complex nested logic

3. **Security Practices**:
   - Use parameterized queries to prevent SQL injection
   - Validate and sanitize all user inputs
   - Implement proper file upload validation
   - Use HTTPS for all API communications
   - Implement proper session management

## Recent Changes and Considerations

### Email System Split

The email system has been recently refactored to split EmailService into:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient

When working with the email system:

- Replace `EmailService::getInstance()` with `new EmailService(true)`
- Use `EmailClient::getInstance()->getSmtpMailer()` for SMTP operations
- Use `self::$emailClient->getLocalizedDate()` for date formatting in EmailService
- Use `EmailClient::getInstance()->sendUrgentNotificationToAdmin()` for urgent admin notifications

This split improves maintainability and provides clearer separation of concerns.

### PHP 8.3 Compatibility

**Recent Fix Applied**: Fixed PHP 8.3 deprecation warnings where `round()` function was called with null values. All `round()` calls now use null coalescing operator (`??`) to provide default values.

**Example Fix**:
```php
// Before (causes deprecation warning)
$cellValue = round($invoice['totalTaxAmount'], 4);

// After (PHP 8.3 compatible)
$cellValue = round($invoice['totalTaxAmount'] ?? 0, 4);
```

**Dependency Management**:
- Several packages use `dev-master` (unstable) - consider updating to stable versions
- Regular security updates needed for packages like Stripe, Firebase, and Yii2 components
- PHP version requirement: `>=8.1` with recommendation for PHP 8.3 compatibility

## Best Practices for AI Agents

### Information Gathering

1. **Read Documentation First**: **ALWAYS** start by reading relevant documentation in the `docs/` directory
2. **Understand Multi-Tenant Context**: Understand the business context and multi-tenant implications
3. **Security Awareness**: Be aware of the critical security issues identified in this project
4. **Examine Related Files**: Look at related files to understand dependencies and interactions
5. **Check Recent Changes**: Be aware of recent changes, especially PHP 8.3 compatibility fixes
6. **Use Codebase Retrieval**: Use the codebase retrieval tool to understand existing patterns before making changes

### Planning and Implementation

1. **Create Detailed Plans**: Always create a detailed, step-by-step plan before making changes
2. **Conservative Approach**: Be conservative with changes, especially to core components
3. **Follow Patterns**: Follow existing patterns and conventions in the codebase
4. **Incremental Changes**: Make small, incremental changes rather than large rewrites
5. **Test Thoroughly**: Test changes thoroughly before considering them complete

### Code Quality

1. **Maintain Consistency**: Keep code style consistent with the existing codebase
2. **Add Documentation**: Document your changes with clear comments and PHPDoc blocks
3. **Error Handling**: Implement proper error handling with try-catch blocks and logging
4. **Performance Considerations**: Consider performance implications, especially for database queries
5. **Security First**: **ALWAYS** consider security implications, especially for API endpoints
6. **Input Validation**: Validate all user inputs using model validation rules
7. **Type Safety**: Use PHP type hints and null coalescing operators for PHP 8.3 compatibility
8. **Testing**: Suggest writing or updating tests for any code changes

### Multi-Tenant Awareness

1. **Business Isolation**: **CRITICAL** - Always consider the multi-tenant nature of the application
2. **Query Scoping**: **MANDATORY** - Ensure ALL queries are properly scoped by businessId
3. **Access Control**: Implement proper access control checks in controllers using `_checkAuth()`
4. **Settings Management**: Use `Business::config()` method for business-specific settings
5. **Data Leakage Prevention**: Never allow data from one business to be visible to another
6. **User Context**: Always verify user has access to the business before processing requests

### Communication

1. **Clear Explanations**: Provide clear explanations of your changes and reasoning
2. **Documentation Updates**: Suggest updates to documentation when necessary
3. **Alternative Approaches**: Present alternative approaches when relevant
4. **Limitations**: Be upfront about limitations or potential issues with your changes

### Environment and Configuration

1. **APP_ID Configuration**: Ensure proper APP_ID is set in `main-local.php`
2. **Environment Variables**: Use environment variables for sensitive configuration
3. **Database Configuration**: Verify database settings in environment-specific config files
4. **SMTP Configuration**: Check SMTP provider settings for email functionality
5. **File Permissions**: Ensure proper write permissions for uploads and PDF generation

## Troubleshooting Common Issues

### Multi-Tenant Issues

- **Data Leakage**: If data from other businesses is visible, check for missing businessId conditions in queries
- **Access Control**: Verify proper access control checks in controllers using `_checkAuth()`
- **Business Context**: Ensure `$this->business` is properly set in controllers
- **User Permissions**: Verify user has proper permissions for the business
- **Settings Access**: Check if business-specific settings are being accessed correctly

### Email System Issues

- **SMTP Configuration**: Check SMTP provider configuration in params
- **Template Variables**: Verify all required variables are passed to templates
- **Error Handling**: Check logs for email sending errors
- **Rate Limiting**: Monitor email sending limits and counters

### PDF Generation Issues

- **Template Paths**: Verify template paths and file existence in templates directory
- **Dynamic Data**: Ensure all required data is passed to templates
- **mPDF Configuration**: Check mPDF configuration for errors and memory limits
- **File Permissions**: Verify write permissions for PDF storage directories
- **Template Selection**: Verify correct template is selected based on business settings
- **Asset Publishing**: Ensure template assets are properly published and accessible
- **GST Template Issues**: For invoice and proforma_invoice, ensure both `pdfView.php` and `pdfView-gst.php` are updated
- **Signature Block Display**: Check `Key::isDisplaySignatureBlock` setting if signature blocks are not appearing
- **Business Settings**: Verify business-specific display settings are properly configured

### Performance Issues

- **N+1 Queries**: Use eager loading with `with()` method to avoid N+1 query problems
- **Database Indexes**: Ensure proper indexes on businessId, isDeleted, and frequently queried columns
- **Query Optimization**: Use `explain` to analyze slow queries
- **Caching**: Implement query caching for frequently accessed data
- **Memory Usage**: Monitor memory usage for large data operations

### Security Issues

- **Authentication**: Verify AUTH_TOKEN header is properly validated
- **Input Validation**: Ensure all user inputs are validated and sanitized
- **SQL Injection**: Use parameterized queries instead of string concatenation
- **File Uploads**: Validate file types, sizes, and scan for malicious content
- **Rate Limiting**: Implement rate limiting for sensitive endpoints

## Conclusion

Following these guidelines will help ensure consistent, high-quality, and **secure** contributions to the Quotation Pro project.

### Key Priorities:
1. **Security First**: Address critical security vulnerabilities before implementing new features
2. **Multi-Tenant Awareness**: Always consider data isolation and business context
3. **Understanding Before Action**: Thoroughly understand existing code and architecture before making changes
4. **Conservative Approach**: Maintain a conservative approach to modifications, especially to core components
5. **Documentation**: Keep documentation updated with any significant changes
6. **Testing**: Always suggest testing for any code changes

### Remember:
- This is a **production application** serving real businesses
- **Data security and isolation** are paramount
- **Performance and reliability** are critical for user experience
- **Code quality** affects long-term maintainability

## Template System Architecture

### Template File Structure

Each document type follows a consistent structure:

```
templates/
├── {document_type}/
│   ├── default/
│   │   ├── pdfView.php         # Standard template
│   │   ├── pdfView-gst.php     # GST variant (for invoice & proforma_invoice)
│   │   ├── assets/
│   │   │   ├── pdf-style.css   # Template-specific CSS
│   │   │   └── images/         # Template images
│   │   └── fonts/              # Custom fonts
│   ├── modern/                 # Alternative template variant
│   └── layout/
│       └── pdf-layout.php      # Shared layout
```

### Template Selection Logic

- **Standard Documents**: Use `pdfView.php` template
- **GST Documents** (invoice & proforma_invoice):
  - Use `pdfView-gst.php` when `business->isGstApplicable` is true
  - Use `pdfView.php` when `business->isGstApplicable` is false
- **Template Variants**: Selected based on business template preferences

### Signature Block Implementation

All document templates should implement signature block with conditional display:

```php
// Variable definition in settings section
$isDisplaySignatureBlock = $settings[Key::isDisplaySignatureBlock] ?? 1;

// Conditional display in template
<?php if ($isDisplaySignatureBlock): ?>
<td width="" colspan="<?= $colsWithPayment ?>" style="text-align: right;">
    <div id="signBlock">
        <p class="company-name">For, <?= strtoupper($business->name) ?></p>
        <div class="signatureSpace" style="height: 60px; text-align:right;">
            <?php if (!empty($business->signatureImg)) : ?>
                <img src="<?= $business->signatureImg ?>" class="signImage" style="height: 60px; text-align: right;padding:20px" />&nbsp;
            <?php else : ?>
                <br /><br /><br /><br /><br />
            <?php endif; ?>
        </div>
        <div class="signature" style=" text-align: right;margin-bottom: 10px;"><?= Yii::t('template-default', 'AUTHORIZED SIGNATURE') ?></div>
    </div>
</td>
<?php endif; ?>
```

## Migration

Always follows the Yii2 migration naming convention with the format mYYMMDD_HHMMSS_description,
 for e.g. : m250422_205701_create_queue_table
 where:
m is the prefix for migrations
250422 represents the today's date (April 22, 2025)
205701 represents the current time (20:57:01)
create_queue_table is the description of what the migration does
This change ensures that the migration file has a unique name that follows the standard naming convention.