# Email Marketing System for Quotation Pro App

> **Note:** This document provides a comprehensive overview of the email marketing system implemented in the Quotation Pro App. For a complete understanding of the email system, please also refer to the main [email_system_documentation.md](email_system_documentation.md) file, which contains the most up-to-date information about the entire email system architecture.

## Table of Contents

1. [System Overview](#system-overview)
2. [Core Components](#core-components)
3. [Email Types and Triggers](#email-types-and-triggers)
4. [Technical Implementation](#technical-implementation)
5. [Email Design and Templates](#email-design-and-templates)
6. [SMTP Configuration and Load Balancing](#smtp-configuration-and-load-balancing)
7. [Best Practices](#best-practices)
8. [Future Enhancements](#future-enhancements)

## System Overview

The email marketing system in the Quotation Pro App is designed to send targeted, event-driven emails to users based on various subscription and usage events. The system follows a centralized approach where all emails are sent through a singleton EmailService class, ensuring consistent formatting, error handling, and delivery tracking.

Key features of the email system include:

- **Event-driven emails**: Automatically triggered based on user actions and system events
- **Personalized content**: Emails include user-specific information and relevant details
- **Responsive design**: All email templates are designed to work across devices
- **SMTP load balancing**: Support for multiple SMTP providers with automatic failover
- **Usage tracking**: Email sending limits are tracked and managed via cache
- **WhatsApp integration**: Support links with user ID as support pin
- **Consistent branding**: All emails follow a consistent design pattern

## Core Components

### 1. EmailService (`common\services\EmailService`)

The EmailService is the central component responsible for handling all email functionality in the application. It is implemented as a singleton to ensure consistent email handling across the application.

Key features:
- **Singleton pattern**: Ensures a single instance is used throughout the application
- **SMTP load balancing**: Manages multiple SMTP configurations with automatic rotation
- **Centralized error handling**: Consistent logging and error management
- **Template rendering**: Handles view composition and variable substitution
- **Email tracking**: Maintains counters for sent emails

### 2. LimitChecker (`common\components\LimitChecker`)

The LimitChecker component is responsible for monitoring usage limits and triggering appropriate email notifications when limits are reached.

Key features:
- **Usage monitoring**: Tracks document creation counts against limits
- **Threshold detection**: Identifies when users reach free usage limits
- **Email triggering**: Initiates limit-reached emails via EmailService
- **Pro user detection**: Skips limit checks for premium subscribers

### 3. Email Templates

Email templates are stored in the `common\mail\subscription\` directory and use PHP view files with HTML markup. Each template follows a consistent structure while providing content specific to the email type.

Key features:
- **Responsive design**: Templates work across desktop and mobile devices
- **Consistent branding**: All templates follow the same design pattern
- **Dynamic content**: Templates include placeholders for personalized information
- **WhatsApp integration**: Support links with user ID as support pin
- **Call-to-action buttons**: Clear buttons for user actions

## Email Types and Triggers

### 1. Subscription Failed Email
- **Template**: `payment-failed-html.php`
- **Trigger**: When a payment attempt fails
- **Method**: `EmailService::sendPaymentFailedMails()`
- **Purpose**: Inform users about payment failure and provide steps to resolve
- **Key Content**:
  - Error code and message
  - Plan details
  - Support contact information
  - "Try Again" call-to-action

### 2. Payment Success Email
- **Template**: `payment-success-html.php`
- **Trigger**: When a subscription payment is successful
- **Method**: `EmailService::sendPaymentSuccessEmail()`
- **Purpose**: Confirm payment, request app ratings/reviews
- **Key Content**:
  - Subscription details (plan, amount, dates)
  - Plan features
  - Rating request with app store links
  - Support contact information

### 3. Subscription Removed Email
- **Template**: `subscription-removed-html.php`
- **Trigger**: When a subscription is cancelled or expires
- **Method**: `EmailService::sendSubscriptionRemovedEmail()`
- **Purpose**: Encourage subscription renewal with plan options
- **Key Content**:
  - Subscription end details
  - Benefits of renewing
  - Recommended plans
  - "Renew Now" call-to-action

### 4. Limit Reached Email
- **Template**: `limit-reached-html.php`
- **Trigger**: When a user reaches the maximum usage limit
- **Method**: `EmailService::sendFinalLimitReachedEmail()`
- **Purpose**: Prompt users to subscribe to continue using features
- **Key Content**:
  - Limit type reached (quotation, invoice, etc.)
  - Recommended subscription plans
  - Benefits of upgrading
  - "Upgrade Now" call-to-action

### 5. Subscription Receipt Email
- **Template**: `subscription-receipt-html.php`
- **Trigger**: When a user subscribes via any payment provider
- **Method**: `EmailService::sendSubscriptionReceiptEmail()`
- **Purpose**: Provide subscription details and receipt
- **Key Content**:
  - Receipt number and date
  - Subscription details (plan, amount, dates)
  - Payment method and transaction ID
  - Plan features

## Technical Implementation

### EmailService Implementation

The EmailService is implemented as a singleton class that manages all email sending operations:

```php
class EmailService extends Component
{
    const TIMEZONE = 'Asia/Kolkata';
    const DATE_FORMAT = 'd.m.Y';
    const EMAIL_LIMIT_PER_ACCOUNT = 300;

    private static $instance = null;
    private $smtpMailers = [];
    public $mailer;

    private function __construct()
    {
        parent::__construct();
        $this->initializeSmtpMailers();
        $this->mailer = Yii::$app->mailer;
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // Other methods...
}
```

### SMTP Mailer Rotation

The EmailService implements a rotation mechanism for SMTP mailers to handle daily sending limits:

```php
private function getNextSmtpMailer(): MailerInterface
{
    $sentEmailsCounter = Yii::$app->cache->get('sentEmailsCounter');
    if (!$sentEmailsCounter) {
        $sentEmailsCounter = 0;
    }
    $mailConfigIndex = (int)($sentEmailsCounter / self::EMAIL_LIMIT_PER_ACCOUNT);

    if ($mailConfigIndex >= count($this->smtpMailers)) {
        throw new \Exception("All SMTP services are exhausted!");
    }

    // Increment counter with 24-hour expiration
    Yii::$app->cache->set('sentEmailsCounter', $sentEmailsCounter + 1, 86400);

    return $this->smtpMailers[$mailConfigIndex];
}
```

### Email Sending Methods

The EmailService provides specialized methods for different types of emails:

```php
public function sendUserMail(
    User   $user,
    string $template,
    array  $params,
    string $subjectPrefix
): bool
{
    try {
        $smtpMailer = $this->getNextSmtpMailer();
    } catch (\Exception $e) {
        Yii::error("Failed to get SMTP mailer: " . $e->getMessage());
        return false;
    }
    return $this->sendMail(
        $smtpMailer,
        $template,
        $params,
        $user->email,
        $subjectPrefix
    );
}

// Other specialized methods for different email types...
```

### LimitChecker Implementation

The LimitChecker component monitors usage limits and triggers emails when limits are reached:

```php
public function checkFinalLimit(User $user, $limitType, $currentCount)
{
    $forceSubscriptionCount = Yii::$app->params['FORCE_SUBSCRIPTION_COUNT'] ?? 40;

    // If user is already a pro user, they haven't reached the limit
    if ($user->isProUser) {
        return false;
    }

    // Check if user has reached the force subscription count
    if ($currentCount >= $forceSubscriptionCount) {
        // Send email notification about reaching the limit
        $this->sendLimitReachedEmail($user, $limitType);
        return true;
    }

    return false;
}
```

## Email Design and Templates

All email templates follow a consistent design pattern with the following elements:

1. **Header** with date and business logo
2. **Personalized greeting** with user's name
3. **Main message** with clear purpose and context
4. **Relevant details** specific to the email type
5. **Call-to-action buttons** for easy user response
6. **Support information** including WhatsApp link with user ID as support pin
7. **Footer** with standard information

### WhatsApp Support Link

All email templates include a WhatsApp support link with the user's ID as a support pin:

```php
$whatsAppLink = "https://wa.me/" . Yii::$app->params[Key::CONTACT_NUMBER] . "?text=" . urlencode("Hello Support Team, My Support PIN is *" . $user->id . "*");
```

### Responsive Design

All email templates use responsive design principles to ensure they display correctly on all devices:

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<style type="text/css">
    @media only screen and (max-width: 600px) {
        .container {
            width: 100% !important;
        }
        .content {
            padding: 15px !important;
        }
        .button {
            width: 100% !important;
            display: block !important;
            margin: 10px 0 !important;
        }
    }
</style>
```

## SMTP Configuration and Load Balancing

### Multiple SMTP Configurations

The application supports multiple SMTP configurations defined in `common/config/params.php`:

```php
'smtpMailers' => [
    [
        'class' => \yii\symfonymailer\Mailer::class,
        'transport' => [
            'dsn' => 'smtp://<EMAIL>:<EMAIL>:587',
        ],
        'viewPath' => '@common/mail',
        'useFileTransport' => false,
    ],
    [
        'class' => \yii\symfonymailer\Mailer::class,
        'transport' => [
            'dsn' => 'smtp://<EMAIL>:<EMAIL>:587',
        ],
        'viewPath' => '@common/mail',
        'useFileTransport' => false,
    ],
    // Additional configurations...
],
```

### Email Counter Management

The system maintains a counter in the cache to track the number of emails sent within a 24-hour period:

```php
// Increment counter with 24-hour expiration
Yii::$app->cache->set('sentEmailsCounter', $sentEmailsCounter + 1, 86400); // 86400 seconds = 24 hours
```

### SMTP Rotation Logic

The system automatically rotates between SMTP configurations based on the number of emails sent:

```php
$mailConfigIndex = (int)($sentEmailsCounter / self::EMAIL_LIMIT_PER_ACCOUNT);
```

## Best Practices

The email marketing system follows these best practices:

1. **Personalization**: All emails include the user's name and relevant subscription details
2. **Clear CTAs**: Each email has clear call-to-action buttons
3. **Responsive Design**: Emails are designed to work on all devices
4. **Consistent Branding**: All emails maintain consistent branding and styling
5. **Value Proposition**: Emails clearly communicate the value of subscription plans
6. **Error Handling**: Comprehensive error logging and fallback mechanisms
7. **Load Balancing**: Distribution of email load across multiple SMTP providers
8. **Centralized Management**: All emails are sent through the EmailService
9. **Event-Driven**: Emails are triggered by specific events rather than batch sending
10. **Support Access**: Easy access to support via WhatsApp with user ID as support pin

## Future Enhancements

Potential enhancements to the email marketing system include:

1. **AI-Generated Content**: Integrate AI to generate personalized email content based on user behavior and preferences
2. **A/B Testing**: Implement A/B testing for email subject lines and content
3. **Advanced Analytics**: Track email open rates, click-through rates, and conversion rates
4. **Segmentation**: Implement user segmentation for more targeted email campaigns
5. **Drip Campaigns**: Create automated email sequences for user onboarding and engagement
6. **Localization**: Support for multiple languages based on user preferences
7. **Predictive Sending**: Use AI to determine the optimal time to send emails to each user
8. **Interactive Emails**: Implement AMP for Email to create interactive email experiences
9. **Feedback Collection**: Include feedback forms in emails to gather user input
10. **Dynamic Content Blocks**: Implement dynamic content blocks that change based on user attributes
