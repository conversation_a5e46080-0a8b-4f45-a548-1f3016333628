# AI Agent Prompt: API Configuration for Quotation Pro App

## Context

You are assisting with the configuration of the Quotation Pro App API, a multi-tenant business application for creating and managing quotations, invoices, and other business documents. The application uses a sophisticated multi-environment configuration system that allows for easy switching between different environments and application variants.

## Configuration Architecture

The Quotation Pro App uses a layered configuration system:

1. **Base Configuration**: Core settings in `common/config/main.php` and `common/config/params.php`
2. **Environment Configuration**: Environment-specific settings in `common/config/apps-settings/{APP_ID}/`
3. **Application Settings**: Dynamic settings stored in the database with defaults in `app-settings-params.php`
4. **Local Overrides**: Developer-specific settings in `*-local.php` files (not in version control)

## Environment System

The application uses the `APP_ID` parameter to determine which environment configuration to load:

```php
// In common/config/main-local.php
define('APP_ID', 'quotation-dev');
```

Available environments include:
- `quotation-local`: Local development
- `quotation-dev`: Development server
- `quotation-prod`: Production
- `quotation-premium`: Premium version
- `quotation-custom-dev`: Custom development
- `estimate-prod`: Estimate Maker variant

## Key Configuration Files

### Common Configuration

- **`common/config/main.php`**: Core application components
  ```php
  return [
      'components' => [
          'mailer' => [
              'class' => \yii\symfonymailer\Mailer::class,
              'transport' => [
                  'dsn' => 'native://default',
              ],
              'viewPath' => '@common/mail',
              'useFileTransport' => false,
          ],
          // Other components
      ],
  ];
  ```

- **`common/config/params.php`**: Global parameters
  ```php
  return array_merge($appSettings, [
      'adminEmail' => ['<EMAIL>'],
      'supportEmail' => '<EMAIL>',
      'region' => 'IN',
      // Other parameters
  ]);
  ```

### Environment-Specific Configuration

- **`common/config/apps-settings/{APP_ID}/main.php`**: Environment components
  ```php
  return [
      'components' => [
          'db' => [
              'class' => 'yii\db\Connection',
              'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_quotation-dev',
              'username' => 'praxinfo_quotation-dev',
              'password' => 'dev@quotation9',
              'charset' => 'utf8mb4',
          ],
          // Other components
      ],
  ];
  ```

- **`common/config/apps-settings/{APP_ID}/params.php`**: Environment parameters
  ```php
  return array_merge($appSettings, [
      'adminEmail' => ['<EMAIL>'],
      'supportEmail' => '<EMAIL>',
      // Other parameters
  ]);
  ```

### Application Settings

- **`common/config/app-settings-params.php`**: Default application settings
  ```php
  return [
      Key::CONTACT_NUMBER => "+919510962986",
      Key::FREE_USAGE_LIMIT => "10",
      // Other settings
  ];
  ```

- **`common/config/apps-settings/{APP_ID}/app-settings-params.php`**: Environment-specific settings
  ```php
  return [
      Key::FREE_USAGE_LIMIT => "20",
      // Other settings
  ];
  ```

## Multi-Tenant Configuration

The application supports custom domains for different businesses through `business-domains.php`:

```php
return [
    'custom.quotationmaker.app' => 'praxinfo',
    'premium.quotationmaker.app' => 'praxinfo',
    // Other domain mappings
];
```

Business-specific settings are stored in the `business_settings` table and accessed through:

```php
$taxSettings = $business->config(Key::GROUP_QUOTATION, Key::TAX_SETTINGS);
```

## Task Types

As an AI assistant, you may be asked to help with:

1. **Environment Configuration**
   - Set up new environments
   - Modify existing environment settings
   - Troubleshoot environment-specific issues

2. **Application Settings**
   - Add new application settings
   - Update existing settings
   - Synchronize settings with the database

3. **API Configuration**
   - Configure API components
   - Set up API parameters
   - Optimize API performance

4. **Multi-Tenant Configuration**
   - Configure business domains
   - Set up business-specific settings
   - Troubleshoot multi-tenant issues

## Guidelines

When assisting with API configuration:

1. **Environment Awareness**
   - Always consider which environment the changes apply to
   - Use the appropriate configuration files for the target environment
   - Be aware of the configuration loading order

2. **Security Best Practices**
   - Never store sensitive information in version-controlled files
   - Use environment variables or `*-local.php` files for sensitive data
   - Ensure proper access controls for configuration files

3. **Performance Considerations**
   - Use caching for frequently accessed settings
   - Consider lazy loading for components that are not always needed
   - Optimize database queries for configuration retrieval

4. **Documentation**
   - Document all configuration changes
   - Explain the purpose and impact of each change
   - Provide examples of how to use new or modified settings

## Example Tasks

### Example 1: Add a New Application Setting

```
Task: Add a new setting for maximum file upload size.

Approach:
1. Define the setting key in common/models/enum/Key.php:
   const MAX_FILE_UPLOAD_SIZE = 'max_file_upload_size';

2. Add the default value in common/config/app-settings-params.php:
   Key::MAX_FILE_UPLOAD_SIZE => '10485760', // 10MB in bytes

3. Add the setting to AppSettings::getDefaults():
   ['int', Key::MAX_FILE_UPLOAD_SIZE, env(Key::MAX_FILE_UPLOAD_SIZE), "Maximum file upload size in bytes"],

4. Synchronize the settings with the database:
   ./yii batch-update/sync-app-settings

5. Use the setting in the API:
   $maxFileSize = getParam(Key::MAX_FILE_UPLOAD_SIZE);
```

### Example 2: Configure a New Environment

```
Task: Set up a new staging environment.

Approach:
1. Create a new directory:
   mkdir common/config/apps-settings/quotation-staging

2. Create main.php with database configuration:
   return [
       'components' => [
           'db' => [
               'class' => 'yii\db\Connection',
               'dsn' => 'mysql:host=staging-db;dbname=quotation_staging',
               'username' => 'staging_user',
               'password' => 'staging_password',
               'charset' => 'utf8mb4',
           ],
           // Other components
       ],
   ];

3. Create params.php with environment-specific parameters:
   return array_merge($appSettings, [
       'adminEmail' => ['<EMAIL>'],
       'supportEmail' => '<EMAIL>',
       // Other parameters
   ]);

4. Create app-settings-params.php with environment-specific settings:
   return [
       Key::FREE_USAGE_LIMIT => "15",
       // Other settings
   ];

5. Update main-local.php to use the new environment:
   define('APP_ID', 'quotation-staging');
```

### Example 3: Optimize API Configuration

```
Task: Optimize API response caching and formatting.

Approach:
1. Update api/config/main.php to configure HTTP caching and JSON formatting:
   'components' => [
       'response' => [
           'class' => 'yii\web\Response',
           'on beforeSend' => function ($event) {
               $response = $event->sender;
               if ($response->data !== null && $response->format == 'json') {
                   $response->headers->set('Cache-Control', 'public, max-age=3600');
               }
           },
           'formatters' => [
               \yii\web\Response::FORMAT_JSON => [
                   'class' => 'yii\web\JsonResponseFormatter',
                   'prettyPrint' => YII_DEBUG,
                   'encodeOptions' => JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE,
               ],
           ],
       ],
       // Other components
   ],

2. Configure data caching for frequently accessed data:
   'components' => [
       'cache' => [
           'class' => 'yii\caching\FileCache',
           'cachePath' => '@runtime/cache',
       ],
       // Other components
   ],

3. Implement caching in API controllers:
   public function actionView($id)
   {
       $cacheKey = 'quotation_' . $id;
       $quotation = Yii::$app->cache->getOrSet($cacheKey, function() use ($id) {
           return Quotation::findOne($id);
       }, 3600); // Cache for 1 hour

       return $this->_sendResponse($quotation);
   }
```

## Reference

For more detailed information, refer to:
- `API_CONFIGURATION_GUIDE.md` - Comprehensive documentation of the configuration system
- `common/config/` - Core configuration files
- `common/config/apps-settings/` - Environment-specific configuration files
- `common/models/enum/Key.php` - Application setting keys
