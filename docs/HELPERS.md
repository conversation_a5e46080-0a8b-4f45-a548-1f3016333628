# Quotation Pro App - Helper Files Documentation

This document provides detailed information about the helper files used in the Quotation Pro App, focusing on their purpose, core functions, dependencies, and integration logic.

## Table of Contents

1. [AppleNotificationValidator.php](#applenotificationvalidatorphp)
2. [AppleReceipt.php](#applereceiptphp)
3. [BooleanHelper.php](#booleanhelperphp)
4. [CommonHelper.php](#commonhelperphp)
5. [Dropdown.php](#dropdownphp)
6. [FileManager.php](#filemanagerphp)
7. [functions.php](#functionsphp)
8. [GoogleBillingHelper.php](#googlebillinghelperphp)
9. [GoogleContactHelper.php](#googlecontacthelperphp)
10. [ItunesValidatorHelper.php](#itunesvalidatorhelperphp)
11. [RazorpayHelper.php](#razorpayhelperphp)
12. [StripeCurrencyHelper.php](#stripecurrencyhelperphp)
13. [StripeHelper.php](#stripehelperphp)
14. [theCsv.php](#thecsvphp)
15. [UrlHelper.php](#urlhelperphp)

---

## AppleNotificationValidator.php

### Purpose & Responsibility
Validates and decodes Apple's signed notifications for in-app purchases and subscriptions. This helper is crucial for verifying the authenticity of notifications received from Apple's App Store Server Notifications.

### Core Functions
- `validateNotification(string $signedPayload)`: Validates and decodes Apple's signed notification JWT payload, returning the decoded data if valid or false otherwise.
- `formatPEM(string $certData)`: Converts certificate data to PEM format.
- `convertPublicKeyToJWK($publicKey)`: Converts a public key to JWK format.
- `verifyCertificateChain(array $certChain)`: Verifies the certificate chain against Apple's root CA.

### Dependency Insights
- Depends on Firebase JWT library for JWT validation and decoding
- Requires access to Apple's root CA certificate (configured in environment variables)
- Used by AppleSubscriptionController for webhook handling

### Data Flow
- Receives a signed JWT payload from Apple's server notifications
- Extracts and validates the certificate chain
- Decodes the transaction information and renewal information
- Returns structured data containing payload, transaction info, and renewal info

### Integration Logic
- Requires `APPLE_ROOT_CA_PATH` environment variable pointing to Apple's root CA certificate
- Validates the certificate chain against Apple's root CA
- Used in webhook endpoints to process subscription events from Apple

---

## AppleReceipt.php

### Purpose & Responsibility
Parses and normalizes Apple receipt data from both old and new formats. Acts as a data container for Apple in-app purchase receipts.

### Core Functions
- Constructor that initializes receipt data from either old or new format
- Normalizes timestamps from milliseconds to seconds
- Provides structured access to receipt data fields

### Dependency Insights
- Used by ItunesValidatorHelper for processing Apple receipts
- Used in AppleSubscriptionController for handling subscription events

### Data Flow
- Receives raw receipt data from Apple's validation response
- Normalizes data format differences between old and new receipt formats
- Provides standardized access to receipt fields like productId, transactionId, purchaseDate, etc.

### Integration Logic
- Supports both old and new Apple receipt formats
- Converts timestamps from milliseconds to seconds for consistency
- Used as a data container for subscription processing

---

### Dependency Insights
- No external dependencies
- Used throughout the application for consistent boolean value representation

### Data Flow
- Converts numeric boolean values (0/1) to human-readable text
- Provides mapping arrays for dropdown options

### Integration Logic
- Used in views and controllers for displaying status values
- Provides consistent text representation of boolean values across the application

---

## CommonHelper.php

### Purpose & Responsibility
Provides common utility functions used across the application.

### Core Functions
- `isActiveItems()`: Returns an array mapping 0/1 to "Active"/"Inactive"
- `isAutomaticItems()`: Returns an array mapping 0/1 to "Manual"/"Automatic"

### Dependency Insights
- No external dependencies
- Used throughout the application for consistent value representation

### Data Flow
- Provides mapping arrays for dropdown options and display values

### Integration Logic
- Used in views and controllers for displaying status values
- Provides consistent text representation of common values across the application

---

## Dropdown.php

### Purpose & Responsibility
A trait that provides dropdown generation functionality for ActiveRecord models.

### Core Functions
- `dropdown($valueField = 'id', $displayField = 'name', $whereCondition = null)`: Generates an array suitable for dropdown lists with key-value pairs
- `selectDropdown($valueField = 'id', $displayField = 'name', $whereCondition = null)`: Generates an array suitable for select2 dropdown lists with id-text pairs

### Dependency Insights
- Depends on Yii's ActiveQuery and ArrayHelper
- Used by models that need to generate dropdown options

### Data Flow
- Takes model data and converts it to arrays suitable for dropdown lists
- Supports filtering with where conditions

### Integration Logic
- Used in models to provide dropdown options for forms
- Simplifies the creation of dropdown lists from model data

---

## FileManager.php

### Purpose & Responsibility
Manages file uploads, storage, and retrieval for various document types in the application (quotations, invoices, receipts, business logos, etc.).

### Core Functions
- `upload($model, $urlField, $fileField, $relativePath, $id = null, $prefix = null, $deleteTempFile = true)`: Uploads a file and updates the model
- `saveFile($file, $relativePath, $id = null, $prefix = null, $deleteTempFile = true)`: Saves a file to the specified path
- `getFilePath($relativePath = '')`: Gets the absolute file path for a relative path
- `removeFileFromURL($fileUrl)`: Removes a file based on its URL
- `getFilePathFromURL($fileUrl)`: Converts a file URL to a file path
- `removeFile($filePath)`: Removes a file from the filesystem
- `isDirEmpty($dir)`: Checks if a directory is empty
- Document-specific save methods:
  - `saveQuotationFile($quotation)`
  - `saveInvoiceFile($invoice)`
  - `saveReceiptFile($receipt)`
  - `saveBusinessFile($business)`
  - `saveBusinessHeaderFile($business)`
  - `saveBusinessFooterFile($business)`
  - `saveBusinessSignFile($business)`
  - `saveProductImportFile($productImport)`
  - `saveSupplierImage($supplier)`
  - `saveAttachmentFile($attachment)`
- `remove($model, $urlField)`: Removes a file associated with a model

### Dependency Insights
- Depends on Yii's FileHelper for directory operations
- Used by various models for file management (Quotation, Invoice, Receipt, Business, etc.)

### Data Flow
- Handles file uploads from web forms
- Manages file storage in organized directory structures
- Provides URL and path conversion utilities
- Handles file deletion and cleanup

### Integration Logic
- Uses predefined directory constants for different file types
- Organizes files by business ID, year, and month
- Generates unique filenames to prevent collisions
- Integrates with model afterSave methods for automatic file processing

---

## functions.php

### Purpose & Responsibility
Provides global utility functions used throughout the application.

### Core Functions
- Time and date formatting:
  - `timeAgo($timestamp)`: Converts a timestamp to a human-readable "time ago" format
  - `formatDate($strDate, $withTime)`: Formats a date string
  - `convertDateToTimestamp($dateString)`: Converts a date string to a timestamp
- String manipulation:
  - `clean($string)`: Removes special characters from a string
  - `slugify($text, $divider)`: Converts a string to a URL-friendly slug
  - `trimInside($subject)`: Removes all whitespace from a string
- Configuration access:
  - `env($name, $default)`: Gets an environment variable
  - `getParam($name, $default)`: Gets a parameter from Yii's params
  - `setParam($name, $value)`: Sets a parameter in Yii's params
- Currency and number formatting:
  - `formatAsCurrency($number, $currency, $precision)`: Formats a number as currency
  - `formatAsCurrencyByLocale($number, $locale, $precision)`: Formats a number as currency with locale
  - `amountInWords($amount)`: Converts an amount to words
  - `amountToWordsINR($amount)`: Converts an amount to Indian Rupees in words
- Debugging:
  - `logDataIntoFile($data, $title, $filePrefix)`: Logs data to a file
  - `logError($message, $data)`: Logs an error message with data
  - `logMail($subject, $data, $message, $to)`: Logs and emails data
  - `getLastErrors($n, $logFile)`: Gets the last n errors from a log file
- Data conversion:
  - `getValue($type, $value)`: Converts a value to a specified type
  - `toBool($value)`: Converts a value to boolean

### Dependency Insights
- Used throughout the application
- Depends on Yii's framework components for formatting and configuration

### Data Flow
- Provides utility functions for common operations
- Handles data formatting and conversion
- Provides debugging and logging utilities

### Integration Logic
- Globally available functions
- Used in controllers, models, and views for common operations
- Provides consistent formatting and conversion across the application

---

## GoogleBillingHelper.php

### Purpose & Responsibility
Manages Google Play billing integration for in-app purchases and subscriptions.

### Core Functions
- `sharedInstance()`: Returns a singleton instance of the helper
- `getAndroidPublisher()`: Gets a configured AndroidPublisher client
- `getValidator()`: Gets a configured validator for Google Play receipts
- `getAcknowledger($productId, $purchaseToken)`: Gets an acknowledger for Google Play purchases
- `setConsumable($productId, $purchaseToken)`: Consumes a one-time purchase
- `getNotificationType($type)`: Gets the notification type name from its code
- `acknowledgeSubscription($subscription)`: Acknowledges a subscription
- `debugPurchaseToken($productId, $purchaseToken, $isRecurring)`: Debugs a purchase token
- `processSubscription($productId, $purchaseToken, User $user = null)`: Processes a subscription purchase

### Dependency Insights
- Depends on Google API Client library
- Depends on ReceiptValidator library for Google Play validation
- Used by GoogleSubscriptionController for handling subscription events

### Data Flow
- Validates purchase tokens with Google Play API
- Processes subscription data and updates local subscription records
- Acknowledges purchases to prevent duplicate processing
- Handles different purchase types (one-time vs. subscription)

### Integration Logic
- Requires Google service account JSON file configured in environment variables
- Requires Android package name configured in environment variables
- Handles different notification types from Google Play
- Integrates with Subscription model for tracking subscriptions

---

## GoogleContactHelper.php

### Purpose & Responsibility
Facilitates adding contacts to Google Contacts via a Google Apps Script API.

### Core Functions
- `getGoogleContactStoreUrl($name, $phone)`: Generates a URL for storing a contact in Google Contacts
- `addContactToGoogle($name, $phone)`: Adds a contact to Google Contacts via API call

### Dependency Insights
- Depends on GuzzleHttp Client for API requests
- Used in email templates and API controllers

### Data Flow
- Takes name and phone number as input
- Makes a GET request to a Google Apps Script endpoint
- Returns the response from the Google Apps Script

### Integration Logic
- Requires `GOOGLE_CONTACT_STORE_API_URL` environment variable
- Used in email templates to generate contact links
- Used in ApiController for direct contact addition

---

## ItunesValidatorHelper.php

### Purpose & Responsibility
Validates and processes Apple App Store receipts for in-app purchases and subscriptions.

### Core Functions
- `sharedInstance()`: Returns a singleton instance of the helper
- `validateReceipt($receiptBase64Data)`: Validates a receipt with Apple's servers
- `validateAndProcessSubscription($receiptBase64Data, User $user = null)`: Validates and processes a subscription
- `processSubscription(AppleReceipt $latestReceipt, User $user = null)`: Processes a subscription from a receipt

### Dependency Insights
- Depends on ReceiptValidator library for iTunes validation
- Uses AppleReceipt for receipt data parsing
- Used by AppleSubscriptionController for handling subscription events

### Data Flow
- Validates receipt data with Apple's servers
- Parses receipt data into AppleReceipt objects
- Updates local subscription records based on receipt data
- Logs subscription events

### Integration Logic
- Supports both sandbox and production environments
- Requires iTunes purchase secret configured in environment variables
- Handles different receipt formats
- Integrates with Subscription model for tracking subscriptions

---

## RazorpayHelper.php

### Purpose & Responsibility
Manages Razorpay integration for payment processing and subscriptions.

### Core Functions
- `sharedInstance($account)`: Returns a singleton instance of the helper
- `sharedInstanceByRegion($region)`: Returns an instance based on region
- `setApiKey($account)`: Sets the API key for Razorpay
- `getClient()`: Gets the Razorpay API client
- `createCustomer(User $user)`: Creates a Razorpay customer
- `convertNormalPriceToUnits($price, $currency)`: Converts a price to Razorpay units
- `convertUnitPriceToNormal($price, $currency)`: Converts Razorpay units to normal price
- `createOrder(User $user, SubscriptionPlan $plan)`: Creates a Razorpay order
- `createSubscription(User $user, SubscriptionPlan $plan)`: Creates a Razorpay subscription
- `processOrder($subscriptionData, $user = null)`: Processes a Razorpay order
- `processSubscription($subscriptionData, $user = null)`: Processes a Razorpay subscription
- `cancelSubscription($subscriptionId, $paymentId)`: Cancels a Razorpay subscription
- `handleWebhook($request)`: Handles Razorpay webhook events
- `refund($rzpSubscription, $paymentId)`: Processes a refund
- `sendPaymentIntentEmail($user, $plan)`: Sends a payment intent email

### Dependency Insights
- Depends on Razorpay PHP SDK
- Uses StripeCurrencyHelper for currency conversion
- Used by payment controllers for handling Razorpay payments

### Data Flow
- Creates and manages Razorpay customers, orders, and subscriptions
- Processes webhook events from Razorpay
- Updates local subscription records based on Razorpay events
- Handles refunds and cancellations

### Integration Logic
- Supports multiple Razorpay accounts based on region
- Requires Razorpay API keys and webhook secrets configured in environment variables
- Handles different currency formats
- Integrates with Subscription model for tracking subscriptions

---

## StripeCurrencyHelper.php

### Purpose & Responsibility
Provides currency-related utilities for Stripe integration, including currency conversion and formatting.

### Core Functions
- `getSupportedCurrencies()`: Returns an array of supported currency codes
- `getZeroDecimalCurrencies()`: Returns an array of zero-decimal currencies
- `getCurrencyOptions($priceInINR)`: Generates currency options for Stripe with converted prices
- `getConversionData()`: Fetches currency conversion rates from an external API

### Dependency Insights
- Depends on GuzzleHttp Client for API requests
- Used by StripeHelper and RazorpayHelper for currency conversion

### Data Flow
- Provides currency code lists and conversion utilities
- Fetches real-time currency conversion rates
- Generates Stripe-compatible currency option objects

### Integration Logic
- Uses external API for currency conversion rates
- Handles zero-decimal currencies differently
- Provides consistent currency handling across payment providers

---

## StripeHelper.php

### Purpose & Responsibility
Manages Stripe integration for payment processing and subscriptions.

### Core Functions
- `sharedInstance($account)`: Returns a singleton instance of the helper
- `sharedInstanceByRegion($region)`: Returns an instance based on region
- `setApiKey($account)`: Sets the API key for Stripe
- `getCurrentAccount()`: Gets the current Stripe account
- `getCustomerPortalUrl(User $user)`: Gets a Stripe customer portal URL
- `createStripePrice($planId)`: Creates a Stripe price for a plan
- `convertNormalPriceToUnits($price, $currency)`: Converts a price to Stripe units
- `convertUnitPriceToNormal($price, $currency)`: Converts Stripe units to normal price
- `calculateProratedRefundAmount($subscriptionId)`: Calculates a prorated refund amount
- `cancelSubscription($subscriptionId)`: Cancels a Stripe subscription
- `setPaymentMethodBySubscriptionId($subscriptionId)`: Sets a payment method for a subscription
- `setDefaultPaymentMethod($stripeSubscription)`: Sets the default payment method
- `createCustomer(User $user)`: Creates a Stripe customer
- `getCustomerFromStripeSubscriptionId($stripeSubscriptionId)`: Gets a customer from a subscription ID
- `removeIncompleteTransactions()`: Removes incomplete transactions
- `removePendingIntents($userId)`: Removes pending payment intents
- `createPaymentIntent(User $user, SubscriptionPlan $plan)`: Creates a payment intent
- `createStripePaymentIntent(User $user, SubscriptionPlan $plan)`: Creates a Stripe payment intent
- `successPayment(string $checkoutSessionId, $userId)`: Processes a successful payment
- `processSubscription(StripeSubscription|PaymentIntent $stripeSubscription)`: Processes a subscription
- `handleWebhook($payload)`: Handles Stripe webhook events
- `createUpgradePaymentIntent(Subscription $activeSubscription, SubscriptionPlan $plan)`: Creates a payment intent for an upgrade
- `getStripeCustomerId(User $user)`: Gets a Stripe customer ID for a user
- `getUserByStripeCustomerId($stripeCustomerId)`: Gets a user by Stripe customer ID
- `stripeRefund($stripeSubscription, $subscriptionId)`: Processes a refund
- `storeSubscriptionPaymentIntent(StripeSubscription|PaymentIntent $stripeSubscription, User $user, SubscriptionPlan $plan)`: Stores a subscription payment intent

### Dependency Insights
- Depends on Stripe PHP SDK
- Uses StripeCurrencyHelper for currency conversion
- Used by payment controllers for handling Stripe payments

### Data Flow
- Creates and manages Stripe customers, subscriptions, and payment intents
- Processes webhook events from Stripe
- Updates local subscription records based on Stripe events
- Handles refunds and cancellations

### Integration Logic
- Supports multiple Stripe accounts based on region
- Requires Stripe API keys configured in environment variables
- Handles different currency formats
- Integrates with Subscription model for tracking subscriptions

---

## theCsv.php

### Purpose & Responsibility
Provides CSV export functionality for database tables and query results.

### Core Functions
- `export($parameter)`: Exports data to CSV format

### Dependency Insights
- Depends on Yii's Query and Inflector
- Used for exporting data to CSV

### Data Flow
- Takes database table name, query, or data array as input
- Generates CSV output with headers
- Writes to file or outputs to browser

### Integration Logic
- Supports exporting from database tables, SQL queries, or data arrays
- Handles UTF-8 encoding
- Supports custom headers and file naming

---

## UrlHelper.php

### Purpose & Responsibility
Provides URL generation utilities for different application components.

### Core Functions
- `frontendUrl($url)`: Generates a frontend URL
- `backendUrl($url)`: Generates a backend URL
- `uploadUrl($url)`: Generates an upload URL
- `billingUrl($url)`: Generates a billing URL

### Dependency Insights
- Depends on Yii's URL manager components
- Used throughout the application for URL generation

### Data Flow
- Takes a relative URL as input
- Returns an absolute URL for the specified application component

### Integration Logic
- Uses Yii's URL managers configured in the application
- Provides consistent URL generation across the application
- Supports different application components (frontend, backend, upload, billing)
