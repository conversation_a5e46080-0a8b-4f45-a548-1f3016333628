# AI Agent Prompt: Model Development for Quotation Pro App

## Context

You are assisting with the development and maintenance of the data models for the Quotation Pro App, a multi-tenant business application for creating and managing quotations, invoices, and other business documents. The application uses the Yii2 PHP framework and follows an ActiveRecord pattern for database interaction.

## Model Architecture

The Quotation Pro App follows a structured model architecture:

1. **Base Models** (`common/models/base/`)
   - Generated by Yii2's Gii tool
   - Represent direct database table mappings
   - Define basic attributes, rules, and relations
   - Should not be modified directly (extend instead)

2. **Business Models** (`common/models/`)
   - Extend base models with business logic
   - Implement behaviors like soft deletion
   - Define complex validation rules
   - Handle document number generation
   - Implement PDF generation logic

3. **API Models** (`api/modules/v1/models/`)
   - Handle API-specific logic like search and filtering
   - Implement data transformations for API responses
   - Handle pagination and sorting

## Key Models

### User Model

The User model represents application users and implements authentication:

```php
class User extends BaseUser implements IdentityInterface
{
    // Constants for user status
    const STATUS_DELETED = 0;
    const STATUS_INACTIVE = 9;
    const STATUS_ACTIVE = 10;
    
    // Authentication methods
    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(['accessToken' => $token, 'status' => self::STATUS_ACTIVE]);
    }
    
    // Password handling
    public function setPassword($password)
    {
        $this->passwordHash = Yii::$app->security->generatePasswordHash($password);
    }
    
    // Token generation
    public function generateAccessToken()
    {
        $this->accessToken = Yii::$app->security->generateRandomString(64);
    }
    
    // Business association
    public function getBusiness()
    {
        return $this->hasOne(Business::className(), ['id' => 'businessId'])
            ->viaTable('user_business', ['userId' => 'id']);
    }
}
```

### Business Model

The Business model is the core of the multi-tenant architecture:

```php
class Business extends BaseBusiness
{
    // Document number tracking
    public $quotationNumber = 1;
    public $invoiceNumber = 1;
    public $poNumber = 1;
    
    // Settings management
    public function settings($group)
    {
        return BusinessSettings::getSettings($this->id, $group);
    }
    
    public function config($group, $key)
    {
        return BusinessSettings::getSetting($this->id, $group, $key);
    }
    
    // User association
    public function getUsers()
    {
        return $this->hasMany(User::className(), ['id' => 'userId'])
            ->viaTable('user_business', ['businessId' => 'id']);
    }
}
```

### Quotation Model

The Quotation model handles quotation documents and related types:

```php
class Quotation extends BaseQuotation
{
    // Document type flags
    public $isQuotation = 1;
    public $isPurchaseOrder = 0;
    public $isBudget = 0;
    
    // Number generation
    public function generateQuotationNumber()
    {
        if ($this->id && empty($this->quotationNumber)) {
            $nextQuoteNumber = $this->business->quotationNumber;
            $quotationPrefix = $this->business->quotationPrefix;
            $this->quotationNumber = sprintf("%s%s", $quotationPrefix, $nextQuoteNumber);
            $this->updateAttributes(['quotationNumber']);
            $this->business->quotationNumber++; // increment new quotation number!
            $this->business->updateAttributes(['quotationNumber']);
            return true;
        }
        return false;
    }
    
    // PDF generation
    public function generatePDF()
    {
        // PDF generation logic
    }
}
```

### Invoice Model

The Invoice model handles invoice documents and related types:

```php
class Invoice extends BaseInvoice
{
    // Document type flags
    public $isInvoice = 1;
    public $isProformaInvoice = 0;
    public $isDeliveryNote = 0;
    
    // Number generation
    public function generateInvoiceNumber()
    {
        if ($this->id && empty($this->invoiceNumber)) {
            $nextInvoiceNumber = $this->business->invoiceNumber;
            $invoicePrefix = $this->business->invoicePrefix;
            $this->invoiceNumber = sprintf("%s%s", $invoicePrefix, $nextInvoiceNumber);
            $this->updateAttributes(['invoiceNumber']);
            $this->business->invoiceNumber++; // increment new invoice number!
            $this->business->updateAttributes(['invoiceNumber']);
            return true;
        }
        return false;
    }
}
```

### BusinessSettings Model

The BusinessSettings model manages business-specific settings:

```php
class BusinessSettings extends BaseBusinessSettings
{
    // Get all settings for a group
    public static function getSettings($businessId, $group)
    {
        $settings = self::find()
            ->where(['businessId' => $businessId, 'group' => $group])
            ->all();
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->key] = $setting->value;
        }
        
        return $result;
    }
    
    // Get a specific setting
    public static function getSetting($businessId, $group, $key)
    {
        $setting = self::find()
            ->where(['businessId' => $businessId, 'group' => $group, 'key' => $key])
            ->one();
        
        return $setting ? $setting->value : null;
    }
}
```

## Common Behaviors

### Soft Delete Behavior

Most models implement soft deletion:

```php
public function behaviors()
{
    return [
        'softDeleteBehavior' => [
            'class' => SoftDeleteBehavior::className(),
            'softDeleteAttributeValues' => [
                'isDeleted' => true
            ],
        ],
    ];
}

public function beforeSoftDelete()
{
    $this->deletedAt = new Expression('NOW()');
    return true;
}
```

### Timestamp Behavior

Models track creation and update times:

```php
public function behaviors()
{
    return [
        'timestamp' => [
            'class' => TimestampBehavior::className(),
            'createdAtAttribute' => 'createdAt',
            'updatedAtAttribute' => 'updatedAt',
            'value' => new Expression('NOW()'),
        ],
    ];
}
```

## Task Types

As an AI assistant, you may be asked to help with:

1. **Creating New Models**
   - Create base models for new database tables
   - Extend base models with business logic
   - Implement appropriate behaviors
   - Define validation rules and relations

2. **Enhancing Existing Models**
   - Add new methods or properties
   - Optimize database queries
   - Implement new business logic
   - Fix bugs in existing methods

3. **Model Relationships**
   - Define proper relations between models
   - Implement eager loading for performance
   - Handle cascading operations (e.g., deletion)

4. **Validation Rules**
   - Implement complex validation rules
   - Handle conditional validation
   - Create custom validators

## Guidelines

When assisting with model development:

1. **Follow Yii2 Conventions**
   - Use Yii2's ActiveRecord patterns
   - Follow naming conventions (e.g., `tableName()`, `rules()`, `attributeLabels()`)
   - Use proper relation methods (`hasOne()`, `hasMany()`, etc.)

2. **Maintain Multi-Tenant Isolation**
   - Always include `businessId` in queries
   - Use scopes to filter by business
   - Validate that users can only access their own business data

3. **Implement Soft Deletion**
   - Use SoftDeleteBehavior for all models that need deletion
   - Include `isDeleted` condition in queries
   - Handle cascading soft deletion for related records

4. **Document Number Generation**
   - Follow existing patterns for document number generation
   - Increment counters in the Business model
   - Use transactions to prevent race conditions

5. **Performance Optimization**
   - Use eager loading for related models
   - Create indexes for frequently queried columns
   - Optimize complex queries

## Example Tasks

### Example 1: Create a New Model

```
Task: Create a new Receipt model for tracking payment receipts.

Approach:
1. Create a migration for the receipts table
2. Generate the base model using Gii
3. Create the Receipt model extending the base model
4. Implement soft deletion behavior
5. Add receipt number generation logic
6. Define relations to Invoice and Business models
7. Implement PDF generation for receipts
```

### Example 2: Enhance an Existing Model

```
Task: Add support for recurring invoices to the Invoice model.

Approach:
1. Add new fields to the invoices table via migration
2. Update the base model to include the new fields
3. Add methods to handle recurring logic:
   - `createRecurringInstance()`
   - `getNextRecurringDate()`
   - `isRecurring()`
4. Implement validation rules for recurring fields
5. Create a console command to generate recurring invoices
```

### Example 3: Optimize Model Queries

```
Task: Optimize the query performance for listing quotations.

Approach:
1. Analyze the current query in QuotationListing model
2. Add appropriate indexes to the database
3. Implement eager loading for related models
4. Use join instead of subqueries where appropriate
5. Add caching for frequently accessed data
6. Test performance with different data volumes
```

## Reference

For more detailed information, refer to:
- `common/models/` - Core model implementations
- `common/models/base/` - Base model implementations
- `console/migrations/` - Database schema definitions
- Yii2 documentation on [Active Record](https://www.yiiframework.com/doc/guide/2.0/en/db-active-record)
