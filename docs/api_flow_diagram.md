# Quotation Pro API Flow Diagram

## API Authentication Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │UserController │     │  User Model   │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ POST /v1/user/login │               │     │               │
│ {email, password}   │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │  Login Request      │                     │
        │────────────────────►│                     │
        │                     │                     │
        │                     │  findByEmail()      │
        │                     │────────────────────►│
        │                     │                     │
        │                     │  validatePassword() │
        │                     │────────────────────►│
        │                     │                     │
        │                     │  generateAccessToken│
        │                     │────────────────────►│
        │                     │                     │
        │  Response with      │                     │
        │  AUTH_TOKEN         │                     │
        │◄────────────────────│                     │
        │                     │                     │
```

## API Request Authentication Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │BaseApiController    │  Identity     │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ GET /v1/quotation/list │             │     │               │
│ AUTH_TOKEN: xxx  │     │             │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │  API Request with   │                     │
        │  AUTH_TOKEN header  │                     │
        │────────────────────►│                     │
        │                     │                     │
        │                     │  _checkAuth()       │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │ findIdentityByAccessToken() │
        │                     │────────────────────►│
        │                     │                     │
        │                     │ Return User Identity│
        │                     │◄────────────────────│
        │                     │                     │
        │                     │ Set Yii::$app->user │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │ Process Request     │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │  JSON Response      │                     │
        │◄────────────────────│                     │
        │                     │                     │
```

## Document Creation Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │QuotationController   │Quotation Model│     │  PdfService   │
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ POST /v1/quotation/create │          │     │               │     │               │
│ {data}         │     │               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  Create Request     │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  _checkAuth()       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Create Quotation   │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │                     │  save()             │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │                     │  afterSave()        │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │                     │  generatePDF()      │
        │                     │                     │────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Generate PDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │                     │  Return PDF Data    │
        │                     │                     │◄────────────────────│
        │                     │                     │                     │
        │                     │  Return Quotation   │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │  JSON Response      │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## PDF Generation Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Document Model│     │  PdfService   │     │ Template View │     │  mPDF Library │
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ generatePDF() │     │               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  Call PdfService    │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  Get Template       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Publish Assets     │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Render Template    │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │                     │  Process Template   │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │  Return HTML        │                     │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │                     │  Configure mPDF     │                     │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Setup mPDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Generate PDF       │                     │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Convert HTML to PDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Return PDF Data    │                     │
        │                     │◄────────────────────────────────────────────│
        │                     │                     │                     │
        │                     │  Store PDF File     │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │  Return PDF Data    │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## Multi-Tenant Data Access Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │BaseApiController    │ Business Model│     │ Database Query│
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ GET /v1/quotation/list │             │     │               │     │               │
│ AUTH_TOKEN: xxx  │     │             │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  API Request        │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  _checkAuth()       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Get User Business  │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │  Return Business    │                     │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │                     │  Set $this->business│                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Query with businessId Filter             │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Execute Query
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Return Results     │                     │
        │                     │◄────────────────────────────────────────────│
        │                     │                     │                     │
        │  JSON Response      │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## Configuration System Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ API Request   │     │ Configuration │     │ Business      │
│ Processing    │     │ System        │     │ Settings      │
├───────────────┤     ├───────────────┤     ├───────────────┤
│               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │  Initialize App     │                     │
        │────────────────────►│                     │
        │                     │                     │
        │                     │  Load Base Config   │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │  Load APP_ID Config │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │  Load Local Config  │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │  Process Request    │                     │
        │◄────────────────────│                     │
        │                     │                     │
        │  Get Business Settings                    │
        │────────────────────────────────────────────►│
        │                     │                     │
        │                     │                     │  Load Settings
        │                     │                     │─────────┐
        │                     │                     │         │
        │                     │                     │◄────────┘
        │                     │                     │
        │  Return Settings    │                     │
        │◄────────────────────────────────────────────│
        │                     │                     │
        │  Apply Settings     │                     │
        │─────────┐           │                     │
        │         │           │                     │
        │◄────────┘           │                     │
        │                     │                     │
```

## API Request Authentication Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │BaseApiController    │  Identity     │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ GET /v1/quotation/list │             │     │               │
│ AUTH_TOKEN: xxx  │     │             │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │  API Request with   │                     │
        │  AUTH_TOKEN header  │                     │
        │────────────────────►│                     │
        │                     │                     │
        │                     │  _checkAuth()       │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │ findIdentityByAccessToken() │
        │                     │────────────────────►│
        │                     │                     │
        │                     │ Return User Identity│
        │                     │◄────────────────────│
        │                     │                     │
        │                     │ Set Yii::$app->user │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │ Process Request     │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │  JSON Response      │                     │
        │◄────────────────────│                     │
        │                     │                     │
```

## Document Creation Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │QuotationController   │Quotation Model│     │  PdfService   │
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ POST /v1/quotation/create │          │     │               │     │               │
│ {data}         │     │               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  Create Request     │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  _checkAuth()       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Create Quotation   │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │                     │  save()             │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │                     │  afterSave()        │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │                     │  generatePDF()      │
        │                     │                     │────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Generate PDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │                     │  Return PDF Data    │
        │                     │                     │◄────────────────────│
        │                     │                     │                     │
        │                     │  Return Quotation   │                     │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │  JSON Response      │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## PDF Generation Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Document Model│     │  PdfService   │     │ Template View │     │  mPDF Library │
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ generatePDF() │     │               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  Call PdfService    │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  Get Template       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Publish Assets     │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Render Template    │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │                     │  Process Template   │
        │                     │                     │─────────┐           │
        │                     │                     │         │           │
        │                     │                     │◄────────┘           │
        │                     │                     │                     │
        │                     │  Return HTML        │                     │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │                     │  Configure mPDF     │                     │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Setup mPDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Generate PDF       │                     │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Convert HTML to PDF
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Return PDF Data    │                     │
        │                     │◄────────────────────────────────────────────│
        │                     │                     │                     │
        │                     │  Store PDF File     │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │  Return PDF Data    │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## Multi-Tenant Data Access Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│  API Client   │     │BaseApiController    │ Business Model│     │ Database Query│
├───────────────┤     ├───────────────┤     ├───────────────┤     ├───────────────┤
│ GET /v1/quotation/list │             │     │               │     │               │
│ AUTH_TOKEN: xxx  │     │             │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │                     │
        │  API Request        │                     │                     │
        │────────────────────►│                     │                     │
        │                     │                     │                     │
        │                     │  _checkAuth()       │                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Get User Business  │                     │
        │                     │────────────────────►│                     │
        │                     │                     │                     │
        │                     │  Return Business    │                     │
        │                     │◄────────────────────│                     │
        │                     │                     │                     │
        │                     │  Set $this->business│                     │
        │                     │─────────┐           │                     │
        │                     │         │           │                     │
        │                     │◄────────┘           │                     │
        │                     │                     │                     │
        │                     │  Query with businessId Filter             │
        │                     │────────────────────────────────────────────►│
        │                     │                     │                     │
        │                     │                     │                     │  Execute Query
        │                     │                     │                     │─────────┐
        │                     │                     │                     │         │
        │                     │                     │                     │◄────────┘
        │                     │                     │                     │
        │                     │  Return Results     │                     │
        │                     │◄────────────────────────────────────────────│
        │                     │                     │                     │
        │  JSON Response      │                     │                     │
        │◄────────────────────│                     │                     │
        │                     │                     │                     │
```

## Configuration System Flow

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ API Request   │     │ Configuration │     │ Business      │
│ Processing    │     │ System        │     │ Settings      │
├───────────────┤     ├───────────────┤     ├───────────────┤
│               │     │               │     │               │
└───────┬───────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │  Initialize App     │                     │
        │────────────────────►│                     │
        │                     │                     │
        │                     │  Load Base Config   │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │  Load APP_ID Config │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │                     │  Load Local Config  │
        │                     │─────────┐           │
        │                     │         │           │
        │                     │◄────────┘           │
        │                     │                     │
        │  Process Request    │                     │
        │◄────────────────────│                     │
        │                     │                     │
        │  Get Business Settings                    │
        │────────────────────────────────────────────►│
        │                     │                     │
        │                     │                     │  Load Settings
        │                     │                     │─────────┐
        │                     │                     │         │
        │                     │                     │◄────────┘
        │                     │                     │
        │  Return Settings    │                     │
        │◄────────────────────────────────────────────│
        │                     │                     │
        │  Apply Settings     │                     │
        │─────────┐           │                     │
        │         │           │                     │
        │◄────────┘           │                     │
        │                     │                     │
```
