# IP-Based Rate Limiting and Automatic Blocking Guide

This document provides detailed instructions for implementing IP-based rate limiting and automatic blocking of suspicious IPs in the Quotation Pro application.

## Table of Contents

1. [Overview](#overview)
2. [Fail2Ban Implementation](#fail2ban-implementation)
3. [ModSecurity Implementation](#modsecurity-implementation)
4. [ModEvasive Implementation](#modevasive-implementation)
5. [Testing Your Configuration](#testing-your-configuration)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Overview

IP-based rate limiting helps protect your application from:
- Brute force attacks
- Vulnerability scanning
- Denial of Service (DoS) attacks
- Web scraping and data harvesting
- Automated bot attacks

The implementation described in this guide will:
1. Monitor requests from each IP address
2. Detect when an IP makes too many requests in a short time period
3. Detect when an IP requests many non-existent files (likely scanning)
4. Automatically block offending IPs for a configurable time period
5. Log all blocking events for review

## Fail2Ban Implementation

Fail2Ban is a powerful intrusion prevention tool that monitors log files and can ban IPs that show malicious behavior.

### Installation

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install fail2ban

# CentOS/RHEL
sudo yum install epel-release
sudo yum install fail2ban
sudo systemctl enable fail2ban
```

### Configuration

1. **Create a backup of the default configuration**:
   ```bash
   sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
   ```

2. **Edit the local configuration file**:
   ```bash
   sudo nano /etc/fail2ban/jail.local
   ```

3. **Configure the default section**:
   ```ini
   [DEFAULT]
   # Ban IP for 1 hour (3600 seconds)
   bantime = 3600
   
   # Check for 5 failures in 10 minutes (600 seconds)
   findtime = 600
   maxretry = 5
   
   # Email notifications (optional)
   destemail = <EMAIL>
   sendername = Fail2Ban
   mta = sendmail
   action = %(action_mwl)s
   ```

### Apache-Specific Configuration

1. **Create a custom filter for Apache 404 errors** in `/etc/fail2ban/filter.d/apache-404.conf`:
   ```ini
   [Definition]
   failregex = ^<HOST> - .* "(GET|POST|HEAD).*" 404 .*$
   ignoreregex =
   ```

2. **Create a custom filter for Apache rate limiting** in `/etc/fail2ban/filter.d/apache-ratelimit.conf`:
   ```ini
   [Definition]
   failregex = ^<HOST> - .* "(GET|POST|HEAD).*" 429 .*$
   ignoreregex =
   ```

3. **Add jail configurations** to `/etc/fail2ban/jail.local`:
   ```ini
   [apache-404]
   enabled = true
   port = http,https
   filter = apache-404
   logpath = /var/log/apache2/access.log
   maxretry = 20
   findtime = 120
   bantime = 3600
   
   [apache-ratelimit]
   enabled = true
   port = http,https
   filter = apache-ratelimit
   logpath = /var/log/apache2/access.log
   maxretry = 2
   findtime = 60
   bantime = 7200
   ```

4. **If using ModSecurity, add a ModSecurity-specific jail**:
   ```ini
   [apache-modsec]
   enabled = true
   port = http,https
   filter = apache-modsec
   logpath = /var/log/apache2/error.log
   maxretry = 3
   findtime = 600
   bantime = 3600
   ```

5. **Create the ModSecurity filter** in `/etc/fail2ban/filter.d/apache-modsec.conf`:
   ```ini
   [Definition]
   failregex = ModSecurity: .*\[msg ".*"\] .*\[client <HOST>\] .*
   ignoreregex =
   ```

### Starting Fail2Ban

```bash
sudo systemctl start fail2ban
sudo systemctl status fail2ban
```

### Checking Fail2Ban Status

```bash
# Check status of all jails
sudo fail2ban-client status

# Check status of a specific jail
sudo fail2ban-client status apache-404

# See currently banned IPs
sudo fail2ban-client status apache-404
```

### Manually Banning/Unbanning IPs

```bash
# Ban an IP manually
sudo fail2ban-client set apache-404 banip *************

# Unban an IP manually
sudo fail2ban-client set apache-404 unbanip *************
```

## ModSecurity Implementation

ModSecurity is a web application firewall (WAF) that can provide more granular control over request limiting and IP blocking.

### Installation

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install libapache2-mod-security2

# CentOS/RHEL
sudo yum install mod_security
```

### Basic Configuration

1. **Create the base configuration**:
   ```bash
   sudo cp /etc/modsecurity/modsecurity.conf-recommended /etc/modsecurity/modsecurity.conf
   ```

2. **Edit the configuration file**:
   ```bash
   sudo nano /etc/modsecurity/modsecurity.conf
   ```

3. **Enable ModSecurity**:
   Change `SecRuleEngine DetectionOnly` to `SecRuleEngine On`

### Custom Rate Limiting Rules

1. **Create a directory for custom rules**:
   ```bash
   sudo mkdir -p /etc/modsecurity/rules
   ```

2. **Create a rate limiting rule file** at `/etc/modsecurity/rules/request-limiting.conf`:
   ```apache
   # Initialize IP collection with 60 second timeout
   SecAction "id:5000,phase:1,nolog,pass,initcol:ip=%{REMOTE_ADDR},initcol:ip.requests=0,initcol:ip.nonexistent=0,expirevar:ip.requests=60,expirevar:ip.nonexistent=60"
   
   # Increment counter for each request
   SecRule REQUEST_FILENAME ".*" "id:5001,phase:1,nolog,pass,setvar:ip.requests=+1"
   
   # Check if request is for a non-existent file
   SecRule RESPONSE_STATUS "@eq 404" "id:5002,phase:5,nolog,pass,setvar:ip.nonexistent=+1"
   
   # Block IP if too many requests in general (100 per minute)
   SecRule IP:REQUESTS "@gt 100" "id:5003,phase:1,log,deny,status:429,msg:'Too many requests from %{REMOTE_ADDR} in 1 minute'"
   
   # Block IP if too many requests for non-existent files (20 per minute) - likely scanning
   SecRule IP:NONEXISTENT "@gt 20" "id:5004,phase:1,log,deny,status:403,msg:'Possible scanning detected from %{REMOTE_ADDR}',expirevar:ip.nonexistent=0"
   
   # Whitelist for specific IPs (add your office/home IPs)
   SecRule REMOTE_ADDR "@ipMatch 127.0.0.1,::1" "id:5005,phase:1,nolog,allow,ctl:ruleEngine=Off"
   # SecRule REMOTE_ADDR "@ipMatch YOUR_OFFICE_IP" "id:5006,phase:1,nolog,allow,ctl:ruleEngine=Off"
   ```

3. **Include the custom rules** in your ModSecurity configuration:
   Edit `/etc/apache2/mods-available/security2.conf` (Debian/Ubuntu) or appropriate file for your distribution:

   ```apache
   <IfModule security2_module>
       # Include base rules
       IncludeOptional /etc/modsecurity/*.conf
       
       # Include custom rules
       IncludeOptional /etc/modsecurity/rules/*.conf
   </IfModule>
   ```

4. **Enable ModSecurity and restart Apache**:
   ```bash
   sudo a2enmod security2  # For Debian/Ubuntu
   sudo systemctl restart apache2  # or httpd for CentOS/RHEL
   ```

## ModEvasive Implementation

ModEvasive is specifically designed to detect and block DoS attacks and brute force attempts.

### Installation

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install libapache2-mod-evasive

# CentOS/RHEL
sudo yum install mod_evasive
```

### Configuration

1. **Create a log directory**:
   ```bash
   sudo mkdir /var/log/mod_evasive
   sudo chown www-data:www-data /var/log/mod_evasive  # Use appropriate web server user
   ```

2. **Create configuration file** at `/etc/apache2/mods-available/evasive.conf` (Debian/Ubuntu) or `/etc/httpd/conf.d/mod_evasive.conf` (CentOS/RHEL):

   ```apache
   <IfModule mod_evasive20.c>
       DOSHashTableSize 3097
       DOSPageCount  5     # Maximum number of requests for the same page per second
       DOSSiteCount  50    # Maximum number of total requests per second
       DOSPageInterval 1   # Interval for page count (seconds)
       DOSSiteInterval 1   # Interval for site count (seconds)
       DOSBlockingPeriod 60  # Time to block IP (seconds)
       DOSLogDir "/var/log/mod_evasive"
       DOSEmailNotify <EMAIL>  # Optional: email notification
       DOSWhitelist 127.0.0.1  # Whitelist localhost
       # Add your office/home IPs to whitelist
       # DOSWhitelist your-office-ip
   </IfModule>
   ```

3. **Enable ModEvasive and restart Apache**:
   ```bash
   sudo a2enmod evasive  # For Debian/Ubuntu
   sudo systemctl restart apache2  # or httpd for CentOS/RHEL
   ```

## Testing Your Configuration

### Testing Fail2Ban

1. **Simulate multiple 404 errors**:
   ```bash
   # Run this from another server or your local machine
   for i in {1..25}; do curl -s http://your-server.com/nonexistent-page-$i > /dev/null; done
   ```

2. **Check if the IP was banned**:
   ```bash
   sudo fail2ban-client status apache-404
   ```

### Testing ModSecurity Rate Limiting

1. **Simulate many requests**:
   ```bash
   # Run this from another server or your local machine
   for i in {1..150}; do curl -s http://your-server.com/ > /dev/null; done
   ```

2. **Check ModSecurity logs**:
   ```bash
   sudo tail -f /var/log/apache2/error.log | grep ModSecurity
   ```

### Testing ModEvasive

1. **Simulate rapid requests to the same page**:
   ```bash
   # Run this from another server or your local machine
   for i in {1..10}; do curl -s http://your-server.com/ > /dev/null; done
   ```

2. **Check ModEvasive logs**:
   ```bash
   sudo ls -la /var/log/mod_evasive
   ```

## Monitoring and Maintenance

### Regular Log Review

Set up a routine to review logs for false positives and attack patterns:

```bash
# Review Fail2Ban logs
sudo tail -f /var/log/fail2ban.log

# Review ModSecurity audit logs
sudo tail -f /var/log/apache2/modsec_audit.log

# Review Apache access logs for 404 and 429 status codes
sudo grep " 404 " /var/log/apache2/access.log
sudo grep " 429 " /var/log/apache2/access.log
```

### Whitelist Management

Maintain a list of legitimate IPs that should never be blocked:

1. **For Fail2Ban**, add to `/etc/fail2ban/jail.local`:
   ```ini
   [DEFAULT]
   ignoreip = 127.0.0.1/8 ::1 your-office-ip your-home-ip
   ```

2. **For ModSecurity**, add to your rules file:
   ```apache
   SecRule REMOTE_ADDR "@ipMatch your-office-ip,your-home-ip" "id:5006,phase:1,nolog,allow,ctl:ruleEngine=Off"
   ```

3. **For ModEvasive**, add to your configuration:
   ```apache
   DOSWhitelist your-office-ip
   DOSWhitelist your-home-ip
   ```

### Adjusting Thresholds

If you find the initial settings too strict or too lenient, adjust the thresholds:

1. **For Fail2Ban**, edit `/etc/fail2ban/jail.local` and modify:
   - `maxretry`: Number of failures before banning
   - `findtime`: Time window to count failures (in seconds)
   - `bantime`: How long to ban IPs (in seconds)

2. **For ModSecurity**, edit your rules file and modify:
   - The threshold values in the `@gt` conditions
   - The expiration times in `expirevar` settings

3. **For ModEvasive**, edit your configuration and modify:
   - `DOSPageCount`: Maximum requests for the same page
   - `DOSSiteCount`: Maximum total requests
   - `DOSBlockingPeriod`: How long to block IPs

After any changes, restart the respective service:
```bash
sudo systemctl restart fail2ban
sudo systemctl restart apache2  # or httpd for CentOS/RHEL
```

## Troubleshooting

### Common Issues and Solutions

1. **Legitimate users getting blocked**:
   - Add their IPs to the whitelist
   - Reduce the sensitivity by increasing thresholds
   - Increase the `findtime` to require more failures in a longer period

2. **Rules not triggering**:
   - Check log files for errors
   - Verify the services are running
   - Test with more aggressive request patterns

3. **High server load from ModSecurity**:
   - Consider using ModEvasive instead for pure rate limiting
   - Optimize ModSecurity rules to use fewer resources
   - Increase server resources if necessary

### Viewing Currently Banned IPs

```bash
# For Fail2Ban
sudo fail2ban-client status apache-404
sudo fail2ban-client status apache-ratelimit

# For iptables (used by both Fail2Ban and ModEvasive)
sudo iptables -L -n | grep DROP
```

### Manually Unbanning IPs

```bash
# For Fail2Ban
sudo fail2ban-client set apache-404 unbanip *************

# For iptables directly
sudo iptables -D INPUT -s ************* -j DROP
```

---

This guide provides a comprehensive approach to implementing IP-based rate limiting and automatic blocking. Choose the method that best fits your needs and server environment. For most cases, a combination of Fail2Ban with either ModSecurity or ModEvasive provides excellent protection against a wide range of attacks.

For additional assistance or custom configurations, please contact your system administrator or security team.
