# Queue Worker Log Management Guide

This guide provides information on how to effectively manage log files for queue workers.

## Log File Structure

Queue workers generate two types of log files:

1. **Standard Output Logs**: Contains normal operation messages
   - Path: `/home/<USER>/[environment]/runtime/logs/queue-default-worker.log`

2. **Error Logs**: Contains error messages and exceptions
   - Path: `/home/<USER>/[environment]/runtime/logs/queue-default-worker-error.log`

## Viewing Logs

### Viewing Standard Output Logs

```bash
# View the entire log file
cat /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

# View the last 100 lines
tail -n 100 /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

# Follow the log in real-time
tail -f /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
```

### Viewing Error Logs

```bash
# View the entire error log
cat /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log

# View the last 100 lines
tail -n 100 /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log

# Follow the error log in real-time
tail -f /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log
```

### Viewing Both Logs Simultaneously

```bash
# Follow both logs in real-time
tail -f /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker*.log
```

## Managing Log Size

### Truncating Logs

To clear the content of a log file without deleting it (which would break Supervisor's file handle):

```bash
# Truncate the standard output log
> /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

# Truncate the error log
> /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log
```

This command clears the content while keeping the file, permissions, and file handle intact—so Supervisor keeps writing to it without interruption.

### Filtering Large Log Files

For large log files, you can use `grep` to filter for specific content:

```bash
# Find all error messages
grep -i "error" /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

# Find all messages related to a specific job
grep -i "SendUserEmailJob" /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
```

## Automated Log Management

### Setting Up Log Rotation

For a more structured approach to log management, set up log rotation using `logrotate`:

#### Basic Setup

1. Create a configuration file:

```bash
sudo nano /etc/logrotate.d/queue-workers
```

2. Add the following configuration (or copy from [queue-workers.conf](logrotate/queue-workers.conf)):

```
/home/<USER>/*/runtime/logs/queue-default-worker*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 praxinfo praxinfo
    sharedscripts
    postrotate
        supervisorctl reread
    endscript
}
```

This configuration:
- Rotates logs daily
- Keeps 14 days of logs
- Compresses old logs
- Creates new log files with the correct permissions
- Reloads Supervisor after rotation

3. Test the configuration:

```bash
sudo logrotate -d /etc/logrotate.d/queue-workers
```

4. Force a log rotation (optional):

```bash
sudo logrotate -f /etc/logrotate.d/queue-workers
```

#### Explanation of Configuration Options

- **`/home/<USER>/*/runtime/logs/queue-default-worker*.log`**: The path pattern for log files to rotate. The `*` wildcards match both development and production environments, and both standard and error logs.

- **`daily`**: Rotate logs daily. Other options include `weekly`, `monthly`, or `yearly`.

- **`missingok`**: Don't throw an error if the log file is missing.

- **`rotate 14`**: Keep 14 rotated log files before deleting old ones. Adjust this number based on how much history you want to keep.

- **`compress`**: Compress rotated log files using gzip.

- **`delaycompress`**: Delay compression until the next rotation cycle. This is useful when applications might still be writing to the log file.

- **`notifempty`**: Don't rotate empty log files.

- **`create 0640 praxinfo praxinfo`**: Create new log files with these permissions and ownership after rotation.

- **`sharedscripts`**: Run the postrotate script only once, even if multiple log files match the pattern.

- **`postrotate` ... `endscript`**: Commands to run after rotation. In this case, we're telling Supervisor to reread its configuration, which ensures it continues writing to the new log files.

#### Advanced Configuration Options

##### Size-based Rotation

If you prefer to rotate logs based on size rather than time:

```
/home/<USER>/*/runtime/logs/queue-default-worker*.log {
    size 100M
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 0640 praxinfo praxinfo
    sharedscripts
    postrotate
        supervisorctl reread
    endscript
}
```

This will rotate logs when they reach 100MB in size.

##### Different Rotation Schedules

For different rotation schedules for different log files:

```
/home/<USER>/*/runtime/logs/queue-default-worker.log {
    daily
    rotate 7
    compress
    missingok
    notifempty
    create 0640 praxinfo praxinfo
    postrotate
        supervisorctl reread
    endscript
}

/home/<USER>/*/runtime/logs/queue-default-worker-error.log {
    daily
    rotate 30
    compress
    missingok
    notifempty
    create 0640 praxinfo praxinfo
    postrotate
        supervisorctl reread
    endscript
}
```

This keeps standard logs for 7 days but error logs for 30 days.

##### Date Extension

To add a date extension to rotated logs:

```
/home/<USER>/*/runtime/logs/queue-default-worker*.log {
    daily
    dateext
    dateformat -%Y%m%d
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 0640 praxinfo praxinfo
    sharedscripts
    postrotate
        supervisorctl reread
    endscript
}
```

This will name rotated logs with the date, like `queue-default-worker.log-20250423.gz`.

#### Checking Logrotate Status

To check when logrotate last ran:

```bash
cat /var/lib/logrotate/status
```

#### Troubleshooting

If you encounter issues with logrotate:

1. **Check Permissions**: Make sure logrotate has permission to access and modify the log files.

2. **Check Syntax**: Verify your configuration file syntax:
   ```bash
   sudo logrotate -d /etc/logrotate.d/queue-workers
   ```

3. **Check Log**: Look at the logrotate log:
   ```bash
   sudo cat /var/lib/logrotate/status | grep queue-workers
   ```

4. **Manual Rotation**: Try a manual rotation with verbose output:
   ```bash
   sudo logrotate -vf /etc/logrotate.d/queue-workers
   ```

## Troubleshooting Common Log Issues

### Log Files Not Being Created

If log files are not being created:

1. Check if the log directory exists and has the correct permissions:
   ```bash
   sudo mkdir -p /home/<USER>/dev.quotationmaker.app/runtime/logs
   sudo chmod -R 775 /home/<USER>/dev.quotationmaker.app/runtime
   sudo chown -R praxinfo:praxinfo /home/<USER>/dev.quotationmaker.app/runtime
   ```

2. Create the log files manually:
   ```bash
   sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
   sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log
   sudo chown praxinfo:praxinfo /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker*.log
   ```

### Permission Denied Errors

If you see "Permission denied" errors:

1. Check the ownership of the log files:
   ```bash
   ls -l /home/<USER>/dev.quotationmaker.app/runtime/logs/
   ```

2. Fix the permissions if needed:
   ```bash
   sudo chown praxinfo:praxinfo /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker*.log
   sudo chmod 644 /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker*.log
   ```

## Best Practices

1. **Regular Monitoring**: Check log files regularly for errors and issues
2. **Automated Rotation**: Set up log rotation to prevent log files from growing too large
3. **Separate Error Logs**: Use separate log files for standard output and errors
4. **Proper Permissions**: Ensure log files have the correct ownership and permissions
5. **Truncate Instead of Delete**: Use the truncation command (`> logfile.log`) for quick cleanup without disrupting Supervisor
