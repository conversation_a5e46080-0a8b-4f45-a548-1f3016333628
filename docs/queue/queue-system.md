# Queue System Documentation

## Overview

The queue system allows for asynchronous processing of tasks, improving application performance and user experience by offloading time-consuming operations to background processes. The implementation is inspired by <PERSON><PERSON>'s queue system but built on top of Yii2's queue extension.

## Installation

### 1. Install the Yii2 Queue Extension

```bash
composer require yiisoft/yii2-queue:~2.3.7
```

### 2. Install Redis Extension (for Redis Driver)

If you plan to use the Redis driver (recommended for production), you'll need to install the Yii2 Redis extension:

```bash
composer require yiisoft/yii2-redis:~2.0.19
```

### 3. Run the Migration (for DB Driver)

If you're using the DB driver, you need to run the migration to create the queue table:

```bash
php yii migrate/up m250422_205701_create_queue_table
```

## Features

- Multiple driver support (Database, Redis, Sync)
- Environment-specific configuration
- Delayed job execution
- Job retry with backoff
- Failed job handling
- Laravel-like syntax and API
- Fluent interface for queue operations
- Integration with email system
- Comprehensive error handling
- Console commands for queue management
- Simplified single-queue approach (see [Simplified Queue Approach](simplified-queue-approach.md))

## Queue Drivers

### Database Driver

The database driver uses a database table to store job information. This is suitable for simple applications or development environments.

#### Configuration

```php
'components' => [
    'queue' => [
        'class' => \yii\queue\db\Queue::class,
        'db' => 'db',
        'tableName' => '{{%queue}}',
        'channel' => 'default',
        'mutex' => \yii\mutex\MysqlMutex::class,
    ],
],
```

### Redis Driver

The Redis driver uses Redis for storing job information. This is recommended for production environments due to its performance and reliability.

#### Configuration

```php
'components' => [
    'queue' => [
        'class' => \yii\queue\redis\Queue::class,
        'redis' => 'redis',
        'channel' => 'default',
    ],
],
```

### Sync Driver

The sync driver processes jobs immediately in the same request. This is useful for testing and debugging.

#### Configuration

```php
'components' => [
    'queue' => [
        'class' => \yii\queue\sync\Queue::class,
    ],
],
```

## Architecture

The queue system consists of several components:

1. **QueueService**: A Laravel-like interface for working with queues
2. **Job Base Class**: A base class for all jobs with retry and error handling
3. **Email Integration**: Integration with the EmailService and EmailClient
4. **Console Commands**: Commands for managing the queue
5. **Environment-Specific Configuration**: Different queue drivers for different environments

## Job Implementation

Jobs can be implemented in two ways:

### 1. Using the Job Base Class

The recommended way is to extend the `\common\jobs\Job` base class:

```php
namespace common\jobs;

use common\models\User;
use Yii;
use yii\queue\Queue;

class SendUserEmailJob extends Job
{
    public $userId;
    public $template;
    public $params;
    public $subject;

    public function __construct($config = [])
    {
        // Set default values
        $this->tries = 3;
        $this->retryAfter = 300; // 5 minutes
        $this->timeout = 60;
        $this->queue = 'emails';

        parent::__construct($config);
    }

    /**
     * Handle the job
     *
     * @param Queue|null $queue The queue instance (optional)
     * @return bool Whether the email was sent successfully
     */
    public function handle($queue = null)
    {
        $user = User::findOne($this->userId);

        if (!$user) {
            Yii::error("User not found: {$this->userId}");
            return false;
        }

        $emailService = new \common\services\EmailService();
        return $emailService->sendUserMail(
            $user,
            $this->template,
            $this->params,
            $this->subject
        );
    }

    public function failed($exception)
    {
        Yii::error("Failed to send email to user {$this->userId}: " . $exception->getMessage());
    }
}
```

### 2. Using the JobInterface

You can also implement the `JobInterface` directly:

```php
namespace common\jobs;

use yii\base\BaseObject;
use yii\queue\JobInterface;
use yii\queue\Queue;

class EmailJob extends BaseObject implements JobInterface
{
    public $to;
    public $subject;
    public $body;

    public function execute($queue)
    {
        // Send email logic here
        return true;
    }
}
```

### 3. Using Callable Jobs

For simple tasks, you can use callables (closures, functions, etc.) as jobs:

```php
// Using a closure as a job
$queueService->push(function($data) {
    // Process data
    return sendEmail($data['to'], $data['subject'], $data['body']);
}, [
    'to' => '<EMAIL>',
    'subject' => 'Hello',
    'body' => 'This is a test email'
]);

// Queue-aware callable
$queueService->push(function($data, $queue) {
    // Process data
    $result = processData($data);

    // Push another job to the queue
    $queue->push(new AnotherJob(['data' => $result]));

    return true;
}, ['key' => 'value']);
```

See [Advanced Job Features](advanced-job-features.md) for more details on queue-aware jobs and callable jobs.

## Dispatching Jobs

### Using QueueService

The recommended way to dispatch jobs is using the `QueueService`:

```php
// Get the QueueService instance
$queueService = Yii::$app->queueService;

// Push a job to the queue
$queueService->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));

// Push a job with a delay
$queueService->delay(60)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Push a job with a delay (alternative syntax)
$queueService->laterPush(60, new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Push a job to a specific queue
$queueService->onQueue('emails')->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));
```

### Using Yii2 Queue Component

You can also use the Yii2 queue component directly:

```php
Yii::$app->queue->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));
```

## Delayed Jobs

### Using QueueService

```php
// Delay for 5 minutes
$queueService->delay(5 * 60)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Schedule for a specific time
$tomorrow = new \DateTime('tomorrow');
$queueService->later($tomorrow)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));
```

### Using Yii2 Queue Component

```php
Yii::$app->queue->delay(5 * 60)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));
```

## Synchronous Execution

In addition to asynchronous processing, the queue system supports synchronous execution of jobs:

```php
// Execute a job synchronously
$queueService->sync(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));

// Execute a callable synchronously
$queueService->sync(function($data) {
    // Process data
    return true;
}, ['key' => 'value']);
```

### Automatic Fallback to Synchronous Execution

The system will automatically fall back to synchronous execution if the queue is not available:

```php
// This will use the queue if available, or execute synchronously if not
$emailService->queue()->sendUserMail($user, $template, $params, $subject);
```

See [Advanced Job Features](advanced-job-features.md) for more details on synchronous vs. asynchronous execution.

## Job Retry

### Using Job Base Class

The `Job` base class provides built-in retry functionality:

```php
class SendEmailJob extends \common\jobs\Job
{
    public function __construct($config = [])
    {
        // Set retry configuration
        $this->tries = 3; // Number of attempts
        $this->retryAfter = 300; // 5 minutes

        parent::__construct($config);
    }

    // ...
}
```

### Using Queue Configuration

You can also configure retry at the queue level:

```php
'components' => [
    'queue' => [
        'class' => \yii\queue\redis\Queue::class,
        'redis' => 'redis',
        'channel' => 'default',
        'attempts' => 3,
        'ttr' => 5 * 60, // 5 minutes
    ],
],
```

## Console Commands

The queue system provides several console commands for managing the queue:

```bash
# Start a worker in daemon mode (continuously listens for new jobs)
yii queue/listen

# Start a worker for a specific queue in daemon mode
yii queue/listen --queue=emails

# Process current jobs and then exit
yii queue/run

# Process a limited number of jobs and then exit
yii queue/run --limit=10

# Process jobs with a timeout between checks
yii queue/listen --timeout=5

# Clear the queue
yii queue/clear

# Show queue information
yii queue/info

# Remove a specific job
yii queue/remove 123
```

## Best Practices

1. **Keep Jobs Small**: Jobs should be focused on a single task
2. **Make Jobs Idempotent**: Jobs should be safe to run multiple times
3. **Handle Failures**: Implement proper error handling in jobs
4. **Monitor Queue Size**: Keep an eye on queue size to prevent backlog
5. **Use Appropriate Driver**: Choose the right driver for your environment
6. **Set Appropriate Timeouts**: Configure timeouts based on job complexity
7. **Implement Logging**: Log job execution for debugging and monitoring

## Integration with Email System

The queue system is integrated with the email system, allowing you to queue emails for asynchronous processing:

```php
// Queue an email to be sent
$emailService = new \common\services\EmailService();
$emailService->queue()->sendUserMail(
    $user,
    'welcome-email',
    ['user' => $user],
    'Welcome to Quotation Pro'
);

// Queue an admin email
$emailService->queue()->sendAdminMail(
    'notifications/new-user',
    ['user' => $user],
    'New User Registration'
);

// Queue an urgent notification
$emailService->queue()->sendUrgentNotificationToAdmin(
    'URGENT: System Alert',
    'The system has detected an issue that requires immediate attention.',
    ['timestamp' => time(), 'severity' => 'high']
);
```

## Other Integration Possibilities

The queue system can be integrated with other systems in the application:

- **PDF Generation**: Queue PDF generation for large documents
- **Data Import/Export**: Queue data processing for large datasets
- **API Integrations**: Queue API calls to third-party services
- **Scheduled Tasks**: Queue recurring tasks using cron jobs
- **Notifications**: Queue push notifications and SMS messages

## Monitoring and Maintenance

Regular monitoring and maintenance of the queue system is essential:

- Monitor queue size and processing time
- Set up alerts for queue backlog
- Regularly check for failed jobs
- Implement logging for job execution
- Set up proper error reporting for failed jobs

## Production Deployment

For production deployment, we recommend using Supervisor to manage queue workers for Redis-based queues and cron jobs for DB-based queues.

### Supervisor Setup (Redis Driver)

Supervisor is a process control system that allows you to monitor and control processes on Unix-like operating systems. It's perfect for managing queue workers in production environments.

See the [Supervisor Setup Guide](supervisor-setup-guide.md) for detailed instructions on setting up Supervisor for both development and production environments on the same VPS server.

Configuration files for Supervisor are available in the `docs/queue/supervisor/` directory:
- [quotation-dev-queue.conf](supervisor/quotation-dev-queue.conf) - Configuration for development environment
- [quotation-prod-queue.conf](supervisor/quotation-prod-queue.conf) - Configuration for production environment

### Cron Job Setup (DB Driver)

For the DB driver, you'll need to set up cron jobs to periodically process the queued tasks.

See the [Cron Job Setup Guide](cron-setup-guide.md) for detailed instructions on setting up cron jobs for the DB driver.

A sample crontab configuration is available in the `docs/queue/cron/` directory:
- [quotation-db-queue.crontab](cron/quotation-db-queue.crontab) - Crontab configuration for DB driver

### Redis Configuration

For Redis-based queues, you'll need to configure Redis properly for optimal performance and security.

See the [Redis Setup Guide](redis-setup-guide.md) for detailed instructions on setting up and configuring Redis for the queue system.
