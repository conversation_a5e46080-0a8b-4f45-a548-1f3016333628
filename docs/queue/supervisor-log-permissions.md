# Supervisor Log Permissions Guide

This guide explains how to properly configure Supervisor log permissions to avoid issues with log files being created with root ownership.

## The Problem

By default, <PERSON>visor creates log files with the ownership of the user that started the Supervisor daemon, which is typically `root`. This can cause issues when:

1. The application needs to write to or rotate these log files
2. You need to view or analyze the logs without using sudo
3. Log rotation scripts fail due to permission issues

## The Solution

There are two key steps to solving this problem:

### 1. Create Log Files Beforehand

The most reliable approach is to create the log files and set their permissions before starting Supervisor:

```bash
# Create the log directory
sudo mkdir -p /home/<USER>/praxinfo.quotationmaker.app/runtime/logs

# Create the log files
sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log

# Set the correct permissions
sudo chmod -R 775 /home/<USER>/praxinfo.quotationmaker.app/runtime
sudo chown -R praxinfo:praxinfo /home/<USER>/praxinfo.quotationmaker.app/runtime
```

### 2. Configure Supervisor Log Ownership

Supervisor provides two directives to control the ownership of log files:

- `logfile_user`: The user that should own the log file
- `logfile_group`: The group that should own the log file

> **Important**: Even with these directives, you might still encounter permission issues if the log files don't exist or have incorrect ownership. Creating the files beforehand is the most reliable approach.

## Configuring Log File Ownership

Add these directives to your Supervisor program configuration:

```ini
[program:quotation-prod-queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/praxinfo.quotationmaker.app/yii queue/listen --verbose=1 --color=0 --queue=default
autostart=true
autorestart=true
user=praxinfo
numprocs=4
redirect_stderr=false
stdout_logfile=/home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
stderr_logfile=/home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log
stopwaitsecs=60
logfile_user=praxinfo
logfile_group=praxinfo
```

The `logfile_user` and `logfile_group` directives ensure that the log files are created with the specified ownership.

## Fixing Existing Log Files

If you already have log files with incorrect ownership, you can fix them with:

```bash
sudo chown praxinfo:praxinfo /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
```

## Reloading Supervisor

After making these changes, reload Supervisor:

```bash
sudo supervisorctl reread
sudo supervisorctl update
```

## Verifying the Fix

Check the ownership of the log file after restarting the worker:

```bash
ls -l /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
```

It should now show `praxinfo` as the owner instead of `root`.

## Common Issues

### Log Files Still Owned by Root

If the log files are still owned by root after making these changes:

1. Make sure you've reloaded Supervisor
2. Restart the specific program:
   ```bash
   sudo supervisorctl restart quotation-prod-queue-default:*
   ```
3. Check if the log directory exists and has the correct permissions:
   ```bash
   sudo mkdir -p /home/<USER>/praxinfo.quotationmaker.app/runtime/logs
   sudo chown -R praxinfo:praxinfo /home/<USER>/praxinfo.quotationmaker.app/runtime
   ```

### Permission Denied Errors

If you see "Permission denied" errors in the Supervisor logs:

1. Make sure the user specified in `logfile_user` exists on the system
2. Make sure the log directory is writable by that user:
   ```bash
   sudo chmod -R 775 /home/<USER>/praxinfo.quotationmaker.app/runtime/logs
   ```

## Managing Log Files

### Truncating Log Files

To clear the content of a log file without deleting it (which would break Supervisor's file handle):

```bash
# Truncate the standard output log
> /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log

# Truncate the error log
> /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log
```

This command clears the content while keeping the file, permissions, and file handle intact—so Supervisor keeps writing to it without interruption.

### Log Rotation

For a more structured approach to log management, consider setting up log rotation using `logrotate`. This will automatically archive and compress old logs while maintaining a manageable file size.

## Best Practices

1. Always use the same user for both `user` and `logfile_user`
2. Make sure log directories exist and have the correct permissions before starting Supervisor
3. Use a dedicated user for your application rather than `www-data` or `root`
4. Set up proper log rotation to prevent log files from growing too large
5. Use the truncation command (`> logfile.log`) for quick cleanup without disrupting Supervisor
