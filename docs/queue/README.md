# Queue System Documentation

This directory contains documentation for the queue system used in the Quotation Pro application.

## Table of Contents

1. [Queue System Overview](queue-system.md)
2. [Queue Usage Examples](queue-usage-examples.md)
3. [Advanced Job Features](advanced-job-features.md)
4. [Simplified Queue Approach](simplified-queue-approach.md)
5. [Supervisor Setup Guide](supervisor-setup-guide.md)
6. [Redis Setup Guide](redis-setup-guide.md)
7. [Redis Queue Monitoring Guide](redis-queue-monitoring.md)
8. [Cron Job Setup Guide](cron-setup-guide.md)
9. [Supervisor Log Permissions Guide](supervisor-log-permissions.md)
10. [Queue Worker Log Management Guide](log-management-guide.md)
11. [Logrotate Configuration Files](logrotate/README.md)
12. [Queue Command Options](queue-command-options.md)
13. [Queue Performance Optimization](queue-performance-optimization.md)

## Quick Start

To get started with the queue system, follow these steps:

1. Install the required packages:
   ```bash
   composer require yiisoft/yii2-queue:~2.3.7
   composer require yiisoft/yii2-redis:~2.0.19
   ```

2. Run the migration to create the queue table (for DB driver):
   ```bash
   php yii migrate/up m250422_205701_create_queue_table
   ```

3. Start a queue worker:
   ```bash
   php yii queue/listen
   ```

4. Push a job to the queue:
   ```php
   Yii::$app->queueService->push(new \common\jobs\SendUserEmailJob([
       'userId' => $user->id,
       'template' => 'welcome-email',
       'params' => ['user' => $user],
       'subject' => 'Welcome to Quotation Pro',
   ]));
   ```

## Key Features

- Multiple driver support (Database, Redis, Sync)
- Environment-specific configuration
- Delayed job execution
- Job retry with backoff
- Failed job handling
- Queue-aware jobs
- Callable jobs
- Synchronous fallback
- Comprehensive error handling

## Production Deployment

For production deployment, we recommend using:

- Redis driver for better performance
- Supervisor for managing queue workers
- Multiple workers for different queues
- Monitoring and alerting for queue health

See the [Supervisor Setup Guide](supervisor-setup-guide.md) for detailed instructions on setting up queue workers in production.
