# Supervisor Setup Guide for Queue Workers

This guide provides instructions for setting up Supervisor to manage queue workers for both development and production environments on the same VPS server.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installing Supervisor](#installing-supervisor)
3. [Installing Redis](#installing-redis)
4. [Configuring Supervisor for Queue Workers](#configuring-supervisor-for-queue-workers)
5. [Setting Up Cron Jobs for DB Driver](#setting-up-cron-jobs-for-db-driver)
6. [Monitoring and Management](#monitoring-and-management)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

- Ubuntu/Debian-based VPS server
- PHP 7.4 or higher
- Composer
- Redis (for dev and prod environments)
- MySQL/MariaDB (for local environment with DB driver)

## Installing Supervisor

Supervisor is a process control system that allows you to monitor and control processes on Unix-like operating systems. It's perfect for managing queue workers.

```bash
# Update package lists
sudo apt update

# Install Supervisor
sudo apt install -y supervisor

# Start Supervisor service
sudo systemctl start supervisor

# Enable Supervisor to start on boot
sudo systemctl enable supervisor

# Check status
sudo systemctl status supervisor
```

## Installing Redis

Redis is required for the queue system in development and production environments.

```bash
# Update package lists
sudo apt update

# Install Redis
sudo apt install -y redis-server

# Configure Redis to start on boot
sudo systemctl enable redis-server

# Start Redis
sudo systemctl start redis-server

# Check Redis status
sudo systemctl status redis-server
```

### Securing Redis

For production environments, it's recommended to secure Redis:

```bash
# Edit Redis configuration
sudo nano /etc/redis/redis.conf
```

Make the following changes:
1. Set a password: `requirepass your_strong_password`
2. Bind to localhost only: `bind 127.0.0.1`
3. Disable protected mode: `protected-mode no` (only if you need remote access)

```bash
# Restart Redis to apply changes
sudo systemctl restart redis-server
```

Update your application's Redis configuration in `common/config/apps-settings/quotation-prod/main-queue.php` to include the password:

```php
return [
    'components' => [
        'queue' => [
            'class' => \yii\queue\redis\Queue::class,
            'redis' => [
                'class' => \yii\redis\Connection::class,
                'hostname' => 'localhost',
                'port' => 6379,
                'database' => 0,
                'password' => 'your_strong_password',
            ],
            'channel' => 'default',
            'as log' => \yii\queue\LogBehavior::class,
        ],
    ],
];
```

## Configuring Supervisor for Queue Workers

### 1. Create Configuration Files

Create separate configuration files for development and production environments:

#### Development Environment (dev.quotationmaker.app)

Create a file at `/etc/supervisor/conf.d/quotation-dev-queue-default.conf`:

```ini
[program:quotation-dev-queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/dev.quotationmaker.app/yii queue/listen --color=0 --queue=default --timeout=10
autostart=true
autorestart=true
user=praxinfo
numprocs=2
redirect_stderr=false
stdout_logfile=/home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
stderr_logfile=/home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log
stopwaitsecs=60
logfile_user=praxinfo
logfile_group=praxinfo
```

#### Production Environment (praxinfo.quotationmaker.app)

Create a file at `/etc/supervisor/conf.d/quotation-prod-queue-default.conf`:

```ini
[program:quotation-prod-queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/praxinfo.quotationmaker.app/yii queue/listen --color=0 --queue=default --timeout=10
autostart=true
autorestart=true
user=praxinfo
numprocs=4
redirect_stderr=false
stdout_logfile=/home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
stderr_logfile=/home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log
stopwaitsecs=60
logfile_user=praxinfo
logfile_group=praxinfo
```

### 2. Create Log Directories and Files

It's important to create the log directories and files with the correct permissions before starting Supervisor. This prevents permission issues that can occur when Supervisor tries to create the log files itself.

```bash
# For development environment
sudo mkdir -p /home/<USER>/dev.quotationmaker.app/runtime/logs

# Create the log files
sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log

# Set the correct permissions
sudo chmod -R 775 /home/<USER>/dev.quotationmaker.app/runtime
sudo chown -R praxinfo:praxinfo /home/<USER>/dev.quotationmaker.app/runtime

# For production environment
sudo mkdir -p /home/<USER>/praxinfo.quotationmaker.app/runtime/logs

# Create the log files
sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log

# Set the correct permissions
sudo chmod -R 775 /home/<USER>/praxinfo.quotationmaker.app/runtime
sudo chown -R praxinfo:praxinfo /home/<USER>/praxinfo.quotationmaker.app/runtime
```

> **Important**: Creating the log files beforehand with the correct ownership is crucial. Even with `logfile_user` and `logfile_group` directives in the Supervisor configuration, you might still encounter permission issues if the log files don't exist or have incorrect ownership.

### 3. Update Supervisor Configuration

After creating the configuration files, update Supervisor to recognize the new programs:

```bash
# Reload Supervisor configuration
sudo supervisorctl reread

# Update Supervisor to add the new programs
sudo supervisorctl update

# Check status of all programs
sudo supervisorctl status
```

### 4. Managing Queue Workers

You can manage the queue workers using the following commands:

```bash
# Start all queue workers
sudo supervisorctl start all

# Start specific queue workers
sudo supervisorctl start quotation-dev-queue-default:*
sudo supervisorctl start quotation-prod-queue-default:*

# Stop all queue workers
sudo supervisorctl stop all

# Stop specific queue workers
sudo supervisorctl stop quotation-dev-queue-default:*
sudo supervisorctl stop quotation-prod-queue-default:*

# Restart all queue workers
sudo supervisorctl restart all

# Restart specific queue workers
sudo supervisorctl restart quotation-dev-queue-default:*
sudo supervisorctl restart quotation-prod-queue-default:*
```

### 5. Detailed Implementation Steps

Follow these steps to implement the queue system on your server:

#### For Development Environment (dev.quotationmaker.app):

1. Copy the supervisor configuration file to the supervisor config directory:
   ```bash
   sudo cp quotation-dev-queue-default.conf /etc/supervisor/conf.d/
   ```

2. Create the log directories and files with the correct permissions:
   ```bash
   sudo mkdir -p /home/<USER>/dev.quotationmaker.app/runtime/logs

   # Create the log files
   sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log
   sudo touch /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log

   # Set the correct permissions
   sudo chmod -R 775 /home/<USER>/dev.quotationmaker.app/runtime
   sudo chown -R praxinfo:praxinfo /home/<USER>/dev.quotationmaker.app/runtime
   ```

3. Reload supervisor to apply the changes:
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   ```

4. Check the status of the queue worker:
   ```bash
   sudo supervisorctl status quotation-dev-queue-default:*
   ```

#### For Production Environment (praxinfo.quotationmaker.app):

1. Copy the supervisor configuration file to the supervisor config directory:
   ```bash
   sudo cp quotation-prod-queue-default.conf /etc/supervisor/conf.d/
   ```

2. Create the log directories and files with the correct permissions:
   ```bash
   sudo mkdir -p /home/<USER>/praxinfo.quotationmaker.app/runtime/logs

   # Create the log files
   sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
   sudo touch /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker-error.log

   # Set the correct permissions
   sudo chmod -R 775 /home/<USER>/praxinfo.quotationmaker.app/runtime
   sudo chown -R praxinfo:praxinfo /home/<USER>/praxinfo.quotationmaker.app/runtime
   ```

3. Reload supervisor to apply the changes:
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   ```

4. Check the status of the queue worker:
   ```bash
   sudo supervisorctl status quotation-prod-queue-default:*
   ```

### 6. Managing Log Files

#### Truncating Log Files

To clear the content of a log file without deleting it (which would break Supervisor's file handle):

```bash
# Truncate the standard output log
> /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

# Truncate the error log
> /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker-error.log
```

This command clears the content while keeping the file, permissions, and file handle intact—so Supervisor keeps writing to it without interruption.

#### Rotating Log Files

For a more structured approach to log management, consider setting up log rotation using `logrotate`. This will automatically archive and compress old logs while maintaining a manageable file size.

### 7. Optimizing CPU Usage

Queue workers can consume significant CPU resources, especially when running in daemon mode with continuous polling. Here are some strategies to reduce CPU usage:

#### Using the Timeout Parameter

The `--timeout` parameter is crucial for reducing CPU usage. It specifies how long (in seconds) the worker should wait between checks when the queue is empty:

```bash
php yii queue/listen --timeout=10
```

Without a timeout, the worker will continuously poll Redis, potentially causing high CPU usage. A timeout of 5-10 seconds is usually a good balance between responsiveness and CPU usage.

#### Reducing Worker Processes

If CPU usage is still high, consider reducing the number of worker processes:

```ini
numprocs=1
```

This will reduce the overall CPU usage while still processing jobs (albeit potentially more slowly).

#### Using Cron Jobs Instead of Daemon Mode

For lower-volume queues, you can use cron jobs instead of daemon mode:

```crontab
* * * * * php /home/<USER>/dev.quotationmaker.app/yii queue/run --limit=100 > /dev/null 2>&1
```

This runs the worker once per minute, processes up to 100 jobs, and then exits.

### 8. Monitoring and Troubleshooting

To monitor the queue workers, you can:

1. Check the status of all supervisor processes:
   ```bash
   sudo supervisorctl status
   ```

2. View the queue worker logs:
   ```bash
   # For development environment
   tail -f /home/<USER>/dev.quotationmaker.app/runtime/logs/queue-default-worker.log

   # For production environment
   tail -f /home/<USER>/praxinfo.quotationmaker.app/runtime/logs/queue-default-worker.log
   ```

3. Restart the queue workers if needed:
   ```bash
   # For development environment
   sudo supervisorctl restart quotation-dev-queue-default:*

   # For production environment
   sudo supervisorctl restart quotation-prod-queue-default:*
   ```

4. Check the queue information:
   ```bash
   # For development environment
   php /home/<USER>/dev.quotationmaker.app/yii queue/info

   # For production environment
   php /home/<USER>/praxinfo.quotationmaker.app/yii queue/info
   ```

## Setting Up Cron Jobs for DB Driver

For the local environment using the DB driver, you'll need to set up cron jobs to process the queue:

```bash
# Edit crontab
crontab -e
```

Add the following entries:

```crontab
# Run queue/run command every minute for quotation-local (DB driver)
* * * * * php /path/to/quotation-local/yii queue/run --verbose=1 > /path/to/quotation-local/runtime/logs/queue-run.log 2>&1

# Run queue/run command for specific queues
* * * * * php /path/to/quotation-local/yii queue/run --verbose=1 --queue=urgent > /path/to/quotation-local/runtime/logs/queue-urgent-run.log 2>&1
* * * * * php /path/to/quotation-local/yii queue/run --verbose=1 --queue=emails > /path/to/quotation-local/runtime/logs/queue-emails-run.log 2>&1

# Clear old jobs from the queue table once a day
0 0 * * * php /path/to/quotation-local/yii queue/clear --olderThan=86400 > /path/to/quotation-local/runtime/logs/queue-clear.log 2>&1
```

> **Note:** Replace `/path/to/quotation-local` with the actual path to your local application.

## Monitoring and Management

### Supervisor Web Interface

You can set up the Supervisor web interface for easier monitoring:

```bash
# Install required package
sudo apt install -y python3-pip
sudo pip3 install supervisor-web

# Edit Supervisor configuration
sudo nano /etc/supervisor/supervisord.conf
```

Add the following at the end of the file:

```ini
[inet_http_server]
port=127.0.0.1:9001
username=admin
password=your_strong_password
```

Restart Supervisor:

```bash
sudo systemctl restart supervisor
```

You can now access the Supervisor web interface at `http://localhost:9001` (you may need to set up an SSH tunnel or configure a reverse proxy to access it remotely).

### Monitoring Queue Size

You can monitor the queue size using Redis CLI:

```bash
# Connect to Redis
redis-cli

# Check the size of the default queue
LLEN queue

# Check the size of specific queues
LLEN queue:urgent
LLEN queue:emails
```

## Troubleshooting

### Common Issues

#### 1. Queue Workers Not Starting

Check the Supervisor logs:

```bash
sudo tail -f /var/log/supervisor/supervisord.log
```

Check the application logs:

```bash
tail -f /path/to/quotation-dev/runtime/logs/queue-worker.log
```

#### 2. Redis Connection Issues

Check if Redis is running:

```bash
sudo systemctl status redis-server
```

Check Redis connectivity:

```bash
redis-cli ping
```

If using a password:

```bash
redis-cli -a your_strong_password ping
```

#### 3. Permission Issues

Make sure the user specified in the Supervisor configuration (e.g., `www-data`) has the necessary permissions:

```bash
sudo chown -R www-data:www-data /path/to/quotation-dev
sudo chown -R www-data:www-data /path/to/quotation-prod
```

#### 4. Memory Issues

If workers are being killed due to memory issues, you can limit the memory usage in the Supervisor configuration:

```ini
[program:quotation-prod-queue-listen]
...
environment=MALLOC_ARENA_MAX=2
...
```

This limits the number of memory arenas used by glibc, which can help reduce memory usage.

### Restarting After Configuration Changes

After making changes to the Supervisor configuration files, you need to reload the configuration and restart the affected programs:

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl restart all
```
