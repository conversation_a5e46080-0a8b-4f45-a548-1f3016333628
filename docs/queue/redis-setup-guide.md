# Redis Setup Guide for Queue System

This guide provides detailed instructions for setting up and configuring Redis for the queue system in both development and production environments.

## Table of Contents

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Basic Configuration](#basic-configuration)
4. [Security Configuration](#security-configuration)
5. [Performance Tuning](#performance-tuning)
6. [Monitoring](#monitoring)
7. [Backup and Recovery](#backup-and-recovery)
8. [Troubleshooting](#troubleshooting)

## Introduction

Redis is an in-memory data structure store that can be used as a database, cache, and message broker. In our application, we use Redis as a queue backend for processing asynchronous tasks in development and production environments.

## Installation

### Ubuntu/Debian

```bash
# Update package lists
sudo apt update

# Install Redis
sudo apt install -y redis-server

# Start Redis service
sudo systemctl start redis-server

# Enable Redis to start on boot
sudo systemctl enable redis-server

# Check Redis status
sudo systemctl status redis-server
```

### CentOS/RHEL

```bash
# Install EPEL repository
sudo yum install -y epel-release

# Install Redis
sudo yum install -y redis

# Start Redis service
sudo systemctl start redis

# Enable Redis to start on boot
sudo systemctl enable redis

# Check Redis status
sudo systemctl status redis
```

## Basic Configuration

The main Redis configuration file is located at `/etc/redis/redis.conf` (Ubuntu/Debian) or `/etc/redis.conf` (CentOS/RHEL).

### Key Configuration Options

Edit the configuration file:

```bash
sudo nano /etc/redis/redis.conf
```

Here are some important configuration options:

```conf
# Bind Redis to localhost only (more secure)
bind 127.0.0.1

# Set the port (default is 6379)
port 6379

# Set the maximum memory Redis can use
maxmemory 256mb

# Set the memory policy (what to do when memory limit is reached)
maxmemory-policy allkeys-lru

# Enable persistence
appendonly yes
appendfsync everysec

# Set the database count (default is 16)
databases 16
```

After making changes, restart Redis:

```bash
sudo systemctl restart redis-server
```

## Security Configuration

### Password Authentication

It's recommended to set a password for Redis, especially in production environments:

```bash
sudo nano /etc/redis/redis.conf
```

Add or uncomment the following line:

```conf
requirepass your_strong_password
```

### Disable Protected Mode

If you need to connect to Redis from remote hosts, you'll need to disable protected mode:

```conf
protected-mode no
```

> **Note:** Only do this if you have other security measures in place, such as a firewall or binding to specific interfaces.

### Firewall Configuration

If you're using UFW (Ubuntu):

```bash
sudo ufw allow from 127.0.0.1 to any port 6379
```

If you're using firewalld (CentOS/RHEL):

```bash
sudo firewall-cmd --permanent --add-port=6379/tcp
sudo firewall-cmd --reload
```

## Performance Tuning

### Memory Configuration

Redis is an in-memory database, so memory configuration is crucial:

```conf
# Set the maximum memory Redis can use
maxmemory 1gb

# Set the memory policy
maxmemory-policy volatile-lru
```

Memory policies:
- `noeviction`: Return errors when memory limit is reached
- `allkeys-lru`: Evict least recently used keys
- `volatile-lru`: Evict least recently used keys with an expire set
- `allkeys-random`: Evict random keys
- `volatile-random`: Evict random keys with an expire set
- `volatile-ttl`: Evict keys with expire set and shortest TTL

### Persistence Configuration

Redis offers two persistence options:

1. RDB (Redis Database): Point-in-time snapshots
2. AOF (Append Only File): Logs every write operation

For queue systems, AOF is often preferred:

```conf
# Enable AOF
appendonly yes

# Set fsync policy
appendfsync everysec
```

Fsync policies:
- `always`: Fsync after every write (safest, slowest)
- `everysec`: Fsync every second (good compromise)
- `no`: Let the OS handle fsync (fastest, least safe)

## Monitoring

### Basic Monitoring Commands

Connect to Redis CLI:

```bash
redis-cli
```

If you've set a password:

```bash
redis-cli -a your_strong_password
```

Useful monitoring commands:

```bash
# Get server info
INFO

# Get memory stats
INFO memory

# Get client connections
CLIENT LIST

# Monitor commands in real-time
MONITOR

# Check queue size
LLEN queue
LLEN queue:urgent
LLEN queue:emails
```

### Redis Monitoring Tools

1. **Redis Commander**: Web-based Redis management tool
   ```bash
   npm install -g redis-commander
   redis-commander --redis-host localhost --redis-port 6379 --redis-password your_strong_password
   ```

2. **RedisInsight**: GUI tool for Redis
   Download from: https://redislabs.com/redis-enterprise/redis-insight/

## Backup and Recovery

### Creating Backups

Redis automatically creates RDB snapshots based on your configuration. You can also trigger a backup manually:

```bash
redis-cli SAVE
```

The default location for the RDB file is `/var/lib/redis/dump.rdb`.

### Backup Script

Create a backup script:

```bash
#!/bin/bash
BACKUP_DIR="/path/to/redis/backups"
DATETIME=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/redis_backup_$DATETIME.rdb"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create Redis backup
redis-cli SAVE

# Copy the dump file
cp /var/lib/redis/dump.rdb $BACKUP_FILE

# Compress the backup
gzip $BACKUP_FILE

# Keep only the last 7 backups
find $BACKUP_DIR -name "redis_backup_*.rdb.gz" -type f -mtime +7 -delete
```

Make the script executable:

```bash
chmod +x redis_backup.sh
```

Add it to crontab:

```bash
crontab -e
```

```crontab
0 0 * * * /path/to/redis_backup.sh
```

### Restoring from Backup

To restore from a backup:

1. Stop Redis:
   ```bash
   sudo systemctl stop redis-server
   ```

2. Replace the dump.rdb file:
   ```bash
   sudo cp /path/to/backup/dump.rdb /var/lib/redis/dump.rdb
   sudo chown redis:redis /var/lib/redis/dump.rdb
   ```

3. Start Redis:
   ```bash
   sudo systemctl start redis-server
   ```

## Troubleshooting

### Common Issues

#### 1. Redis Won't Start

Check the logs:

```bash
sudo tail -f /var/log/redis/redis-server.log
```

Common issues:
- Permission problems with the data directory
- Configuration errors
- Memory issues

#### 2. Connection Refused

If you can't connect to Redis:

- Check if Redis is running: `sudo systemctl status redis-server`
- Check the binding configuration: `grep "bind" /etc/redis/redis.conf`
- Check if the port is open: `sudo netstat -tuln | grep 6379`

#### 3. Authentication Failed

If you get authentication errors:

- Check if a password is set: `grep "requirepass" /etc/redis/redis.conf`
- Make sure you're using the correct password in your application configuration

#### 4. Memory Issues

If Redis is using too much memory:

- Check current memory usage: `redis-cli INFO memory`
- Adjust the `maxmemory` setting in the configuration
- Consider changing the `maxmemory-policy`

### Redis CLI Commands for Debugging

```bash
# Check if Redis is responsive
redis-cli PING

# Get server statistics
redis-cli INFO

# Check memory usage
redis-cli INFO memory

# List all keys
redis-cli KEYS *

# Check the type of a key
redis-cli TYPE key_name

# Check the length of a list (queue)
redis-cli LLEN queue_name

# Monitor Redis commands in real-time
redis-cli MONITOR

# Check client connections
redis-cli CLIENT LIST
```
