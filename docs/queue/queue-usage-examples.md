# Queue System Usage Examples

This document provides examples of how to use the queue system in the Quotation Pro application.

## Basic Queue Usage

### Using QueueService

The `QueueService` provides a Laravel-like interface for working with queues:

```php
// Get the QueueService instance
$queueService = Yii::$app->queueService;

// Push a job to the queue
$queueService->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));

// Push a job with a delay
$queueService->delay(60)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Push a job to a specific queue
$queueService->onQueue('emails')->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));

// Push a job to be executed at a specific time
$tomorrow = new \DateTime('tomorrow');
$queueService->later($tomorrow)->push(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Execute a job synchronously
$queueService->sync(new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'welcome-email',
    'params' => ['user' => $user],
    'subject' => 'Welcome to Quotation Pro',
]));

// Execute a callable synchronously
$queueService->sync(function($data) {
    // Process data synchronously
    return processData($data);
}, ['key' => 'value']);

// Execute a queue-aware job synchronously (a mock queue will be provided)
$queueService->sync(new \common\jobs\QueueAwareJob([
    'data' => 'some data',
]));

// Push a closure to the queue
$queueService->push(function ($data) {
    Yii::info('Processing data: ' . json_encode($data));
    return true;
}, ['key' => 'value']);

// Push a queue-aware closure to the queue
$queueService->push(function ($data, $queue) {
    // Process data
    $result = processData($data);

    // Push a follow-up job
    $queue->push(new \common\jobs\SendUserEmailJob([
        'userId' => $data['userId'],
        'template' => 'follow-up-email',
        'params' => ['result' => $result],
        'subject' => 'Your results are ready',
    ]));

    return true;
}, ['userId' => $user->id, 'data' => 'some data']);

// Push a job with a delay (alternative syntax)
$queueService->laterPush(60, new \common\jobs\SendUserEmailJob([
    'userId' => $user->id,
    'template' => 'reminder-email',
    'params' => ['user' => $user],
    'subject' => 'Reminder: Complete Your Profile',
]));

// Using GenericEmailJob for flexible email sending
$queueService->push(new \common\jobs\GenericEmailJob([
    'to' => $user->email,
    'subject' => 'Your Account Summary',
    'view' => ['html' => 'account-summary'],
    'params' => [
        'user' => $user,
        'stats' => $userStats,
        'recentActivity' => $recentActivity,
    ],
    'options' => [
        'cc' => '<EMAIL>',
        'bcc' => ['<EMAIL>', '<EMAIL>'],
        'attachments' => [
            '/path/to/summary.pdf',
            ['path' => '/path/to/terms.pdf', 'options' => ['as' => 'Terms_and_Conditions.pdf']],
        ],
    ],
    'useSmtp' => true,
]));
```

### Using EmailService with Queue

The `EmailService` has been updated to support queueing emails:

```php
// Create an EmailService instance
$emailService = new \common\services\EmailService();

// Queue an email to be sent
$emailService->queue()->sendUserMail(
    $user,
    'welcome-email',
    ['user' => $user],
    'Welcome to Quotation Pro'
);

// Queue an email with a delay
$emailService->queue()->delay(60)->sendUserMail(
    $user,
    'reminder-email',
    ['user' => $user],
    'Reminder: Complete Your Profile'
);

// Queue an email to be sent at a specific time
$tomorrow = new \DateTime('tomorrow');
$emailService->queue()->later($tomorrow)->sendUserMail(
    $user,
    'reminder-email',
    ['user' => $user],
    'Reminder: Complete Your Profile'
);

// Queue an admin email
$emailService->queue()->sendAdminMail(
    'notifications/new-user',
    ['user' => $user],
    'New User Registration'
);

// Queue an urgent notification
$emailService->queue()->sendUrgentNotificationToAdmin(
    'URGENT: System Alert',
    'The system has detected an issue that requires immediate attention.',
    ['timestamp' => time(), 'severity' => 'high']
);

// Using sendGenericEmail for flexible email sending
$emailService->sendGenericEmail(
    '<EMAIL>',
    'Your Account Summary',
    ['html' => 'account-summary'],
    [
        'user' => $user,
        'stats' => $userStats,
        'recentActivity' => $recentActivity,
    ],
    [] // No additional options
);

// Using sendGenericEmail with CC, BCC, and attachments
$emailService->sendGenericEmail(
    $user->email,
    'Your Monthly Invoice',
    ['html' => 'invoice-email'],
    [
        'user' => $user,
        'invoice' => $invoice,
        'items' => $invoiceItems,
    ],
    [
        'cc' => '<EMAIL>',
        'bcc' => ['<EMAIL>', '<EMAIL>'],
        'attachments' => [
            '/path/to/invoice.pdf',
            ['path' => '/path/to/terms.pdf', 'options' => ['as' => 'Terms_and_Conditions.pdf']],
        ],
    ]
);

// Queue a generic email
$emailService->queue()->sendGenericEmail(
    $user->email,
    'Your Account Summary',
    ['html' => 'account-summary'],
    [
        'user' => $user,
        'stats' => $userStats,
        'recentActivity' => $recentActivity,
    ],
    [] // No additional options
);

// Queue a generic email with delay
$emailService->queue()->delay(300)->sendGenericEmail(
    $user->email,
    'Follow-up: Your Recent Purchase',
    ['html' => 'follow-up-email'],
    [
        'user' => $user,
        'purchase' => $purchase,
        'relatedProducts' => $relatedProducts,
    ],
    [
        'from' => ['<EMAIL>' => 'Sales Team'],
        'replyTo' => ['<EMAIL>' => 'Customer Support'],
    ]
);
```

### Using EmailClient with Queue

The `EmailClient` also supports queueing emails:

```php
// Get the EmailClient instance
$emailClient = \common\services\EmailClient::getInstance();

// Queue an urgent notification
$emailClient->queue()->sendUrgentNotificationToAdmin(
    'URGENT: System Alert',
    'The system has detected an issue that requires immediate attention.',
    ['timestamp' => time(), 'severity' => 'high']
);

// Queue an urgent notification with a delay
$emailClient->queue()->delay(60)->sendUrgentNotificationToAdmin(
    'URGENT: System Alert',
    'The system has detected an issue that requires immediate attention.',
    ['timestamp' => time(), 'severity' => 'high']
);
```

## Creating Custom Jobs

You can create custom jobs by extending the `\common\jobs\Job` class:

```php
namespace common\jobs;

use common\models\User;
use Yii;
use yii\queue\Queue;

class GenerateReportJob extends Job
{
    /**
     * @var int The user ID to generate the report for
     */
    public $userId;

    /**
     * @var string The report type
     */
    public $reportType;

    /**
     * @var array Additional parameters for the report
     */
    public $params = [];

    /**
     * Constructor
     *
     * @param array $config The configuration array
     */
    public function __construct($config = [])
    {
        // Set default values
        $this->tries = 3;
        $this->retryAfter = 300; // 5 minutes
        $this->timeout = 120;
        $this->queue = 'reports';

        parent::__construct($config);
    }

    /**
     * Handles the job
     *
     * @param Queue|null $queue The queue instance (optional)
     * @return bool Whether the report was generated successfully
     */
    public function handle($queue = null)
    {
        $user = User::findOne($this->userId);

        if (!$user) {
            Yii::error("User not found: {$this->userId}");
            return false;
        }

        // Generate the report
        $reportService = new \common\services\ReportService();
        $report = $reportService->generateReport($user, $this->reportType, $this->params);

        // If we have a queue and the report was generated successfully, push a notification job
        if ($queue !== null && $report) {
            $queue->push(new SendEmailJob([
                'userId' => $this->userId,
                'template' => 'report-ready',
                'params' => ['reportType' => $this->reportType, 'reportUrl' => $report->url],
                'subject' => 'Your ' . $this->reportType . ' report is ready',
            ]));
        }

        return (bool)$report;
    }

    /**
     * Called when the job has failed
     *
     * @param \Exception $exception The exception that caused the job to fail
     * @return void
     */
    public function failed($exception)
    {
        Yii::error("Failed to generate report for user {$this->userId}: " . $exception->getMessage());

        // Notify the admin
        $emailClient = \common\services\EmailClient::getInstance();
        $emailClient->sendUrgentNotificationToAdmin(
            'Report Generation Failed',
            "Failed to generate {$this->reportType} report for user {$this->userId}",
            [
                'userId' => $this->userId,
                'reportType' => $this->reportType,
                'error' => $exception->getMessage(),
            ]
        );
    }
}
```

## Running Queue Workers

To process jobs in the queue, you need to run a queue worker. You can use the provided console commands:

```bash
# Start a worker in daemon mode (continuously listens for new jobs)
php yii queue/listen

# Start a worker for a specific queue in daemon mode
php yii queue/listen --queue=emails

# Process current jobs and then exit
php yii queue/run

# Process a limited number of jobs and then exit
php yii queue/run --limit=10

# Process jobs with a timeout between checks
php yii queue/listen --timeout=5

# Clear the queue
php yii queue/clear

# Show queue information
php yii queue/info
```

## Queue Configuration

The queue system is configured in the environment-specific configuration files:

- `common/config/apps-settings/quotation-local/main-queue.php`: Uses the DB driver
- `common/config/apps-settings/quotation-dev/main-queue.php`: Uses the Redis driver
- `common/config/apps-settings/quotation-prod/main-queue.php`: Uses the Redis driver

You can customize the queue configuration by editing these files.

## Best Practices

1. **Keep Jobs Small**: Jobs should be focused on a single task
2. **Make Jobs Idempotent**: Jobs should be safe to run multiple times
3. **Handle Failures**: Implement proper error handling in jobs
4. **Monitor Queue Size**: Keep an eye on queue size to prevent backlog
5. **Use Appropriate Driver**: Choose the right driver for your environment
6. **Set Appropriate Timeouts**: Configure timeouts based on job complexity
7. **Implement Logging**: Log job execution for debugging and monitoring
