# Simplified Queue Approach

## Overview

The queue system has been simplified to use a single queue channel named `default` for all jobs. This approach simplifies configuration, monitoring, and maintenance while still providing all the benefits of asynchronous processing.

## Configuration

### Base Job Class

All jobs use the `default` queue by default, as configured in the base Job class:

```php
/**
 * @var string The queue the job should be sent to
 */
public $queue = 'default';
```

### QueueService

The QueueService is configured to use the `default` queue as the default queue:

```php
/**
 * @var string The default queue to use
 */
public $defaultQueue = 'default';
```

## Supervisor Configuration

For production environments using Redis, a single supervisor configuration file is used:

```ini
[program:quotation-prod-queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/quotation-prod/yii queue/listen --verbose=1 --color=0 --queue=default
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/quotation-prod/runtime/logs/queue-default-worker.log
stopwaitsecs=60
```

## Cron Job Configuration

For local environments using the DB driver, a single cron job is used:

```crontab
# Run queue/run command every minute for the default queue (DB driver)
# The --limit parameter ensures the command exits after processing a certain number of jobs
* * * * * php /path/to/quotation-local/yii queue/run --limit=100 --verbose=1 --queue=default > /path/to/quotation-local/runtime/logs/queue-default-run.log 2>&1
```

## Benefits of the Simplified Approach

1. **Simplified Configuration**: Only one queue to configure and monitor
2. **Reduced Resource Usage**: Fewer queue workers means less server resources
3. **Easier Maintenance**: Simpler to troubleshoot and maintain
4. **Consistent Processing**: All jobs are processed by the same workers

## Future Expansion

If needed in the future, additional queues can be added for specific types of jobs:

1. **Urgent Queue**: For high-priority jobs that need immediate processing
2. **Default Queue**: For general-purpose jobs that aren't email-related

However, for most applications, a single queue is sufficient and provides the best balance of simplicity and functionality.
