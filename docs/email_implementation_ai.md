# Email Implementation Documentation

> **Note:** This document provides detailed implementation details for the email system in the Quotation Pro application. For a high-level overview and architecture information, please refer to the main [email_system_documentation.md](email_system_documentation.md) file.

## Overview

This document covers the email architecture, triggers, templates, configuration, and best practices for working with the email system in great detail, focusing on implementation specifics.

## Table of Contents

1. [Architecture](#architecture)
2. [Email Service](#email-service)
3. [Email Templates](#email-templates)
4. [Email Triggers](#email-triggers)
5. [SMTP Configuration](#smtp-configuration)
6. [Shared Components](#shared-components)
7. [Email Types](#email-types)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Architecture

The email system in Quotation Pro follows a centralized architecture with the following key components:

1. **EmailService**: A singleton service that manages all email operations
2. **Email Templates**: PHP view files with HTML markup stored in the `common/mail` directory
3. **Shared Components**: Reusable elements like headers, footers, and styles
4. **SMTP Configuration**: Multiple SMTP providers with load balancing
5. **Trigger Points**: Various points in the application that initiate email sending

## Email Service

The `EmailService` class (`common/services/EmailService.php`) is the central component responsible for handling all email functionality in the application. It is implemented as a singleton to ensure consistent email handling across the application.

### Key Features

- **Singleton Pattern**: Ensures a single instance is used throughout the application
- **SMTP Load Balancing**: Manages multiple SMTP configurations with automatic rotation
- **Centralized Error Handling**: Consistent logging and error management
- **Template Rendering**: Handles view composition and variable substitution
- **Email Tracking**: Maintains counters for sent emails

### Core Methods

```php
// Get the singleton instance
$emailService = EmailService::getInstance();

// Send email to a user
public function sendUserMail(
    User   $user,                // User model
    string $template,            // Template path
    array  $params,              // Template parameters
    string $subjectPrefix        // Subject prefix
): bool
{
    try {
        $smtpMailer = $this->getNextSmtpMailer();
    } catch (\Exception $e) {
        Yii::error("Failed to get SMTP mailer: " . $e->getMessage());
        return false;
    }
    return $this->sendMail(
        $smtpMailer,
        $template,
        $params,
        $user->email,
        $subjectPrefix
    );
}

// Send email to admin
public function sendAdminMail(
    string $template,            // Template path
    array  $params,              // Template parameters
    string $subjectPrefix        // Subject prefix
): bool
{
    return $this->sendMail(
        $this->mailer,
        $template,
        $params,
        Yii::$app->params['adminEmail'],
        $subjectPrefix
    );
}

// Core mail sending method
private function sendMail(
    MailerInterface $mailer,
    string          $template,
    array           $params,
    string|array    $toEmail,
    string          $subjectPrefix,
    string|array    $ccEmail = null
): bool
{
    try {
        $date = $this->getLocalizedDate();
        $message = $mailer->compose(['html' => $template], $params)
            ->setFrom($this->getSender())
            ->setTo($toEmail)
            ->setSubject("$subjectPrefix - {$date} | " . Yii::$app->name);

        if ($ccEmail) {
            $message->setBcc($ccEmail);
        }

        return $message->send();
    } catch (\Exception $e) {
        Yii::error("Email failed to $toEmail: " . $e->getMessage());
        return false;
    }
}
```

### SMTP Rotation Logic

The service implements a rotation mechanism to distribute emails across multiple SMTP providers:

```php
private function getNextSmtpMailer(): MailerInterface
{
    $sentEmailsCounter = Yii::$app->cache->get('sentEmailsCounter');
    if (!$sentEmailsCounter) {
        $sentEmailsCounter = 0;
    }
    $mailConfigIndex = (int)($sentEmailsCounter / self::EMAIL_LIMIT_PER_ACCOUNT);

    if ($mailConfigIndex >= count($this->smtpMailers)) {
        throw new \Exception("All SMTP services are exhausted! \n sent mail count is :: " . $sentEmailsCounter . " mailConfigIndex :: " . $mailConfigIndex);
    }

    // Increment counter with 24-hour expiration
    Yii::$app->cache->set('sentEmailsCounter', $sentEmailsCounter + 1, 86400); // 86400 seconds = 24 hours

    return $this->smtpMailers[$mailConfigIndex];
}
```

This allows the application to send up to 300 emails per SMTP provider per day, automatically switching to the next provider when the limit is reached. The counter is stored in the application cache with a 24-hour expiration.

## Email Templates

Email templates are stored in the `common/mail` directory and use PHP view files with HTML markup. Each template follows a consistent structure while providing content specific to the email type.

### Directory Structure

```
common/mail/
├── _footer.php                  # Shared footer component
├── _header.php                  # Shared header component
├── _styles.php                  # Shared CSS styles
├── layouts/                     # Layout templates
│   ├── html.php                 # HTML layout
│   └── text.php                 # Plain text layout
├── subscription/                # Subscription-related templates
│   ├── _header.php              # Subscription-specific header
│   ├── limit-reached-html.php   # Usage limit reached notification
│   ├── payment-failed-html.php  # Payment failure notification
│   ├── payment-success-html.php # Payment success notification
│   ├── subscription-receipt-html.php # Subscription receipt
│   └── subscription-removed-html.php # Subscription ended notification
├── notifications/               # Admin notification templates
│   ├── payment-failed.php       # Admin payment failure notification
│   ├── payment-notification.php # Admin payment success notification
│   └── unsubscribe-notification.php # Admin subscription removal notification
├── createUser-html.php          # New user account creation
├── emailVerify-html.php         # Email verification with OTP
├── quotation-html.php           # Quotation email to customer
├── receipt-html.php             # Receipt email to customer
└── invoice-html.php             # Invoice email to customer
```

### Template Structure

Each template follows this general structure, with subscription-related templates using a specialized header:

```php
<?php
/* @var $this yii\web\View */
/* @var $user common\models\User */
/* @var $otherParams ... */

// Template-specific variable preparation
$todayDate = date("d.m.Y");
?>
<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->render('@common/mail/_styles') ?>
</head>
<body style="background: var(--background-color);">
<div class="email-container">
    <?php if (strpos(__FILE__, 'subscription/') !== false): ?>
        <?= $this->render('@common/mail/subscription/_header', ['todayDate' => $todayDate]) ?>
    <?php else: ?>
        <?= $this->render('@common/mail/_header', ['business' => $business ?? null, 'todayDate' => $todayDate]) ?>
    <?php endif; ?>

    <div class="content">
        <h2 class="greeting">Hello <?= $user->name ?>,</h2>

        <!-- Template-specific content -->

    </div>

    <?= $this->render('@common/mail/_footer', ['user' => $user]) ?>
</div>
</body>
</html>
```

### Subscription Templates

Subscription-related templates follow a consistent pattern with alert sections for status messages:

```php
<div class="alert alert-success"> <!-- or alert-warning, alert-danger -->
    <p><strong>Payment Successful!</strong></p>
    <p>Your payment for the <strong><?= $plan->name ?></strong> subscription has been processed successfully.</p>
</div>

<div class="message">
    <p>We're excited to have you as a subscriber! Your subscription is now active, and you can enjoy all the
        premium features included in your plan.</p>
</div>

<div class="details">
    <h3>Subscription Details</h3>
    <table class="responsive-table">
        <tr>
            <td><strong>Plan:</strong></td>
            <td><?= $plan->name ?></td>
        </tr>
        <!-- More details... -->
    </table>
</div>
```

## Email Triggers

Emails are triggered from various points in the application. The main trigger points are:

### 1. Subscription Model

The `Subscription` model (`common/models/Subscription.php`) triggers emails when subscription status changes:

```php
public function afterSave($insert, $changedAttributes)
{
    if ($insert || isset($changedAttributes['isActive']) || isset($changedAttributes['planId'])) {
        if ($this->isActive) {
            $this->business->subscribeToPlan($this->plan);
            if ($this->plan->type === PlanType::BASE) {
                $this->user->subscribeToPlan($this->planId);
                StripeHelper::checkPastDueSubscriptions($this->business, $this->plan, $this->providerSubscriptionId);
            }
            $this->sendPaymentNotificationEmail();
        } else {
            $this->business->unsubscribeToPlan($this->plan);
            if ($this->plan->type === PlanType::BASE) {
                $this->user->unSubscribeToPlan($this->planId);
            }
            if ($this->plan->type === PlanType::MULTI_USER){
                $this->business->handleMultiUserPlanRemoval($this);
            }

            if ($this->status !== "incomplete") {
                $this->sendUnsubscribeNotificationEmail();
            }
        }
    }
    parent::afterSave($insert, $changedAttributes);
}

/**
 * Sends payment notification email to admin
 */
public function sendPaymentNotificationEmail()
{
    $emailService = EmailService::getInstance();
    $emailService->sendPaymentSuccessEmail($this->user, $this);
    $emailService->sendSubscriptionReceiptEmail($this->user, $this);

    $this->user->sendNotification("You have successfully subscribed to {$this->plan->name}! For any issues/queries please contact to our support from contact us screen in the app!");
}

public function sendUnsubscribeNotificationEmail()
{
    $emailService = EmailService::getInstance();
    $emailService->sendSubscriptionRemovedEmail($this->user, $this);
}
```

### 2. Payment Processors

Payment processors like `StripeHelper` and `RazorpayHelper` trigger emails when processing payments or handling webhooks:

```php
// In StripeHelper::handleWebhook() for failed payments
case 'payment_intent.payment_failed':
    $paymentIntent = $event->data->object;
    $paymentIntentId = $paymentIntent->id;
    $failureCode = $paymentIntent->last_payment_error ? $paymentIntent->last_payment_error->code : '';
    $failureMessage = $paymentIntent->last_payment_error ? $paymentIntent->last_payment_error->message : '';
    StripePaymentIntent::sendPaymentFailedNotificationEmail($paymentIntentId, $failureCode, $failureMessage);
    break;

// In StripeHelper::createPaymentIntent()
$isSent = Subscription::SendPaymentIntentEmailNotification($user, $plan, ProviderType::STRIPE);
```

### 3. LimitChecker Component

The `LimitChecker` component (`common/components/LimitChecker.php`) triggers emails when users reach usage limits:

```php
public function checkFinalLimit(User $user, $limitType, $currentCount)
{
    $forceSubscriptionCount = Yii::$app->params['FORCE_SUBSCRIPTION_COUNT'] ?? 40;

    // If user is already a pro user, they haven't reached the limit
    if ($user->isProUser) {
        return false;
    }

    // Check if user has reached the force subscription count
    if ($currentCount >= $forceSubscriptionCount) {
        // Send email notification about reaching the limit
        $this->sendLimitReachedEmail($user, $limitType);
        return true;
    }

    return false;
}

protected function sendLimitReachedEmail(User $user, $limitType)
{
    $emailService = EmailService::getInstance();
    return $emailService->sendFinalLimitReachedEmail($user, $limitType);
}
```

### 4. User Model

The `User` model triggers emails for account-related actions:

```php
// Send OTP verification email
public function sendOTPEmail()
{
    $emailService = EmailService::getInstance();

    try {
        $send_mail = $emailService->sendUserMail(
            $this,
            'emailVerify-html',
            ['user' => $this],
            'OTP Verification'
        );
    } catch (\Exception $e) {
        Yii::error($e);
        $send_mail = '1';
    }
    return $send_mail;
}
```

### 5. Document Models

Document models like `Quotation` and `Receipt` trigger emails when sending documents to customers:

```php
// In Quotation::sendMailToCustomer()
public function sendMailToCustomer()
{
    if ($this->getPdfFilePath() == null) {
        return false;
    }

    $composeMail = EmailService::getSmtpMailer()
        ->compose(
            ['html' => 'quotation-html'],
            ['quotation' => $this, 'customer' => $this->customer]
        )
        ->attach($this->getPdfFilePath())
        ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']]);

    $composeMail
        ->setBcc(Yii::$app->params['supportEmail']);

    $isSent = $composeMail
        ->setTo($this->customer->email)
        ->setSubject('You have received new quotation from ' . Yii::$app->name)
        ->send();

    if ($isSent !== true) {
        Yii::error($composeMail->getErrors());
    }

    return $isSent;
}
```

### 6. Business Model

The `Business` model triggers usage limit notification emails:

```php
public function checkAndSendUsageLimitNotification()
{
    $usageCount = $this->businessStats->totalUsageCount;
    $usageLimitThreshold = env(Key::usageLimitThreshold);

    // Skip if user is premium
    if ($this->isPremium) {
        return;
    }

    // Performance optimization: Early exit if usage count is below threshold
    // This avoids expensive array lookups and env() calls for most users
    if ($usageCount <= $usageLimitThreshold) {
        return;
    }

    // Only load these expensive configurations if we passed the threshold check
    $forceLimit = env(Key::FORCE_SUBSCRIPTION_COUNT);
    $usageLimitNotificationSlots = env(Key::usageLimitNotificationSlots);

    // Check if notification should be sent:
    // 1. When usage count matches any of the notification slots
    // 2. When usage count is above force limit
    $shouldSendNotification = in_array($usageCount, $usageLimitNotificationSlots) || $usageCount > $forceLimit;

    if (!$shouldSendNotification) {
        return;
    }

    // Set the timezone to IST (Indian Standard Time)
    date_default_timezone_set('Asia/Kolkata');
    $todayDate = date("d.m.Y");

    $subject = 'Usage limit Reached!';
    if ($usageCount >= $forceLimit) {
        $subject = "Final Usage limit reached!";
    }

    // Send notification email
    $composeMail = Yii::$app->mailer->compose(...)
}
```

### Specialized Email Methods

The `EmailService` provides specialized methods for different types of emails:

```php
// Send payment failed notification
public function sendPaymentFailedMails(
    User                             $user,
    Subscription|StripePaymentIntent $subscription,
    string                           $failureCode,
    string                           $failureMessage
): bool
{
    $params = [
        'user' => $user,
        'business' => $user->business,
        'subscription' => $subscription,
        'plan' => $subscription->plan,
        'failureCode' => $failureCode,
        'failureMessage' => $failureMessage,
    ];
    $adminSent = $this->sendAdminMail(
        'notifications/payment-failed',
        $params,
        'Payment Failed'
    );

    $userSent = $this->sendUserMail(
        $user,
        'subscription/payment-failed-html',
        $params,
        'Payment Failed'
    );

    return $adminSent && $userSent;
}

// Send payment success notification
public function sendPaymentSuccessEmail(User $user, Subscription $subscription): bool
{
    $params = [
        'user' => $user,
        'business' => $user->business,
        'subscription' => $subscription,
        'plan' => $subscription->plan
    ];

    $userSent = $this->sendUserMail(
        $user,
        'subscription/payment-success-html',
        $params,
        'Payment Successful'
    );

    $adminSent = $this->sendAdminMail(
        'notifications/payment-notification',
        $params,
        'New Subscription Payment'
    );

    return $userSent && $adminSent;
}

// Send subscription removed notification
public function sendSubscriptionRemovedEmail(User $user, Subscription $subscription): bool
{
    $params = [
        'user' => $user,
        'business' => $user->business,
        'subscription' => $subscription,
        'plan' => $subscription->plan
    ];

    $userSent = $this->sendUserMail(
        $user,
        'subscription/subscription-removed-html',
        $params,
        'Subscription Ended'
    );

    $adminSent = $this->sendAdminMail(
        'notifications/unsubscribe-notification',
        $params,
        'Subscription Removed'
    );

    return $userSent && $adminSent;
}

// Send usage limit reached notification
public function sendFinalLimitReachedEmail(User $user, string $limitType): bool
{
    return $this->sendUserMail(
        $user,
        'subscription/limit-reached-html',
        [
            'user' => $user,
            'business' => $user->business,
            'limitType' => $limitType,
            'recommendedPlans' => $this->getRecommendedPlans($limitType)
        ],
        'Usage Limit Reached'
    );
}

// Send subscription receipt
public function sendSubscriptionReceiptEmail(User $user, Subscription $subscription): bool
{
    return $this->sendUserMail(
        $user,
        'subscription/subscription-receipt-html',
        [
            'user' => $user,
            'business' => $user->business,
            'subscription' => $subscription,
            'plan' => $subscription->plan,
            'provider' => $subscription->provider
        ],
        'Subscription Receipt'
    );
}
```

## SMTP Configuration

The application uses Brevo (formerly Sendinblue) as the SMTP provider with multiple accounts configured for load balancing.

### Configuration in main.php

```php
'mailer' => [
    'class' => \yii\symfonymailer\Mailer::class,
    'transport' => [
        'dsn' => 'native://default',
    ],
    'viewPath' => '@common/mail',
    'useFileTransport' => false,
],
'smtpMailer' => [
    'class' => \yii\symfonymailer\Mailer::class,
    'transport' => [
        'dsn' => 'smtp://<EMAIL>:<EMAIL>:587',
    ],
    'viewPath' => '@common/mail',
    'useFileTransport' => false,
],
```

### Multiple SMTP Configuration

The `EmailService` initializes multiple SMTP mailers based on configuration in the `params.php` file:

```php
private function initializeSmtpMailers(): void
{
    // Load SMTP configurations from params
    $smtpConfigs = Yii::$app->params['smtpMailers'] ?? [];

    foreach ($smtpConfigs as $config) {
        $this->smtpMailers[] = Yii::createObject($config);
    }
}
```

The SMTP configurations are defined in `common/config/params.php`:

```php
'smtpMailers' => [
    [
        'class' => \yii\symfonymailer\Mailer::class,
        'transport' => [
            'dsn' => 'smtp://<EMAIL>:<EMAIL>:587',
        ],
        'viewPath' => '@common/mail',
        'useFileTransport' => false,
    ],
    // Additional configurations...
],
```

## Shared Components

The email system uses several shared components to maintain consistency across all emails:

### 1. Styles (`_styles.php`)

Contains all CSS styles used across email templates, including:
- Base styles (typography, colors)
- Layout components (container, header, footer)
- UI elements (buttons, alerts, tables)
- Responsive design rules
- Dark mode support

```php
<style type="text/css">
:root {
    --primary-color: #2B5896;
    --secondary-color: #25D366;
    --background-color: #f6f6f6;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Base Styles */
body {
    font-family: 'Calibri', Arial, Helvetica, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.email-container {
    max-width: 650px;
    margin: 0 auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* More styles... */
</style>
```

### 2. Header (`_header.php`)

Standard header used in general emails:

```php
<div class="header">
    <?php if (isset($business) && $business && $business->image): ?>
        <img src="<?= $business->image ?>" alt="<?= $business->name ?> Logo" style="max-height: 60px;">
    <?php endif; ?>
    <div class="date"><?= $todayDate ?></div>
</div>
```

### 3. Subscription Header (`subscription/_header.php`)

Specialized header for subscription emails that doesn't include business images:

```php
<div class="header">
    <div class="app-name"><?= APP_NAME ?></div>
    <div class="date"><?= $todayDate ?></div>
</div>
```

### 4. Footer (`_footer.php`)

Standard footer with support contact information and WhatsApp integration:

```php
<div class="message">
    <p>If you have any questions or need assistance, please contact our support team at
        <a href="mailto:<?= Yii::$app->params['supportEmail'] ?>"><?= Yii::$app->params['supportEmail'] ?></a>
        <?php if (isset($user)): ?>
        or <a href="<?= $whatsAppLink ?>" class="whatsapp-button">
            <img src="https://web.whatsapp.com/img/favicon/1x/favicon.png" alt="WhatsApp" class="whatsapp-logo">
            Chat with Support
        </a>
        <?php endif; ?>
    </p>
</div>

<div class="footer">
    <p>This email was sent automatically, please do not reply to this email address.</p>
    <p>Thank you,<br/><?= APP_NAME ?> &copy; <?= date('Y') ?></p>
    <?php if (isset($user)): ?>
    <p><small>Support PIN: <?= $user->id ?> (Required when contacting customer support)</small></p>
    <?php endif; ?>
</div>
```

## Email Types

The application sends several types of emails, each with its own template and purpose:

### 1. Subscription-Related Emails

#### Subscription Receipt Email
- **Template**: `subscription/subscription-receipt-html.php`
- **Trigger**: When a user subscribes via any payment provider
- **Purpose**: Provide subscription details and receipt
- **Key Information**:
  - Receipt number
  - Subscription details (plan, period)
  - Customer information
  - Payment method
  - Transaction ID
  - Formatted amount with currency symbol
  - IP address of the user during payment
  - Payment receipt link (when available)
  - Transaction details for dispute resolution

#### Payment Success Email
- **Template**: `subscription/payment-success-html.php`
- **Trigger**: After successful subscription payments
- **Purpose**: Confirm payment and provide subscription details
- **Key Information**:
  - Subscription details
  - Plan features
  - App rating request
  - Formatted amount with currency symbol
  - Payment receipt link (when available)
  - Transaction ID and payment details

#### Payment Failed Email
- **Template**: `subscription/payment-failed-html.php`
- **Trigger**: When payment attempts fail
- **Purpose**: Notify about payment failure and provide retry options
- **Key Information**:
  - Error details
  - Plan information
  - Retry button

#### Subscription Removed Email
- **Template**: `subscription/subscription-removed-html.php`
- **Trigger**: When a subscription is cancelled or expires
- **Purpose**: Notify about subscription end and encourage renewal
- **Key Information**:
  - Subscription end date
  - Renewal benefits
  - Recommended plans

#### Limit Reached Email
- **Template**: `subscription/limit-reached-html.php`
- **Trigger**: When a user reaches the maximum usage limit
- **Purpose**: Prompt users to subscribe to continue using features
- **Key Information**:
  - Limit type reached
  - Subscription benefits
  - Recommended plans

### 2. User Account Emails

#### Email Verification
- **Template**: `emailVerify-html.php`
- **Trigger**: When a user registers or changes email
- **Purpose**: Verify user's email address
- **Key Information**:
  - OTP code
  - Expiration time

#### User Creation
- **Template**: `createUser-html.php`
- **Trigger**: When an admin creates a new user
- **Purpose**: Provide login credentials
- **Key Information**:
  - Temporary password
  - Login instructions

### 3. Document Emails

#### Quotation Email
- **Template**: `quotation-html.php`
- **Trigger**: When sending a quotation to a customer
- **Purpose**: Deliver quotation document
- **Key Information**:
  - Quotation details
  - PDF attachment
  - Business information

#### Receipt Email
- **Template**: `receipt-html.php`
- **Trigger**: When sending a receipt to a customer
- **Purpose**: Deliver receipt document
- **Key Information**:
  - Receipt details
  - PDF attachment
  - Business information

### 4. Admin Notification Emails

#### Payment Notification
- **Template**: `notifications/payment-notification.php`
- **Trigger**: When a payment is successful
- **Purpose**: Notify admin about new subscriptions
- **Key Information**:
  - User details
  - Subscription details
  - Payment information
  - Formatted amount with currency symbol
  - IP address of the user during payment
  - Payment receipt link
  - Comprehensive transaction details for dispute resolution

#### Payment Failed Notification
- **Template**: `notifications/payment-failed.php`
- **Trigger**: When a payment fails
- **Purpose**: Notify admin about payment issues
- **Key Information**:
  - User details
  - Error information
  - Subscription details

#### Unsubscribe Notification
- **Template**: `notifications/unsubscribe-notification.php`
- **Trigger**: When a subscription is cancelled or expires
- **Purpose**: Notify admin about subscription changes
- **Key Information**:
  - User details
  - Subscription details
  - Cancellation reason (if available)

## Best Practices

When working with the email system, follow these best practices:

### 1. Use the EmailService

Always use the `EmailService` singleton to send emails:

```php
$emailService = EmailService::getInstance();

// For general emails
$emailService->sendUserMail($user, 'template-name', $params, 'Subject Prefix');

// For specialized emails, use the dedicated methods
$emailService->sendPaymentSuccessEmail($user, $subscription);
$emailService->sendSubscriptionReceiptEmail($user, $subscription);
$emailService->sendSubscriptionRemovedEmail($user, $subscription);
$emailService->sendFinalLimitReachedEmail($user, $limitType);
$emailService->sendWelcomeEmail($user);
```

### 2. Use Table-Based Layouts

Always use table-based layouts for email templates to ensure compatibility with all email clients:

```php
<table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <td align="center">
            <table class="email-container" border="0" cellpadding="0" cellspacing="0">
                <tr>
                    <td class="content-cell">
                        <!-- Content goes here -->
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
```

### 3. Use Standard Date Format

Always use the standard date format function for displaying dates:

```php
$formattedDate = formatStandardDate($subscription->endDate);
```

This ensures dates are displayed in the clear DD MMM, YYYY format (e.g., 16 Apr, 2025) that is unambiguous across different regions.

### 4. Handle Errors

Always wrap email sending in try-catch blocks and log errors:

```php
try {
    $emailService->sendUserMail($user, 'template-name', $params, 'Subject Prefix');
} catch (\Exception $e) {
    Yii::error("Failed to send email: " . $e->getMessage());
    // Consider implementing a fallback mechanism
    return false;
}
```

### 5. Use Shared Components

Leverage shared components for consistency:

```php
// In template files
<?= $this->render('@common/mail/_styles') ?>

// For subscription emails
<?= $this->render('@common/mail/subscription/_header') ?>

// For general emails
<?= $this->render('@common/mail/_header', ['business' => $business]) ?>

// Footer with support information
<?= $this->render('@common/mail/_footer', ['user' => $user]) ?>
```

### 6. Follow Template Structure

When creating new templates, follow the established structure:

1. Define template variables at the top
2. Include shared styles
3. Use the email-container wrapper
4. Include appropriate header (general or subscription-specific)
5. Use consistent content structure:
   - Alert sections for status messages
   - Message sections for explanatory text
   - Details sections for structured information
6. Include the shared footer
7. Use semantic HTML and CSS classes from _styles.php

### 5. Avoid Pricing Information

Don't include specific pricing in email templates since the application supports multi-currency and the actual price paid might not be known at the time of sending emails. Instead:

```php
// Instead of this:
<p>Amount: <?= $plan->price ?> <?= $plan->currency ?></p>

// Use this:
<p>Plan: <?= $plan->name ?> (<?= $plan->period ?>)</p>
```

### 6. Test Emails

Test emails across different clients and devices:

- Desktop: Outlook, Thunderbird
- Web: Gmail, Yahoo, Outlook.com
- Mobile: iOS Mail, Gmail app, Outlook app

Use the test controllers to send test emails during development:

```php
// In browser
/api/v1/mail-test/test-email
/api/v1/mail-test/subscription-ended
```

## Troubleshooting

### Common Issues

#### 1. Emails Not Sending

**Possible causes:**
- SMTP configuration issues
- Exceeded daily sending limits
- Invalid recipient email
- Exception in the email service

**Solutions:**
- Check SMTP credentials in params.php configuration
- Verify sent email counter in cache (`sentEmailsCounter`)
- Validate recipient email format
- Check logs for exceptions in EmailService

```php
// Reset the email counter if needed (in development)
Yii::$app->cache->delete('sentEmailsCounter');
```

#### 2. Template Rendering Issues

**Possible causes:**
- Missing template variables
- Template path errors
- Syntax errors in template
- Missing shared components

**Solutions:**
- Ensure all required variables are passed to the template
- Verify template path is correct (check for typos)
- Check template for PHP syntax errors
- Verify that shared components exist and are correctly referenced

```php
// Debug template variables
Yii::debug('Template params: ' . json_encode($params));
```

#### 3. Email Styling Problems

**Possible causes:**
- CSS compatibility issues with email clients
- Missing or incorrect style includes
- Responsive design issues
- Inline styles overriding shared styles

**Solutions:**
- Use email-safe CSS properties (avoid advanced CSS features)
- Ensure _styles.php is included at the top of the template
- Test with responsive design tools and multiple email clients
- Be careful with inline styles that might override shared styles

#### 4. SMTP Rotation Issues

**Possible causes:**
- Incorrect SMTP configuration in params.php
- Cache issues with the email counter
- All SMTP services exhausted

**Solutions:**
- Verify SMTP configurations in params.php
- Reset the email counter in cache
- Add more SMTP providers if needed

### Debugging

To debug email issues:

1. Enable file transport in development:
   ```php
   // In common/config/main-local.php or params.php
   'smtpMailers' => [
       [
           'class' => \yii\symfonymailer\Mailer::class,
           'transport' => [...],
           'viewPath' => '@common/mail',
           'useFileTransport' => true, // Set to true for development
       ],
   ],
   ```

2. Check application logs for detailed error messages:
   ```php
   // Add more detailed logging
   Yii::info("Sending email to: " . $user->email . " using template: " . $template);
   ```

3. Use the test controllers to send test emails:
   ```php
   // In browser
   /api/v1/mail-test/test-email
   /api/v1/mail-test/subscription-ended
   ```

4. Inspect generated email files in runtime/mail/ when using file transport

5. Monitor SMTP provider dashboard (Brevo/Sendinblue) for delivery issues and quota usage

6. Check for email sending limits in your SMTP provider:
   - Brevo/Sendinblue typically has daily sending limits
   - The application is configured to switch providers when limits are reached
