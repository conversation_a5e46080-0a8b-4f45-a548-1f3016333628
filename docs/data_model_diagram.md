# Quotation Pro Data Model Diagram

## Core Entity Relationships

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│    User       │     │ UserBusiness  │     │   Business    │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ id            │     │ id            │
│ email         │     │ userId        │◄────┤ ownerId       │
│ name          │     │ businessId    │     │ name          │
│ phoneNumber   │     │ isOwner       │     │ email         │
│ password_hash │     │ createdAt     │     │ phoneNumber   │
│ accessToken   │     │ updatedAt     │     │ addressLine1  │
│ status        │     └───────────────┘     │ taxNumber     │
│ isAdmin       │                           │ image         │
│ isDeleted     │                           │ signatureImg  │
│ createdAt     │                           │ headerImg     │
│ updatedAt     │                           │ footerImg     │
└───────────────┘                           │ isDeleted     │
                                            │ createdAt     │
                                            │ updatedAt     │
                                            └───────────────┘
                                                    ▲
                                                    │
                                                    │
┌───────────────┐     ┌───────────────┐     ┌──────┴────────┐
│   Customer    │     │  Quotation    │     │BusinessSettings│
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ id            │     │ id            │
│ businessId    │◄────┤ businessId    │     │ businessId    │
│ name          │     │ customerId    │     │ group         │
│ email         │     │ quotationNumber│    │ key           │
│ phoneNumber   │     │ subject       │     │ value         │
│ addressLine1  │     │ totalAmount   │     │ createdAt     │
│ isDeleted     │     │ status        │     │ updatedAt     │
│ createdAt     │     │ isPurchaseOrder│    └───────────────┘
│ updatedAt     │     │ isBudget      │
└───────────────┘     │ pdfFileName   │
        ▲             │ pdfFileUrl    │
        │             │ isDeleted     │
        │             │ createdAt     │
        │             │ updatedAt     │
┌───────────────┐     └───────────────┘
│   Invoice     │             │
├───────────────┤             │
│ id            │             │
│ businessId    │             │
│ customerId    │◄────────────┘
│ invoiceNumber │
│ totalAmount   │     ┌───────────────┐
│ paidAmount    │     │QuotationItems │
│ status        │     ├───────────────┤
│ isProformaInvoice│  │ id            │
│ isDeliveryNote │   │ quotationId   │◄─┐
│ pdfFileName   │     │ productId     │  │
│ pdfFileUrl    │     │ quantity      │  │
│ isDeleted     │     │ unitPrice     │  │
│ createdAt     │     │ discount      │  │
│ updatedAt     │     │ tax           │  │
└───────────────┘     │ totalAmount   │  │
        │             │ createdAt     │  │
        │             │ updatedAt     │  │
        │             └───────────────┘  │
        │                                │
        │             ┌───────────────┐  │
        │             │ InvoiceItems  │  │
        │             ├───────────────┤  │
        └────────────►│ id            │  │
                      │ invoiceId     │  │
                      │ productId     │  │
                      │ quantity      │  │
                      │ unitPrice     │  │
                      │ discount      │  │
                      │ tax           │  │
                      │ totalAmount   │  │
                      │ createdAt     │  │
                      │ updatedAt     │  │
                      └───────────────┘  │
                                         │
                      ┌───────────────┐  │
                      │   Product     │  │
                      ├───────────────┤  │
                      │ id            │◄─┘
                      │ businessId    │
                      │ name          │
                      │ description   │
                      │ unitPrice     │
                      │ categoryId    │
                      │ image         │
                      │ isDeleted     │
                      │ createdAt     │
                      │ updatedAt     │
                      └───────────────┘
```

## PDF Template System

```
┌───────────────┐     ┌───────────────┐
│   Template    │     │   Business    │
├───────────────┤     ├───────────────┤
│ id            │     │ id            │
│ type          │     │ quotationTemplateId │
│ slug          │     │ invoiceTemplateId │
│ dirName       │     │ piTemplateId  │
│ title         │     │ poTemplateId  │
│ previewImage  │     │ dnTemplateId  │
│ isHeaderFooterEnable│ receiptTemplateId │
│ marginSettings│     │ budgetTemplateId │
│ cssSettings   │     └───────────────┘
│ isActive      │             ▲
│ createdAt     │             │
│ updatedAt     │             │
└───────────────┘             │
        ▲                     │
        │                     │
        │                     │
┌───────────────┐     ┌───────────────┐
│   Quotation   │     │    Invoice    │
├───────────────┤     ├───────────────┤
│ id            │     │ id            │
│ templateId    │     │ templateId    │
└───────────────┘     └───────────────┘
```

## User Authentication and Authorization

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│     User      │     │  UserBusiness │     │   Business    │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ id            │     │ id            │
│ email         │     │ userId        │◄────┤ ownerId       │
│ accessToken   │     │ businessId    │     │ isMultiuser   │
│ status        │     │ isOwner       │     └───────────────┘
│ isAdmin       │     └───────────────┘
└───────────────┘
        ▲
        │
        │
┌───────────────┐
│   Identity    │
├───────────────┤
│ id            │
│ userId        │
│ accessToken   │
│ createdAt     │
│ updatedAt     │
└───────────────┘
```

## Business Settings Structure

```
┌───────────────┐     ┌───────────────┐
│   Business    │     │BusinessSettings│
├───────────────┤     ├───────────────┤
│ id            │     │ id            │
│ name          │     │ businessId    │
└───────────────┘     │ group         │
        ▲             │ key           │
        │             │ value         │
        └─────────────┤ createdAt     │
                      │ updatedAt     │
                      └───────────────┘
```

## Document Number Generation

```
┌───────────────┐
│   Business    │
├───────────────┤
│ id            │
│ quotationNumber│ ──┐
│ invoiceNumber │ ──┼─┐
│ piNumber      │ ──┼─┼─┐
│ poNumber      │ ──┼─┼─┼─┐
│ dnNumber      │ ──┼─┼─┼─┼─┐
│ receiptNumber │ ──┼─┼─┼─┼─┼─┐
│ budgetNumber  │ ──┘ │ │ │ │ │
└───────────────┘     │ │ │ │ │
                      │ │ │ │ │
┌───────────────┐     │ │ │ │ │
│   Quotation   │     │ │ │ │ │
├───────────────┤     │ │ │ │ │
│ id            │     │ │ │ │ │
│ businessId    │     │ │ │ │ │
│ quotationNumber│◄────┘ │ │ │ │
│ isPurchaseOrder│       │ │ │ │
│ isBudget      │       │ │ │ │
└───────────────┘       │ │ │ │
                        │ │ │ │
┌───────────────┐       │ │ │ │
│    Invoice    │       │ │ │ │
├───────────────┤       │ │ │ │
│ id            │       │ │ │ │
│ businessId    │       │ │ │ │
│ invoiceNumber │◄──────┘ │ │ │
│ isProformaInvoice│      │ │ │
│ isDeliveryNote │        │ │ │
└───────────────┘         │ │ │
                          │ │ │
┌───────────────┐         │ │ │
│ProformaInvoice│         │ │ │
├───────────────┤         │ │ │
│ id            │         │ │ │
│ businessId    │         │ │ │
│ piNumber      │◄────────┘ │ │
└───────────────┘           │ │
                            │ │
┌───────────────┐           │ │
│ PurchaseOrder │           │ │
├───────────────┤           │ │
│ id            │           │ │
│ businessId    │           │ │
│ poNumber      │◄──────────┘ │
└───────────────┘             │
                              │
┌───────────────┐             │
│ DeliveryNote  │             │
├───────────────┤             │
│ id            │             │
│ businessId    │             │
│ dnNumber      │◄────────────┘
└───────────────┘
```

## PDF Generation System

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Document Model│     │  PdfService   │     │ Template View │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ generatePdf() │     │ render()      │
│ businessId    │     │ getTemplate() │     │ getAssetUrl() │
│ number        │     │ publishAssets()│     │ registerCss() │
│ items         │     │ renderTemplate()│     │ registerJs()  │
│ pdfPath       │     │ configureMpdf()│     │               │
└───────────────┘     └───────────────┘     └───────────────┘
        │                     │                     │
        │                     │                     │
        │                     │                     │
```

## Subscription System

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Subscription  │     │     Plan      │     │   Payment     │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ id            │     │ id            │
│ userId        │     │ name          │     │ subscriptionId │
│ businessId    │     │ description    │     │ amount        │
│ planId        │────►│ price         │     │ currency      │
│ startDate     │     │ currency      │     │ provider      │
│ endDate       │     │ duration      │     │ providerTxnId │
│ status        │     │ durationType  │     │ status        │
│ provider      │     │ features      │     │ createdAt     │
│ providerTxnId │     │ limits        │     │ updatedAt     │
│ createdAt     │     │ isActive      │     └───────────────┘
│ updatedAt     │     │ createdAt     │             │
└───────────────┘     │ updatedAt     │             │
        │                └───────────────┘             │
        │                                                │
        └────────────────────────────────────────────►│
```

## Multi-Tenant Architecture

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ BaseController │     │ BusinessFilter │     │ ActiveRecord  │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ $business     │     │ beforeAction()│     │ find()        │
│ getBusiness() │     │ filterByBiz() │     │ findOne()     │
│ checkAccess() │     │               │     │ findAll()     │
│ beforeAction()│     │               │     │ findBySql()   │
└───────────────┘     └───────────────┘     └───────────────┘
```